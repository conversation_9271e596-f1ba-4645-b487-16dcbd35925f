.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--color-white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: var(--color-primary-dark);
  position: fixed; /* Make the header fixed */
  top: 0; /* Position it at the top */
  left: 0; /* Align it to the left */
  width: 100%; /* Make it full width */
  z-index: 1000; /* Ensure it stays above other content */
  height: 60px; /* Fixed height for the header */
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header-logo {
  width: 40px;
  height: 40px;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-primary-dark);
}

.header-right {
  position: relative;
  z-index: 1200; /* Higher z-index to ensure dropdown appears above other elements */
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.user-menu:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

.username {
  font-size: 0.875rem;
  color: var(--color-primary-dark);
  font-weight: 500;
}

.user-icon {
  width: 30px;
  height: 30px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 1200;
  width: 180px;
  overflow: visible;
  margin-top: 8px;
  /* Debug styles to force visibility */
  opacity: 1 !important;
  display: block !important;
  visibility: visible !important;
}

.dropdown-item {
  width: 100%;
  padding: 12px 16px;
  background-color: transparent;
  border: none;
  color: #333;
  font-size: 0.875rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: rgba(0, 130, 163, 0.1);
  color: #0082A3;
}

@media (max-width: 768px) {
  .header-left span {
    display: none;
  }

  .username {
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}