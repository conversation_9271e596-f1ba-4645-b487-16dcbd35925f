export interface RegisterPayload {
  first_name: string;
  last_name: string | null;
  email: string;
  phone_number: string | null;
  password: string;
  confirm_password: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  refresh_token?: string;
  expires_in: number;
  role_id: number;
  email: string;
  first_name: string;
  last_name?: string;
}

export interface ApiError {
  message: string;
  statusCode: number;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onPerPageChange: (perPage: number) => void;
  perPage: number;
  disabled?: boolean;
}