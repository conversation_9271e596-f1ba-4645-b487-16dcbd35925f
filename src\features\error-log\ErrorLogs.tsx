import React, { useEffect, useState, useRef } from 'react';
import { saveAs } from 'file-saver';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Pagination from '../../components/Pagination';
import Sidebar from '../../components/Sidebar';
import apiClient from '../../core/config/api';
import Header from '../../layouts/Header/components/Header';
import DeleteConfirmationModal from '../user/components/DeleteConfirmationModal';
import ErrorDetailsModal from './ErrorDetailsModal';
import { useSelector } from 'react-redux';
import { RootState } from '../../app/store';
import DropdownMenuPortal from '../email/components/DropdownMenuPortal';
import { DeleteIcon, ViewReportsIcon, DownloadIcon } from "../../components/ActionIcons";

export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
}

export interface RequestHistory {
  id: number;
  project_name: string | null;
  request_type: string;
  status: string;
  created_at: string;
}

export interface ErrorLog {
  id: number;
  user_id: number | null;
  request_history_id: number | null;
  request_origin: string;
  level: string;
  error_code: string | null;
  message: string;
  details: Record<string, unknown>;
  stack_trace: string | null;
  created_at: string;
  user: User | null;
  request_history: RequestHistory | null;
}

const ErrorLogs: React.FC = () => {
  const roleId = useSelector((state: RootState) => state.auth.roleId);
  const [errorLogs, setErrorLogs] = useState<ErrorLog[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<ErrorLog | null>(null);
  const [downloadingLogs, setDownloadingLogs] = useState<{ [key: string]: boolean }>({});
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [logToDelete, setLogToDelete] = useState<ErrorLog | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  const fetchTotalCount = async () => {
    try {
      const response = await apiClient.get("/error-logs/count");
      setTotalCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch error logs count:", error);
    }
  };

  const fetchErrorLogs = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: currentPage,
        page_size: dataPerPage,
      };

      // Handle search term based on role
      if (searchTerm) {
        if (roleId === 1) {
          // Admin searches by request_origin
          params.request_origin = searchTerm;
        } else {
          // Non-admin searches by project_name_contains
          params.project_name_contains = searchTerm;
        }
      }

      // Date filters
      if (startDate) {
        params.created_after = formatDateToISOString(startDate);
        if (!endDate) {
          params.created_before = formatDateToISOString(startDate, true);
        }
      }

      if (endDate) {
        params.created_before = formatDateToISOString(endDate, true);
        if (!startDate) {
          params.created_after = formatDateToISOString(endDate);
        }
      }

      // Level filter
      if (levelFilter !== 'all') {
        params.level = levelFilter;
      }

      const response = await apiClient.get('/error-logs', { params });
      setErrorLogs(response.data.data);
      setTotalPages(response.data.total_pages || Math.ceil(response.data.total / dataPerPage));
    } catch (error) {
      console.error("Error fetching error logs:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTimeout) clearTimeout(searchTimeout);

    const timeout = setTimeout(() => {
      fetchErrorLogs();
      fetchTotalCount();
    }, 500);
    setSearchTimeout(timeout);

    return () => {
      if (searchTimeout) clearTimeout(searchTimeout);
    };
  }, [searchTerm, currentPage, dataPerPage, startDate, endDate, levelFilter]);

  const getLevelBadgeClass = (level: string) => {
    switch (level.toLowerCase()) {
      case 'error': return 'badge bg-danger';
      case 'warning': return 'badge bg-warning text-dark';
      case 'info': return 'badge bg-info text-dark';
      case 'debug': return 'badge bg-secondary';
      default: return 'badge bg-primary';
    }
  };

  const formatDateToISOString = (date: Date | undefined, isEndOfDay: boolean = false) => {
    if (!date) return undefined;
    const d = new Date(date);
    if (isEndOfDay) {
      d.setHours(23, 59, 59, 999);
    } else {
      d.setHours(0, 0, 0, 0);
    }
    return d.toISOString().slice(0, 19).replace('T', ' ');
  };

  const handleViewDetails = (log: ErrorLog) => {
    setSelectedLog(log);
    setIsDetailsModalOpen(true);
  };

  const handleDownloadLog = async (log: ErrorLog) => {
    const stateKey = `log-${log.id}`;
    if (downloadingLogs[stateKey]) return;

    setDownloadingLogs(prev => ({ ...prev, [stateKey]: true }));

    try {
      const logData = {
        details: log.details,
        stack_trace: log.stack_trace,
        created_at: log.created_at
      };

      const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
      saveAs(blob, `log_details_${log.id}.json`);

      setTimeout(() => {
        setDownloadingLogs(prev => ({ ...prev, [stateKey]: false }));
      }, 1000);
    } catch (error) {
      console.error('Download failed:', error);
      setDownloadingLogs(prev => ({ ...prev, [stateKey]: false }));
    }
  };

  const openDeleteModal = (log: ErrorLog) => {
    setLogToDelete(log);
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setLogToDelete(null);
  };

  const handleDeleteConfirm = async () => {
    if (!logToDelete) return;

    setDeleteLoading(true);
    try {
      await apiClient.delete(`/error-logs/${logToDelete.id}`);

      // Save the deleted log ID for reference after the modal is closed
      const deletedLogId = logToDelete.id;

      // Update the UI by removing the deleted log
      setErrorLogs(prev => prev.filter(log => log.id !== deletedLogId));

      // Refresh count after deletion
      fetchTotalCount();

      // If the deleted log was selected in the details modal, close it too
      if (selectedLog && selectedLog.id === deletedLogId) {
        setIsDetailsModalOpen(false);
        setSelectedLog(null);
      }

      setDeleteLoading(false);
      return Promise.resolve();
    } catch (error) {
      console.error("Delete failed:", error);
      setDeleteLoading(false);
      return Promise.reject(error);
    }
  };

  const handlePerPageChange = (newPerPage: number) => {
    setDataPerPage(newPerPage);
    setCurrentPage(1);
  };

  // Handle clicks outside of dropdown to close it
  useEffect(() => {
    // We don't need this listener anymore as DropdownMenuPortal handles clicks outside
    return () => {
      // No cleanup needed
    };
  }, [openActionMenuId]);

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <h5 className="mb-0 text-white">
              <i className="fas fa-exclamation-triangle me-2"></i>Logs History{totalCount > 0 && <span>({totalCount})</span>}
            </h5>
          </div>
          <div className="card-body p-3">
            <div className="filters-container">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder={roleId !== 1 ? "Search by project name" : "Search by user"}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchTerm('')}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>
              <div className="filter-group right-filters">
                <div className="date-picker-container">
                  <DatePicker
                    selected={startDate}
                    onChange={(date: Date | null) => {
                      const newStart = date || undefined;
                      setStartDate(newStart);
                      if (newStart && endDate && newStart > endDate) {
                        setEndDate(newStart);
                      }
                    }}
                    placeholderText="Start Date"
                    className="date-picker"
                    dateFormat="MM-dd-yyyy"
                    maxDate={new Date()}
                    isClearable={false}
                  />
                  {startDate && (
                    <button
                      className="clear-date"
                      onClick={() => setStartDate(undefined)}
                      aria-label="Clear start date"
                    >
                      &times;
                    </button>
                  )}
                </div>
                <div className="date-picker-container">
                  <DatePicker
                    selected={endDate}
                    onChange={(date: Date | null) => {
                      const newEnd = date || undefined;
                      setEndDate(newEnd);
                      if (newEnd && startDate && newEnd < startDate) {
                        setStartDate(newEnd);
                      }
                    }}
                    placeholderText="End Date"
                    className="date-picker"
                    dateFormat="MM-dd-yyyy"
                    minDate={startDate}
                    maxDate={new Date()}
                    isClearable={false}
                  />
                  {endDate && (
                    <button
                      className="clear-date"
                      onClick={() => setEndDate(undefined)}
                      aria-label="Clear end date"
                    >
                      &times;
                    </button>
                  )}
                </div>
                <select
                  value={levelFilter}
                  onChange={(e) => setLevelFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Levels</option>
                  <option value="error">Error</option>
                  <option value="warning">Warning</option>
                  <option value="info">Info</option>
                  <option value="debug">Debug</option>
                </select>
              </div>
            </div>

            <div className="table-responsive">
              <table className="error-logs-table" style={{ tableLayout: 'fixed', width: '100%' }}>
                <thead>
                  <tr>
                    <th className="request-col">Request ID</th>
                    <th className="origin-col">{roleId === 1 ? "User" : "Project Name"}</th>
                    <th className="level-col">Level</th>
                    <th className="message-col">Message</th>
                    <th className="date-col">Created</th>
                    <th className="action-col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="text-center" style={{ height: '100px', verticalAlign: 'middle' }}>
                        <div className="spinner-border text-primary" role="status" style={{ width: '3rem', height: '3rem' }}>
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : errorLogs.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="text-center text-muted" style={{ height: '100px', verticalAlign: 'middle', fontSize: '1rem' }}>
                        {searchTerm ? 'No matching results' : 'No error logs found'}
                      </td>
                    </tr>
                  ) : (
                    errorLogs.map((log) => (
                      <tr key={log.id}>
                        <td className="request-col">#{log.request_history_id || 'N/A'}</td>
                        <td className="origin-col">
                          {roleId !== 1
                            ? (log.request_history?.project_name || 'N/A')
                            : (log.request_origin === 'unknown' || !log.request_origin
                              ? (log.user?.email || 'N/A')
                              : log.request_origin)
                          }
                        </td>
                        <td className="level-col">
                          <span className={getLevelBadgeClass(log.level)}>
                            {log.level.toUpperCase()}
                          </span>
                        </td>
                        <td className="message-col">
                          <div
                            className="cursor-pointer"
                            onClick={() => handleViewDetails(log)}
                          >
                            {log.message}
                          </div>
                        </td>
                        <td className="date-col">
                          {new Date(log.created_at).toLocaleString()}
                        </td>
                        <td className="action-col">
                          <div className="dropdown" style={{ position: 'relative' }}>
                            <button
                              ref={(el) => { buttonRefs.current[log.id] = el }}
                              type="button"
                              className="btn btn-outline-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenActionMenuId(openActionMenuId === log.id ? null : log.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '0',
                                padding: '0.1rem 0.25rem',
                                fontSize: '0.7rem',
                                height: '22px',
                                minWidth: '60px',
                                maxWidth: '70px'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === log.id}
                              buttonElement={buttonRefs.current[log.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {/* View Option */}
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  handleViewDetails(log);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px'
                                }}
                              >
                                <ViewReportsIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">View</span>
                              </button>

                              {/* Download Option */}
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  handleDownloadLog(log);
                                  setOpenActionMenuId(null);
                                }}
                                disabled={downloadingLogs[`log-${log.id}`]}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: downloadingLogs[`log-${log.id}`] ? 'not-allowed' : 'pointer',
                                  opacity: downloadingLogs[`log-${log.id}`] ? 0.5 : 1,
                                  borderRadius: '0',
                                  height: '24px'
                                }}
                              >
                                {downloadingLogs[`log-${log.id}`] ? (
                                  <>
                                    <div className="spinner-border spinner-border-sm" role="status">
                                      <span className="visually-hidden">Downloading...</span>
                                    </div>
                                    Downloading...
                                  </>
                                ) : (
                                  <>
                                    <DownloadIcon width="16" height="16" fill="#0082A3" />
                                    <span className="ms-2">Download</span>
                                  </>
                                )}
                              </button>

                              {/* Delete Option */}
                              {roleId === 1 &&
                                <button
                                  type="button"
                                  className="dropdown-item"
                                  onClick={() => {
                                    openDeleteModal(log);
                                    setOpenActionMenuId(null);
                                  }}
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    width: '100%',
                                    textAlign: 'left',
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    padding: '0.25rem 0.75rem',
                                    fontSize: '0.8rem',
                                    cursor: 'pointer',
                                    borderRadius: '0',
                                    height: '24px'
                                  }}
                                >
                                  <DeleteIcon width="16" height="16" fill="#D9363E" />
                                  <span className="ms-2">Delete</span>
                                </button>
                              }
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {errorLogs.length > 0 && (
              <div className="mt-4">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  onPerPageChange={handlePerPageChange}
                  perPage={dataPerPage}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      <ErrorDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        errorLog={selectedLog}
      />

      {isDeleteModalOpen && (
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onCancel={closeDeleteModal}
          onConfirm={handleDeleteConfirm}
          title="Delete Error Log"
          message={`Are you sure you want to delete the error log: ${logToDelete?.message}?`}
          loading={deleteLoading}
        />
      )}
    </div>
  );
};

export default ErrorLogs;