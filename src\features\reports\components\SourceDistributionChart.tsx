import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>ton, ButtonGroup, Spinner, Table } from 'react-bootstrap';
import { CartesianGrid, Legend, Line, LineChart, ResponsiveContainer, <PERSON>ltip, XAxis, YAxis } from 'recharts';
import apiClient from '../../../core/config/api';

interface SourceDistributionData {
  sources: { source: string; count: number }[];
  validationResults: {
    valid: number;
    invalid: number;
    catchAll: number;
    unknown: number;
  };
}

interface SourceDistributionChartProps {
  isActive: boolean;
}

const COLORS = ['#0082A3', '#28a745', '#ffc107', '#dc3545', '#17a2b8'];

const SourceDistributionChart: React.FC<SourceDistributionChartProps> = ({ isActive }) => {
  const [sourceData, setSourceData] = useState<SourceDistributionData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
  const hasLoadedRef = useRef(false);

  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();

    const fetchData = async () => {
      try {
        if (!isActive) {
          return;
        }
        setLoading(true);
        setError(null);

        const response = await apiClient.get('/request-source/distribution', {
          signal: controller.signal
        });

        if (!isMounted) return;

        const formattedData: SourceDistributionData = {
          sources: response.data?.sources?.map((item: any) => ({
            source: item.source,
            count: item.count
          })) || [],
          validationResults: {
            valid: response.data?.validationResults?.valid || 0,
            invalid: response.data?.validationResults?.invalid || 0,
            catchAll: response.data?.validationResults?.catchAll || 0,
            unknown: response.data?.validationResults?.unknown || 0
          }
        };

        setSourceData(formattedData);
        setLoading(false);
      } catch (err: any) {
        if (isMounted && err.name !== 'AbortError') {
          setError('Failed to load source distribution data');
          setLoading(false);
        }
      }
    };

    if (!hasLoadedRef.current) {
      fetchData();
    }

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [isActive]);

  const safeSourceData = sourceData || {
    sources: [],
    validationResults: { valid: 0, invalid: 0, catchAll: 0, unknown: 0 }
  };

  const totalUploads = safeSourceData.sources.reduce((acc, src) => acc + src.count, 0);
  const sourcePercentages = safeSourceData.sources.map(src => ({
    ...src,
    percentage: totalUploads > 0 ? Math.round((src.count / totalUploads) * 100) : 0,
  }));

  const handleDownload = () => {
    const headers = ['Source', 'Count', 'Percentage'];
    const csvRows = [
      headers.join(','),
      ...sourcePercentages.map(src => `"${src.source}",${src.count},${src.percentage}%`)
    ];

    csvRows.push(`Total,${totalUploads},100%`);

    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'source_distribution.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderChartView = () => {
    if (safeSourceData.sources.length === 0) {
      return (
        <div className="text-center p-5">
          <p>No source distribution data available.</p>
        </div>
      );
    }

    // Create data structure for multiple lines
    // We'll create multiple data points across a common X-axis where each source has its own line
    // This creates a proper multi-line chart with each source as a separate line
    const lineChartData = [
      {
        name: 'Start',
        ...safeSourceData.sources.reduce((acc, source) => {
          acc[source.source] = 0; // Start all lines at 0
          return acc;
        }, {} as Record<string, number>)
      },
      {
        name: 'Current',
        ...safeSourceData.sources.reduce((acc, source) => {
          acc[source.source] = source.count; // Show actual values
          return acc;
        }, {} as Record<string, number>)
      }
    ];

    return (
      <div className="p-3">
        <ResponsiveContainer width="100%" height={350}>
          <LineChart
            data={lineChartData}
            margin={{ top: 20, right: 120, left: 20, bottom: 25 }}
            style={{ fontFamily: 'inherit' }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" opacity={0.3} />
            <XAxis
              dataKey="name"
              tick={{ fontSize: '0.65rem', fill: 'var(--color-text-light)' }}
              style={{ fontSize: '0.7rem', fill: 'var(--color-text)' }}
            />
            <YAxis
              tick={{ fontSize: '0.65rem', fill: 'var(--color-text-light)' }}
              style={{ fontSize: '0.7rem', fill: 'var(--color-text)' }}
              width={60}
            />
            <Tooltip
              formatter={(value: number, name: string) => [
                value.toLocaleString(),
                name
              ]}
              contentStyle={{
                backgroundColor: 'var(--color-background)',
                borderColor: 'var(--color-border)',
                borderRadius: '4px',
                fontSize: '0.8rem'
              }}
              itemStyle={{ color: 'var(--color-text)' }}
            />
            <Legend
              wrapperStyle={{
                fontSize: '0.8rem',
                paddingTop: '10px',
                color: 'var(--color-text)'
              }}
            />
            {/* Render a separate Line component for each source */}
            {safeSourceData.sources.map((source, index) => (
              <Line
                key={source.source}
                type="natural"
                dataKey={source.source}
                name={source.source}
                stroke={COLORS[index % COLORS.length]}
                strokeWidth={2}
                dot={{ r: 4, fill: COLORS[index % COLORS.length] }}
                activeDot={{ r: 6, fill: COLORS[index % COLORS.length] }}
                connectNulls={false}
                label={{
                  position: 'top',
                  formatter: (value: number) => value > 0 ? value.toLocaleString() : '',
                  fill: 'var(--color-text)',
                  fontSize: '0.75rem'
                }}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  };

  const renderTableView = () => (
    <div className="table-responsive mt-3">
      <Table striped bordered hover className="reports-table">
        <thead className="table-header">
          <tr>
            <th>Source</th>
            <th>Count</th>
            <th>Percentage</th>
          </tr>
        </thead>
        <tbody>
          {sourcePercentages.map((source, index) => (
            <tr key={index}>
              <td>
                <span
                  className="color-indicator"
                  style={{
                    display: 'inline-block',
                    width: '12px',
                    height: '12px',
                    backgroundColor: COLORS[index % COLORS.length],
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}
                ></span>
                {source.source}
              </td>
              <td>{source.count.toLocaleString()}</td>
              <td className="fw-bold">{source.percentage}%</td>
            </tr>
          ))}
          <tr className="table-active fw-bold">
            <td>Total</td>
            <td>{totalUploads.toLocaleString()}</td>
            <td>100%</td>
          </tr>
        </tbody>
      </Table>
    </div>
  );

  const renderSummary = () => (
    <div className="p-3 bg-light rounded">
      <div className="d-flex align-items-center mb-2 fw-bold">
        <span
          className="color-indicator"
          style={{
            display: 'inline-block',
            width: '12px',
            height: '12px',
            backgroundColor: 'var(--color-text)',
            marginRight: '8px',
            borderRadius: '2px'
          }}
        ></span>
        <span className="small">
          Total: {totalUploads.toLocaleString()} (100%)
        </span>
      </div>
      {sourcePercentages.map((source, index) => (
        <div key={index} className="d-flex align-items-center mb-2">
          <span
            className="color-indicator"
            style={{
              display: 'inline-block',
              width: '12px',
              height: '12px',
              backgroundColor: COLORS[index % COLORS.length],
              marginRight: '8px',
              borderRadius: '2px'
            }}
          ></span>
          <span className="small">
            {source.source}: {source.count.toLocaleString()} ({source.percentage}%)
          </span>
        </div>
      ))}
    </div>
  );

  if (loading) return (
    <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
      <Spinner animation="border" variant="primary" />
    </div>
  );

  if (error) return (
    <div className="alert alert-danger my-3">{error}</div>
  );

  return (
    <div className="bg-transparent">
      <div className="d-flex flex-wrap justify-content-between align-items-center gap-3 mb-3">
        <div className="d-flex flex-wrap gap-2">
          <ButtonGroup>
            <Button
              variant={viewMode === 'chart' ? 'primary' : 'outline-primary'}
              onClick={() => setViewMode('chart')}
              size="sm"
            >
              <i className="bi bi-graph-up me-1"></i> Chart
            </Button>
            <Button
              variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
              onClick={() => setViewMode('table')}
              size="sm"
            >
              <i className="bi bi-table me-1"></i> Table
            </Button>
          </ButtonGroup>
        </div>

        <Button
          variant="outline-primary"
          onClick={handleDownload}
          size="sm"
          disabled={safeSourceData.sources.length === 0}
          style={{ borderColor: 'var(--color-border)', color: 'var(--color-text)' }}
        >
          <i className="bi bi-download me-1"></i> Export
        </Button>
      </div>

      <div className="bg-white shadow-sm rounded">
        {viewMode === 'chart' ? renderChartView() : renderTableView()}
        {renderSummary()}
      </div>

      {safeSourceData.sources.length > 0 && (
        <div className="mt-3 text-end text-muted small">
          Showing {safeSourceData.sources.length} sources
        </div>
      )}
    </div>
  );
};

export default SourceDistributionChart;