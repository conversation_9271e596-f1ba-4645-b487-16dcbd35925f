import React, { useState, useEffect, useRef } from "react";
import Header from "../../../layouts/Header/components/Header";
import Sidebar from "../../../components/Sidebar";
import apiClient from "../../../core/config/api";
import AddServerIPModal from "./AddServerIPModal";
import Pagination from "../../../components/Pagination";
import DeleteConfirmationModal from "../../user/components/DeleteConfirmationModal";
import EditServerIPModal from "./EditServerIPModal";
import { ServerIPType, ServerDropdownOption } from "../../../types/models/ServerIPsType";
import DropdownMenuPortal from "../../email/components/DropdownMenuPortal";
import { DeleteIcon, EditIcon, ActivateIcon, InactivateIcon } from "../../../components/ActionIcons";

const ServerIPs: React.FC = () => {
  const [serverIPs, setServerIPs] = useState<ServerIPType[]>([]);
  const [servers, setServers] = useState<ServerDropdownOption[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedServerIP, setSelectedServerIP] = useState<ServerIPType | null>(null);
  const [serverIPToDelete, setServerIPToDelete] = useState<ServerIPType | null>(null);
  const [activeCount, setActiveCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});
  // New state for selected server IPs and check all
  const [selectedServerIPs, setSelectedServerIPs] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [totalServerIPIds, setTotalServerIPIds] = useState<number[]>([]);
  // State for bulk delete modal
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  // State for bulk activate modal
  const [isBulkActivateModalOpen, setIsBulkActivateModalOpen] = useState(false);
  // State for bulk inactivate modal
  const [isBulkInactivateModalOpen, setIsBulkInactivateModalOpen] = useState(false);
  // Calculate the starting number for the current page
  const startingNumber = (currentPage - 1) * dataPerPage;
  const [loadingAllIds, setLoadingAllIds] = useState(false);
  // Single dropdown state for all actions
  const [actionsDropdownOpen, setActionsDropdownOpen] = useState(false);

  const fetchActiveCount = async () => {
    try {
      const response = await apiClient.get("/server-ips/count/active");
      setActiveCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch active server IPs count:", error);
    }
  };

  const fetchServerIPs = async () => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {
        page: currentPage,
        page_size: dataPerPage,
      };

      if (searchTerm) {
        params.ip_address_contains = searchTerm;
      }

      const response = await apiClient.get("/server-ips", { params });
      setServerIPs(response.data.data);
      setTotalPages(Math.ceil(response.data.total / dataPerPage));
    } catch (err) {
      setError("Failed to fetch server IPs. Please try again later.");
      console.error("Failed to fetch server IPs:", err);
    } finally {
      setLoading(false);
    }
  };

  // New function to fetch all server IP IDs
  const fetchAllServerIPIds = async () => {
    setLoadingAllIds(true);
    try {
      // Instead of using the /count endpoint, get the total from the first page response
      const params: any = {
        page: 1,
        page_size: 100,
      };

      if (searchTerm) {
        params.ip_address_contains = searchTerm;
      }

      // Get first page and extract total count from response
      const firstPageResponse = await apiClient.get("/server-ips", { params });
      const totalCount = firstPageResponse.data.total || 0;

      const pageSize = 100; // Use a reasonable page size
      const totalPagesToFetch = Math.ceil(totalCount / pageSize);

      // Start with IDs from first page
      let allIds: number[] = firstPageResponse.data.data.map((ip: ServerIPType) => ip.id);

      // Fetch remaining pages if needed
      for (let page = 2; page <= totalPagesToFetch; page++) {
        const nextParams: any = {
          page: page,
          page_size: pageSize,
        };

        if (searchTerm) {
          nextParams.ip_address_contains = searchTerm;
        }

        const response = await apiClient.get("/server-ips", { params: nextParams });
        const pageIds = response.data.data.map((ip: ServerIPType) => ip.id);
        allIds = [...allIds, ...pageIds];
      }

      setTotalServerIPIds(allIds);
      return allIds;
    } catch (err) {
      console.error("Failed to fetch all server IP IDs:", err);
      return [];
    } finally {
      setLoadingAllIds(false);
    }
  };

  const fetchServers = async () => {
    try {
      const params = {
        page: 1,
        page_size: 100
      };
      const response = await apiClient.get('/servers/', { params });
      const serverOptions = response.data.data.map((server: any) => ({
        id: server.id,
        server_name: server.server_name,
      }));
      setServers(serverOptions);
    } catch (err) {
      console.error("Failed to fetch servers:", err);
    }
  };

  useEffect(() => {
    fetchServerIPs();
    fetchServers();
    fetchActiveCount();
    // Fetch all server IP IDs when search term changes
    fetchAllServerIPIds();
  }, [currentPage, dataPerPage, searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
    setSelectedServerIPs([]);
    setSelectAll(false);
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    setCurrentPage(1);
    setSelectedServerIPs([]);
    setSelectAll(false);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setDataPerPage(newPerPage);
    setCurrentPage(1);
  };

  const handleAddServerIP = async () => {
    try {
      await fetchServerIPs(); // Refresh the list after adding
      fetchActiveCount();
      await fetchAllServerIPIds();
      setIsAddModalOpen(false);
    } catch (err) {
      setError("Failed to add server IP. Please try again.");
      console.error("Failed to add server IP:", err);
    }
  };

  const handleUpdateServerIP = async (updatedServerIP: ServerIPType) => {
    setServerIPs(serverIPs.map(ip =>
      ip.id === updatedServerIP.id ? updatedServerIP : ip
    ));
    fetchActiveCount();
    await fetchAllServerIPIds();
    setIsEditModalOpen(false);
  };

  const handleDeleteConfirm = async () => {
    if (!serverIPToDelete) return;

    try {
      await apiClient.delete(`/server-ips/${serverIPToDelete.id}`);
      setServerIPs(serverIPs.filter(ip => ip.id !== serverIPToDelete.id));
      fetchActiveCount();
      await fetchAllServerIPIds();
      setIsDeleteModalOpen(false);

      // Remove the deleted IP from selected IPs if it was selected
      if (selectedServerIPs.includes(serverIPToDelete.id)) {
        setSelectedServerIPs(prev => prev.filter(id => id !== serverIPToDelete.id));
      }
    } catch (err) {
      setError("Failed to delete server IP. Please try again.");
      console.error("Failed to delete server IP:", err);
    }
  };

  // Handle checkbox selection
  const handleSelectServerIP = (id: number) => {
    setSelectedServerIPs(prev => {
      if (prev.includes(id)) {
        setSelectAll(false);
        return prev.filter(ipId => ipId !== id);
      } else {
        const newSelected = [...prev, id];
        // Check if all IPs on the current page are selected
        const allCurrentPageSelected = serverIPs.every(ip =>
          newSelected.includes(ip.id)
        );

        // Check if all IPs across all pages are selected
        const allIPsSelected = totalServerIPIds.length > 0 &&
          totalServerIPIds.every(id => newSelected.includes(id));

        if (allIPsSelected) {
          setSelectAll(true);
        }

        return newSelected;
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = async () => {
    if (selectAll) {
      // Immediate UI feedback
      setSelectAll(false);
      setSelectedServerIPs([]);
    } else {
      // Immediate UI feedback
      setSelectAll(true);
      setLoading(true);

      try {
        // Get all server IP IDs if we don't have them yet
        let allIds = totalServerIPIds;
        if (allIds.length === 0) {
          allIds = await fetchAllServerIPIds();
        }

        // Update selected server IPs with all IDs
        setSelectedServerIPs(allIds);
      } catch (err) {
        console.error("Failed to select all server IPs:", err);
        // If there was an error, revert the UI state
        setSelectAll(false);
      } finally {
        setLoading(false);
      }
    }
  };

  // Bulk activate/deactivate selected server IPs
  const handleBulkStatusChange = async (activate: boolean) => {
    if (selectedServerIPs.length === 0) return;

    setLoading(true);
    try {
      // Process each selected server IP
      const updatePromises = selectedServerIPs.map(ipId => {
        return apiClient.put(`/server-ips/${ipId}`, { is_active: activate });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchServerIPs();
      fetchActiveCount();
      // Don't reset selections to maintain multi-page selections
      // setSelectedServerIPs([]);
      // setSelectAll(false);
    } catch (err) {
      setError(`Failed to ${activate ? 'activate' : 'deactivate'} server IPs. Please try again.`);
      console.error(`Failed to ${activate ? 'activate' : 'deactivate'} server IPs:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk activate for selected server IPs
  const handleBulkActivate = () => {
    if (selectedServerIPs.length === 0) return;
    setIsBulkActivateModalOpen(true);
  };

  // Handle bulk inactivate for selected server IPs
  const handleBulkInactivate = () => {
    if (selectedServerIPs.length === 0) return;
    setIsBulkInactivateModalOpen(true);
  };

  // Handle bulk delete for selected server IPs
  const handleBulkDelete = () => {
    if (selectedServerIPs.length === 0) return;
    setIsBulkDeleteModalOpen(true);
  };

  // Handle bulk activate confirmation
  const handleBulkActivateConfirm = async () => {
    if (selectedServerIPs.length === 0) return;

    setLoading(true);
    try {
      // Process each selected server IP
      const updatePromises = selectedServerIPs.map(ipId => {
        return apiClient.put(`/server-ips/${ipId}`, { is_active: true });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchServerIPs();
      fetchActiveCount();
      setSelectedServerIPs([]);
      setSelectAll(false);
      setIsBulkActivateModalOpen(false);
    } catch (err) {
      setError(`Failed to activate server IPs. Please try again.`);
      console.error(`Failed to activate server IPs:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk inactivate confirmation
  const handleBulkInactivateConfirm = async () => {
    if (selectedServerIPs.length === 0) return;

    setLoading(true);
    try {
      // Process each selected server IP
      const updatePromises = selectedServerIPs.map(ipId => {
        return apiClient.put(`/server-ips/${ipId}`, { is_active: false });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchServerIPs();
      fetchActiveCount();
      setSelectedServerIPs([]);
      setSelectAll(false);
      setIsBulkInactivateModalOpen(false);
    } catch (err) {
      setError(`Failed to inactivate server IPs. Please try again.`);
      console.error(`Failed to inactivate server IPs:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk delete confirmation
  const handleBulkDeleteConfirm = async () => {
    if (selectedServerIPs.length === 0) return;

    setLoading(true);
    try {
      // Process each selected server IP
      const deletePromises = selectedServerIPs.map(ipId => {
        return apiClient.delete(`/server-ips/${ipId}`);
      });

      await Promise.all(deletePromises);

      // Refresh the data
      await fetchServerIPs();
      fetchActiveCount();
      await fetchAllServerIPIds();
      setSelectedServerIPs([]);
      setSelectAll(false);
      setIsBulkDeleteModalOpen(false);
    } catch (err) {
      setError(`Failed to delete server IPs. Please try again.`);
      console.error(`Failed to delete server IPs:`, err);
    } finally {
      setLoading(false);
    }
  };

  // We don't need the click outside handler anymore since DropdownMenuPortal handles this
  useEffect(() => {
    // No cleanup needed
    return () => {
      // No cleanup needed
    };
  }, [openActionMenuId]);

  // Get page title with selected count
  const getPageTitle = () => {
    let title = `Server IPs`;

    if (activeCount > 0) {
      title += ` (${activeCount})`;
    }

    return title;
  };

  // Handle button click - either open modal or toggle dropdown
  const handleButtonClick = () => {
    if (selectedServerIPs.length > 0) {
      // If items are selected, toggle the bulk actions dropdown
      setActionsDropdownOpen(!actionsDropdownOpen);
    } else {
      // If no items selected, directly open the Add Server IP modal
      setIsAddModalOpen(true);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const actionsDropdown = document.getElementById('actionsDropdown');
      const dropdownMenu = document.querySelector('.dropdown-menu.show');

      // Check if click is outside both the button and the dropdown menu
      if (actionsDropdownOpen &&
          actionsDropdown &&
          !actionsDropdown.contains(event.target as Node) &&
          dropdownMenu &&
          !dropdownMenu.contains(event.target as Node)) {
        setActionsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [actionsDropdownOpen]);

  return (
    <div className="dashboard-container">
      <style>
        {`
          #actionsDropdown {
            border-radius: 0.5rem !important;
          }
          #actionsDropdown.dropdown-toggle {
            border-radius: 0.5rem !important;
          }
        `}
      </style>
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0 text-white">
                <i className="fas fa-server me-2"></i>{getPageTitle()}
              </h5>
            </div>
          </div>
          <div className="card-body p-3">
            <div className="filters-container mb-3">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search server IPs..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={handleClearSearch}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>

              <div className="filter-group right-filters">
                {/* Single unified dropdown for all actions */}
                <div className="position-relative d-inline-block">
                  <button
                    className={`btn btn-primary ${selectedServerIPs.length > 0 ? 'dropdown-toggle' : ''}`}
                    type="button"
                    id="actionsDropdown"
                    onClick={handleButtonClick}
                    disabled={loading}
                    style={{
                      border: 'none',
                      borderRadius: 'var(--border-radius-sm)',
                      padding: '0.375rem 0.75rem',
                      fontSize: '0.875rem',
                      fontWeight: '400'
                    }}
                  >
                    {selectedServerIPs.length > 0 ? (
                      <>Bulk Actions ({selectedServerIPs.length})</>
                    ) : (
                      <><i className="fas fa-plus me-1"></i> Add Server IP</>
                    )}
                  </button>
                  {actionsDropdownOpen && selectedServerIPs.length > 0 && (
                    <div
                      className="dropdown-menu show"
                      style={{
                        position: 'absolute',
                        top: '100%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        minWidth: '180px',
                        padding: '0.5rem 0',
                        margin: '0.125rem 0 0',
                        backgroundColor: '#fff',
                        border: '1px solid rgba(0,0,0,.15)',
                        borderRadius: '0.375rem',
                        boxShadow: '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
                        zIndex: 1000
                      }}
                    >
                      {/* Show bulk actions only when items are selected */}
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkActivate();
                          setActionsDropdownOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#212529',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <ActivateIcon width="16" height="16" fill="#28a745" />
                        <span className="ms-2">Activate Selected</span>
                      </button>
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkInactivate();
                          setActionsDropdownOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#212529',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <InactivateIcon width="16" height="16" fill="#dc3545" />
                        <span className="ms-2">Inactivate Selected</span>
                      </button>
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkDelete();
                          setActionsDropdownOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#D9363E',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <DeleteIcon width="16" height="16" fill="#D9363E" />
                        <span className="ms-2">Delete Selected</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {error && <div className="error-message">{error}</div>}

            <div className="table-responsive">
              <table className="servers-table" style={{ tableLayout: 'fixed', width: '100%' }}>
                <thead>
                  <tr style={{ background: "#4d7a8c" }}>
                    <th style={{ width: '60px', padding: '0.75rem 0', textAlign: 'center' }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: '0 auto',
                        width: '100%'
                      }}>
                        <div style={{
                          width: '18px',
                          height: '18px',
                          backgroundColor: selectAll ? '#00A3CC' : 'transparent',
                          border: selectAll ? 'none' : '1px solid #dee2e6',
                          borderRadius: '3px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={handleSelectAll}
                        >
                          {selectAll && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                              <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                            </svg>
                          )}
                        </div>
                      </div>
                    </th>
                    <th style={{ width: '80px', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>ID</th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Server</th>
                    <th style={{ width: '18%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>IP Address</th>
                    <th style={{ width: '20%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Sub Domain</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Status</th>
                    <th style={{ width: '17%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Updated At</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="text-center">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : serverIPs.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center">
                        {searchTerm ?
                          'No server IPs found matching your search' :
                          'No server IPs found'}
                      </td>
                    </tr>
                  ) : (
                    serverIPs.map((ip, i) => (
                      <tr key={ip.id}>
                        <td style={{ width: '60px', padding: '0.5rem' }}>
                          <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: '100%'
                          }}>
                            <div style={{
                              width: '18px',
                              height: '18px',
                              backgroundColor: selectedServerIPs.includes(ip.id) ? '#00A3CC' : 'transparent',
                              border: selectedServerIPs.includes(ip.id) ? 'none' : '1px solid #dee2e6',
                              borderRadius: '3px',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              cursor: 'pointer'
                            }}
                            onClick={() => handleSelectServerIP(ip.id)}
                            >
                              {selectedServerIPs.includes(ip.id) && (
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                                  <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                                </svg>
                              )}
                            </div>
                          </div>
                        </td>
                        <td style={{ width: '80px', padding: '0.5rem 0.75rem' }}>#{startingNumber + i + 1}</td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem' }}>{ip.server_name}</td>
                        <td style={{ width: '18%', padding: '0.5rem 0.75rem' }}>{ip.ip_address}</td>
                        <td style={{ width: '20%', padding: '0.5rem 0.75rem' }}>{ip.sub_domain || "-"}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem' }}>
                          <span className={`badge bg-${ip.is_active ? 'success' : 'danger'}`}>
                            {ip.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td style={{ width: '17%', padding: '0.5rem 0.75rem' }}>{new Date(ip.updated_at).toLocaleString()}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem' }}>
                          <div className="dropdown" style={{ position: 'relative' }}>
                            <button
                              ref={(el) => { buttonRefs.current[ip.id] = el }}
                              type="button"
                              className="btn btn-outline-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenActionMenuId(openActionMenuId === ip.id ? null : ip.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                                padding: '0.2rem 0.5rem',
                                fontSize: '0.75rem',
                                height: '24px',
                                width: '80px',
                                cursor: 'pointer',
                                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === ip.id}
                              buttonElement={buttonRefs.current[ip.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {/* Edit Option */}
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  setSelectedServerIP(ip);
                                  setIsEditModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px'
                                }}
                              >
                                <EditIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">Edit</span>
                              </button>

                              {/* Delete Option */}
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  setServerIPToDelete(ip);
                                  setIsDeleteModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: selectedServerIPs.length > 0 ? 'not-allowed' : 'pointer',
                                  borderRadius: '0',
                                  height: '24px',
                                  opacity: selectedServerIPs.length > 0 ? '0.5' : '1'
                                }}
                                disabled={selectedServerIPs.length > 0}
                              >
                                <DeleteIcon width="16" height="16" fill={selectedServerIPs.length > 0 ? "#999" : "#D9363E"} />
                                <span className="ms-2" style={{ color: selectedServerIPs.length > 0 ? '#999' : 'inherit' }}>Delete</span>
                              </button>
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={handlePerPageChange}
              perPage={dataPerPage}
            />
          </div>
        </div>

        <AddServerIPModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onAddSuccess={handleAddServerIP}
          servers={servers}
        />

        {isEditModalOpen && selectedServerIP && (
          <EditServerIPModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onEditSuccess={handleUpdateServerIP}
            serverIP={selectedServerIP}
            servers={servers}
          />
        )}

        {isDeleteModalOpen && serverIPToDelete && (
          <DeleteConfirmationModal
            isOpen={isDeleteModalOpen}
            onClose={() => {
              setIsDeleteModalOpen(false);
              setServerIPToDelete(null);
            }}
            onConfirm={handleDeleteConfirm}
            userName={serverIPToDelete.ip_address}
            entityType="Server IP"
          />
        )}

        {/* Bulk Activate Confirmation Modal */}
        {isBulkActivateModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkActivateModalOpen}
            onClose={() => setIsBulkActivateModalOpen(false)}
            onConfirm={handleBulkActivateConfirm}
            userName={`${selectedServerIPs.length} selected server IPs`}
            entityType="Server IP"
            actionType="activate"
          />
        )}

        {/* Bulk Inactivate Confirmation Modal */}
        {isBulkInactivateModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkInactivateModalOpen}
            onClose={() => setIsBulkInactivateModalOpen(false)}
            onConfirm={handleBulkInactivateConfirm}
            userName={`${selectedServerIPs.length} selected server IPs`}
            entityType="Server IP"
            actionType="inactivate"
          />
        )}

        {/* Bulk Delete Confirmation Modal */}
        {isBulkDeleteModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkDeleteModalOpen}
            onClose={() => setIsBulkDeleteModalOpen(false)}
            onConfirm={handleBulkDeleteConfirm}
            userName={`${selectedServerIPs.length} selected server IPs`}
            entityType="Server IP"
          />
        )}
      </div>
    </div>
  );
};

export default ServerIPs;