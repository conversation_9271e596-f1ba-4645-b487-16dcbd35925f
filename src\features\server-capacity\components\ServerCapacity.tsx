import React, { useState, useEffect, useRef } from 'react';
import Header from '../../../layouts/Header/components/Header';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import AddServerCapacityModal from './AddServerCapacityModal';
import Pagination from '../../../components/Pagination';
import DeleteConfirmationModal from '../../user/components/DeleteConfirmationModal';
import { ServerCapacityType, ServerDropdownOption } from '../../../types/models/ServerCapacityType';
import EditServerCapacityModal from './EditServerCapacityModal';
import DropdownMenuPortal from '../../email/components/DropdownMenuPortal';
import { DeleteIcon, EditIcon } from "../../../components/ActionIcons";

const ServerCapacity: React.FC = () => {
  const [serverCapacities, setServerCapacities] = useState<ServerCapacityType[]>([]);
  const [servers, setServers] = useState<ServerDropdownOption[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCapacity, setSelectedCapacity] = useState<ServerCapacityType | null>(null);
  const [capacityToDelete, setCapacityToDelete] = useState<ServerCapacityType | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<string | null>(null);
  const buttonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});
  // New state for selected capacities and check all
  const [selectedCapacities, setSelectedCapacities] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [totalCapacityIds, setTotalCapacityIds] = useState<number[]>([]);
  // State for bulk delete modal
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  // State for bulk actions dropdown
  const [bulkActionsOpen, setBulkActionsOpen] = useState(false);
  // Calculate the starting number for the current page
  const startingNumber = (currentPage - 1) * dataPerPage;
  const [loadingAllIds, setLoadingAllIds] = useState(false);

  const fetchTotalCount = async () => {
    try {
      const response = await apiClient.get("/server-capacities/count/");
      setTotalCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch server capacity count:", error);
    }
  };

  const fetchServerCapacities = async () => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {
        page: currentPage,
        page_size: dataPerPage,
      };

      if (searchTerm) {
        params.server_name_contains = searchTerm; // Using the backend filter parameter
      }

      const response = await apiClient.get('/server-capacities', { params });
      setServerCapacities(response.data.data);
      setTotalPages(Math.ceil(response.data.total / dataPerPage));
    } catch (err) {
      setError('Failed to fetch server capacities. Please try again later.');
      console.error('Failed to fetch server capacities:', err);
    } finally {
      setLoading(false);
    }
  };

  // New function to fetch all capacity IDs
  const fetchAllCapacityIds = async () => {
    setLoadingAllIds(true);
    try {
      // Instead of using the /count endpoint, get the total from the first page response
      const params: any = {
        page: 1,
        page_size: 100,
      };

      if (searchTerm) {
        params.server_name_contains = searchTerm;
      }

      // Get first page and extract total count from response
      const firstPageResponse = await apiClient.get('/server-capacities', { params });
      const totalCount = firstPageResponse.data.total || 0;

      const pageSize = 100; // Use a reasonable page size
      const totalPagesToFetch = Math.ceil(totalCount / pageSize);

      // Start with IDs from first page
      let allIds: number[] = firstPageResponse.data.data.map((capacity: ServerCapacityType) => {
        return typeof capacity.id === 'string' ? parseInt(capacity.id, 10) : capacity.id;
      });

      // Fetch remaining pages if needed
      for (let page = 2; page <= totalPagesToFetch; page++) {
        const nextParams: any = {
          page: page,
          page_size: pageSize,
        };

        if (searchTerm) {
          nextParams.server_name_contains = searchTerm;
        }

        const response = await apiClient.get('/server-capacities', { params: nextParams });
        const pageIds = response.data.data.map((capacity: ServerCapacityType) => {
          return typeof capacity.id === 'string' ? parseInt(capacity.id, 10) : capacity.id;
        });
        allIds = [...allIds, ...pageIds];
      }

      setTotalCapacityIds(allIds);
      return allIds;
    } catch (err) {
      console.error('Failed to fetch all capacity IDs:', err);
      return [];
    } finally {
      setLoadingAllIds(false);
    }
  };

  const fetchServers = async () => {
    try {
      const params = {
        page: 1,
        page_size: 100
      };
      const response = await apiClient.get('/servers/', { params });
      const serverOptions = response.data.data.map((server: any) => ({
        id: server.id,
        server_name: server.server_name
      }));
      setServers(serverOptions);
    } catch (err) {
      console.error('Failed to fetch servers:', err);
    }
  };

  useEffect(() => {
    fetchServerCapacities();
    fetchServers();
    fetchTotalCount();
    // Fetch all capacity IDs when search term changes
    fetchAllCapacityIds();
  }, [currentPage, dataPerPage, searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
    setSelectedCapacities([]);
    setSelectAll(false);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
    setSelectedCapacities([]);
    setSelectAll(false);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setDataPerPage(newPerPage);
    setCurrentPage(1);
  };

  const handleAddCapacity = async () => {
    try {
      await fetchServerCapacities(); // Refresh the list after adding
      fetchTotalCount();
      await fetchAllCapacityIds();
      setIsAddModalOpen(false);
    } catch (err) {
      setError('Failed to add server capacity. Please try again.');
      console.error('Failed to add server capacity:', err);
    }
  };

  const handleUpdateCapacity = async () => {
    try {
      // Refresh both server capacities and servers list
      await fetchServerCapacities();
      fetchTotalCount();
      await fetchAllCapacityIds();
      setIsEditModalOpen(false);
    } catch (err) {
      setError('Failed to refresh data after update. Please try again.');
      console.error('Failed to refresh data:', err);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!capacityToDelete) return;

    try {
      await apiClient.delete(`/server-capacities/${capacityToDelete.id}`);
      setIsDeleteModalOpen(false);
      setCapacityToDelete(null);
      await fetchServerCapacities();
      fetchTotalCount();
      await fetchAllCapacityIds();

      // Remove the deleted capacity from selected capacities if it was selected
      const numericId = typeof capacityToDelete.id === 'string' ? parseInt(capacityToDelete.id, 10) : capacityToDelete.id;
      if (selectedCapacities.includes(numericId)) {
        setSelectedCapacities(prev => prev.filter(id => id !== numericId));
      }
    } catch (err) {
      setError('Failed to delete server capacity. Please try again.');
      console.error('Failed to delete server capacity:', err);
    }
  };

  // Handle checkbox selection
  const handleSelectCapacity = (id: string | number) => {
    // Convert string ID to number if needed
    const numericId = typeof id === 'string' ? parseInt(id, 10) : id;

    setSelectedCapacities(prev => {
      if (prev.includes(numericId)) {
        setSelectAll(false);
        return prev.filter(capacityId => capacityId !== numericId);
      } else {
        const newSelected = [...prev, numericId];
        // Check if all capacities on the current page are selected
        const allCurrentPageSelected = serverCapacities.every(capacity => {
          const capacityNumericId = typeof capacity.id === 'string' ? parseInt(capacity.id, 10) : capacity.id;
          return newSelected.includes(capacityNumericId);
        });

        // Check if all capacities across all pages are selected
        const allCapacitiesSelected = totalCapacityIds.length > 0 &&
          totalCapacityIds.every(id => newSelected.includes(id));

        if (allCapacitiesSelected) {
          setSelectAll(true);
        }

        return newSelected;
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = async () => {
    if (selectAll) {
      // Immediate UI feedback
      setSelectAll(false);
      setSelectedCapacities([]);
    } else {
      // Immediate UI feedback
      setSelectAll(true);
      setLoading(true);

      try {
        // Get all capacity IDs if we don't have them yet
        let allIds = totalCapacityIds;
        if (allIds.length === 0) {
          allIds = await fetchAllCapacityIds();
        }

        // Update selected capacities with all IDs
        setSelectedCapacities(allIds);
      } catch (err) {
        console.error("Failed to select all capacities:", err);
        // If there was an error, revert the UI state
        setSelectAll(false);
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedCapacities.length === 0) return;
    setIsBulkDeleteModalOpen(true);
  };

  // Handle bulk delete confirmation
  const handleBulkDeleteConfirm = async () => {
    if (selectedCapacities.length === 0) return;

    setLoading(true);
    try {
      // Process each selected capacity
      const deletePromises = selectedCapacities.map(capacityId => {
        return apiClient.delete(`/server-capacities/${capacityId}`);
      });

      await Promise.all(deletePromises);

      // Refresh the data
      await fetchServerCapacities();
      fetchTotalCount();
      await fetchAllCapacityIds();
      setSelectedCapacities([]);
      setSelectAll(false);
      setIsBulkDeleteModalOpen(false);
    } catch (err) {
      setError(`Failed to delete capacities. Please try again.`);
      console.error(`Failed to delete capacities:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle button click - either open modal or toggle dropdown
  const handleButtonClick = () => {
    if (selectedCapacities.length > 0) {
      // If items are selected, toggle the bulk actions dropdown
      setBulkActionsOpen(!bulkActionsOpen);
    } else {
      // If no items selected, directly open the Add Capacity modal
      setIsAddModalOpen(true);
    }
  };

  // Close bulk actions dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById('actionsDropdown');
      const dropdownMenu = document.querySelector('.dropdown-menu.show');

      // Check if click is outside both the button and the dropdown menu
      if (bulkActionsOpen &&
          dropdown &&
          !dropdown.contains(event.target as Node) &&
          dropdownMenu &&
          !dropdownMenu.contains(event.target as Node)) {
        setBulkActionsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [bulkActionsOpen]);

  // Click outside handler to close dropdown menu
  const handleClickOutside = (event: MouseEvent) => {
    if (openActionMenuId !== null) {
      let clickedInsideDropdown = false;

      // Check if click was inside the current open dropdown button
      const openButtonRef = buttonRefs.current[openActionMenuId];
      if (openButtonRef && openButtonRef.contains(event.target as Node)) {
        clickedInsideDropdown = true;
      }

      // Check if click was inside dropdown menu
      const dropdownElements = document.querySelectorAll('.dropdown-portal');
      dropdownElements.forEach(element => {
        if (element.contains(event.target as Node)) {
          clickedInsideDropdown = true;
        }
      });

      if (!clickedInsideDropdown) {
        setOpenActionMenuId(null);
      }
    }
  };

  // Add click outside listener
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openActionMenuId]);

  // Get page title with active count
  const getPageTitle = () => {
    let title = `Server Capacity`;

    if (totalCount > 0) {
      title += ` (${totalCount})`;
    }

    return title;
  };

  return (
    <div className="dashboard-container">
      <style>
        {`
          #actionsDropdown {
            border-radius: 0.5rem !important;
          }
          #actionsDropdown.dropdown-toggle {
            border-radius: 0.5rem !important;
          }
        `}
      </style>
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0 text-white">
                <i className="fas fa-server me-2"></i>{getPageTitle()}
              </h5>
            </div>
          </div>
          <div className="card-body p-3">
            <div className="filters-container mb-3">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search by server name..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={handleClearSearch}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>

              <div className="filter-group right-filters">
                {/* Single unified dropdown for all actions */}
                <div className="position-relative d-inline-block">
                  <button
                    className={`btn btn-primary ${selectedCapacities.length > 0 ? 'dropdown-toggle' : ''}`}
                    type="button"
                    id="actionsDropdown"
                    onClick={handleButtonClick}
                    disabled={loading}
                    style={{
                      border: 'none',
                      borderRadius: '0.5rem !important',
                      padding: '0.375rem 0.75rem',
                      fontSize: '0.875rem',
                      fontWeight: '400'
                    }}
                  >
                    {selectedCapacities.length > 0 ? (
                      <>Bulk Actions ({selectedCapacities.length})</>
                    ) : (
                      <><i className="fas fa-plus me-1"></i> Add Capacity</>
                    )}
                  </button>
                  {bulkActionsOpen && selectedCapacities.length > 0 && (
                    <div
                      className="dropdown-menu show"
                      style={{
                        position: 'absolute',
                        top: '100%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        minWidth: '180px',
                        padding: '0.5rem 0',
                        margin: '0.125rem 0 0',
                        backgroundColor: '#fff',
                        border: '1px solid rgba(0,0,0,.15)',
                        borderRadius: '0.375rem',
                        boxShadow: '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
                        zIndex: 1000
                      }}
                    >
                      {/* Show bulk actions only when items are selected */}
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkDelete();
                          setBulkActionsOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#D9363E',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <DeleteIcon width="16" height="16" fill="#D9363E" />
                        <span className="ms-2">Delete Selected</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {error && <div className="error-message">{error}</div>}

            <div className="table-responsive">
              <table className="servers-table" style={{ tableLayout: 'fixed', width: '100%' }}>
                <thead>
                  <tr style={{ background: "#4d7a8c" }}>
                    <th style={{ width: '60px', padding: '0.75rem 0', textAlign: 'center' }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: '0 auto',
                        width: '100%'
                      }}>
                        <div style={{
                          width: '18px',
                          height: '18px',
                          backgroundColor: selectAll ? '#00A3CC' : 'transparent',
                          border: selectAll ? 'none' : '1px solid #dee2e6',
                          borderRadius: '3px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={handleSelectAll}
                        >
                          {selectAll && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                              <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                            </svg>
                          )}
                        </div>
                      </div>
                    </th>
                    <th style={{ width: '80px', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>ID</th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Server</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Total IPs</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Active IPs</th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Base Capacity</th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Current Capacity</th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Last Updated</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={9} className="text-center">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : serverCapacities.length === 0 ? (
                    <tr>
                      <td colSpan={9} className="text-center">
                        {searchTerm ?
                          'No capacities found matching your search' :
                          'No capacities found'}
                      </td>
                    </tr>
                  ) : (
                    serverCapacities.map((capacity, i) => (
                      <tr key={i}>
                        <td style={{ width: '60px', padding: '0.5rem' }}>
                          <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: '100%'
                          }}>
                            <div style={{
                              width: '18px',
                              height: '18px',
                              backgroundColor: selectedCapacities.includes(typeof capacity.id === 'string' ? parseInt(capacity.id, 10) : capacity.id) ? '#00A3CC' : 'transparent',
                              border: selectedCapacities.includes(typeof capacity.id === 'string' ? parseInt(capacity.id, 10) : capacity.id) ? 'none' : '1px solid #dee2e6',
                              borderRadius: '3px',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              cursor: 'pointer'
                            }}
                            onClick={() => handleSelectCapacity(capacity.id)}
                            >
                              {selectedCapacities.includes(typeof capacity.id === 'string' ? parseInt(capacity.id, 10) : capacity.id) && (
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                                  <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                                </svg>
                              )}
                            </div>
                          </div>
                        </td>
                        <td style={{ width: '80px', padding: '0.5rem 0.75rem' }}>{startingNumber + i + 1}</td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem' }}>{capacity.server_name}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem', textAlign: 'center' }}>{capacity.total_ips}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem', textAlign: 'center' }}>{capacity.active_ips}</td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem', textAlign: 'center' }}>{capacity.base_capacity}</td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem', textAlign: 'center' }}>{capacity.current_capacity}</td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem' }}>{new Date(capacity.last_updated).toLocaleDateString('en-US', {
                          month: '2-digit',
                          day: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit',
                          hour12: true
                        })}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem' }}>
                          <div className="custom-dropdown-container">
                            <div
                              ref={(el) => { buttonRefs.current[`capacity-${capacity.id}`] = el as HTMLButtonElement | null }}
                              className="btn btn-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                setOpenActionMenuId(
                                  openActionMenuId === `capacity-${capacity.id}`
                                    ? null
                                    : `capacity-${capacity.id}`
                                );
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                                padding: '0.2rem 0.5rem',
                                fontSize: '0.75rem',
                                height: '24px',
                                width: '80px',
                                cursor: 'pointer',
                                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </div>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === `capacity-${capacity.id}`}
                              buttonElement={buttonRefs.current[`capacity-${capacity.id}`] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="server-capacity-dropdown-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {/* Edit Option */}
                              <button
                                className="dropdown-item custom-dropdown-item"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setSelectedCapacity(capacity);
                                  setIsEditModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.375rem 0.75rem',
                                  fontSize: '0.875rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '32px'
                                }}
                              >
                                <EditIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">Edit</span>
                              </button>

                              {/* Delete Option */}
                              <button
                                className="dropdown-item custom-dropdown-item"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setCapacityToDelete(capacity);
                                  setIsDeleteModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.375rem 0.75rem',
                                  fontSize: '0.875rem',
                                  cursor: selectedCapacities.length > 0 ? 'not-allowed' : 'pointer',
                                  borderRadius: '0',
                                  height: '32px',
                                  opacity: selectedCapacities.length > 0 ? '0.5' : '1'
                                }}
                                disabled={selectedCapacities.length > 0}
                              >
                                <DeleteIcon width="16" height="16" fill={selectedCapacities.length > 0 ? "#999" : "#D9363E"} />
                                <span className="ms-2" style={{ color: selectedCapacities.length > 0 ? '#999' : 'inherit' }}>Delete</span>
                              </button>
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={handlePerPageChange}
              perPage={dataPerPage}
            />
          </div>
        </div>

        <AddServerCapacityModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onAddSuccess={handleAddCapacity}
          servers={servers}
        />

        {isEditModalOpen && selectedCapacity && (
          <EditServerCapacityModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onEditSuccess={handleUpdateCapacity}
            capacity={selectedCapacity}
            servers={servers}
          />
        )}

        {isDeleteModalOpen && capacityToDelete && (
          <DeleteConfirmationModal
            isOpen={isDeleteModalOpen}
            onClose={() => {
              setIsDeleteModalOpen(false);
              setCapacityToDelete(null);
            }}
            onConfirm={handleDeleteConfirm}
            userName={capacityToDelete.server_name}
            entityType="Server Capacity"
          />
        )}

        {/* Bulk Delete Confirmation Modal */}
        {isBulkDeleteModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkDeleteModalOpen}
            onClose={() => setIsBulkDeleteModalOpen(false)}
            onConfirm={handleBulkDeleteConfirm}
            userName={`${selectedCapacities.length} selected capacities`}
            entityType="Server Capacity"
          />
        )}
      </div>
    </div>
  );
};

export default ServerCapacity;