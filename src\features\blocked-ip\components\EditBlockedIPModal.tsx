import React, { useState, useEffect } from 'react';
import apiClient from '../../../core/config/api';
import { BlockedIPType, ServerDropdownOption } from '../../../types/models/BlockedIPType';

interface EditBlockedIPModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEditSuccess: (updatedBlockedIP: BlockedIPType) => void;
  blockedIP: BlockedIPType | null;
  servers: ServerDropdownOption[];
}

const EditBlockedIPModal: React.FC<EditBlockedIPModalProps> = ({
  isOpen,
  onClose,
  onEditSuccess,
  blockedIP,
  servers,
}) => {
  const [formData, setFormData] = useState({
    server_id: 0,
    ip_address: '',
    blocked_reason: '',
    blocked_until: '',
    is_active: true,
    is_notified: false,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    if (blockedIP) {
      setFormData({
        server_id: blockedIP.server_id,
        ip_address: blockedIP.ip_address,
        blocked_reason: blockedIP.blocked_reason,
        blocked_until: blockedIP.blocked_until,
        is_active: blockedIP.is_active,
        is_notified: blockedIP.is_notified,
      });
    }
  }, [blockedIP]);

  useEffect(() => {
    if (!isOpen) {
      setFormData({
        server_id: 0,
        ip_address: '',
        blocked_reason: '',
        blocked_until: '',
        is_active: true,
        is_notified: false,
      });
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
    }
  }, [isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'is_active' || name === 'is_notified' ? value === 'true' : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!blockedIP) return;
    
    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.put(`/blocked-ips/${blockedIP.id}`, formData);
      setSuccessMessage("Blocked IP updated successfully!");
      onEditSuccess(response.data);

      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      if (err.response?.data?.detail) {
        const apiErrors = err.response.data.detail;

        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              newErrors[field] = error.msg;
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to update blocked IP");
        }
      } else {
        setGeneralError(err.message || "Failed to update blocked IP. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !blockedIP) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Edit Blocked IP</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Server</label>
                <select
                  name="server_id"
                  value={formData.server_id}
                  onChange={handleInputChange}
                  className={`form-select ${errors.server_id ? "is-invalid" : ""}`}
                  required
                  disabled
                >
                  <option value="0">Select a Server</option>
                  {servers.map((server) => (
                    <option key={server.id} value={server.id}>
                      {server.server_name}
                    </option>
                  ))}
                </select>
                {errors.server_id && (
                  <div className="invalid-feedback">{errors.server_id}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">IP Address</label>
                <input
                  type="text"
                  name="ip_address"
                  value={formData.ip_address}
                  onChange={handleInputChange}
                  className={`form-control ${errors.ip_address ? "is-invalid" : ""}`}
                  required
                  disabled
                />
                {errors.ip_address && (
                  <div className="invalid-feedback">{errors.ip_address}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Blocked Reason</label>
                <input
                  type="text"
                  name="blocked_reason"
                  value={formData.blocked_reason}
                  onChange={handleInputChange}
                  className={`form-control ${errors.blocked_reason ? "is-invalid" : ""}`}
                />
                {errors.blocked_reason && (
                  <div className="invalid-feedback">{errors.blocked_reason}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Blocked Until</label>
                <input
                  type="date"
                  name="blocked_until"
                  value={formData.blocked_until}
                  onChange={handleInputChange}
                  className={`form-control ${errors.blocked_until ? "is-invalid" : ""}`}
                />
                {errors.blocked_until && (
                  <div className="invalid-feedback">{errors.blocked_until}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Active</label>
                <select
                  name="is_active"
                  value={String(formData.is_active)}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="true">Yes</option>
                  <option value="false">No</option>
                </select>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Notified</label>
                <select
                  name="is_notified"
                  value={String(formData.is_notified)}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="true">Yes</option>
                  <option value="false">No</option>
                </select>
              </div>
            </div>
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Updating...
                </>
              ) : "Update Blocked IP"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditBlockedIPModal;