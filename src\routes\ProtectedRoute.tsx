import React from "react";
import { useSelector } from "react-redux";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { RootState } from "../app/store";
import LoadingSpinner from "../components/LoadingSpinner";

interface ProtectedRouteProps {
  allowedRoles?: number[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ allowedRoles }) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const isLoading = useSelector((state: RootState) => state.auth.isLoading);
  const location = useLocation();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return <Navigate 
      to={`/login?redirect=${encodeURIComponent(location.pathname)}`} 
      replace 
      state={{ from: location }}
    />;
  }

  // Admin (role_id: 1) can access any route
  if (user.role_id === 1) {
    return <Outlet />;
  }

  // Check if user has any of the allowed roles
  if (allowedRoles && !allowedRoles.includes(user.role_id)) {
    return <Navigate to="/not-authorized" replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;