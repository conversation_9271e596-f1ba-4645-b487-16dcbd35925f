import React, { Suspense, lazy, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Outlet } from "react-router-dom";
import ProtectedRoute from "./ProtectedRoute";
import LoadingSpinner from "../components/LoadingSpinner";
import PageLayout from "../layouts/PageLayout/PageLayout";
import ErrorLogs from "../features/error-log/ErrorLogs";
import BounceMessages from "../features/bounce-messages/BounceMessages";
import EmailPatterns from "../features/email-patterns/EmailPatterns";
import PageNotFound from "../pages/PageNotFound";
import ReportsStats from "../features/reports/components/ReportsStats";

const AuthLayout = lazy(() => import("../layouts/Authentication/AuthLayout"));
const LoginForm = lazy(() => import("../features/auth/components/LoginForm"));
const SignupForm = lazy(() => import("../features/auth/components/SignupForm"));
const NotAuthorized = lazy(() => import("../pages/NotAuthorized"));
const BulkEmailVerifier = lazy(() => import("../features/email/components/BulkEmailVerifier"));
const VerifyEmail = lazy(() => import("../features/email/components/VerifyEmail"));
const EmailFinder = lazy(() => import("../features/email/components/EmailFinder"));
const BulkEmailFinder = lazy(() => import("../features/email/components/BulkEmailFinder"));
const Servers = lazy(() => import("../features/server/components/Server"));
const ServerIPs = lazy(() => import("../features/server-ip/components/ServerIPs"));
const ServerCapacity = lazy(() => import("../features/server-capacity/components/ServerCapacity"));
const BlockedIPs = lazy(() => import("../features/blocked-ip/components/BlockedIPs"));
const UserRoles = lazy(() => import("../features/user/components/UserRoles"));
const UserList = lazy(() => import("../features/user/components/UserList"));
const ApiRequestHistory = lazy(() => import("../features/email/components/ApiRequestHistory"));
const ReportsDashboard = lazy(() => import("../features/reports/ReportsDashboard"));
const UserReportsStats = lazy(() => import("../features/user/components/UserReportsStats"));
const ViewCredits = lazy(() => import("../features/user/components/ViewCredits"));

const AppRouter: React.FC = () => {
  useEffect(() => {
    const dropdownCssLink = document.createElement('link');
    dropdownCssLink.href = '/css/dropdown-menu.css';
    dropdownCssLink.rel = 'stylesheet';
    dropdownCssLink.id = 'dropdown-menu-css';
    document.head.appendChild(dropdownCssLink);

    // Cleanup on unmount
    return () => {
      const cssLink = document.getElementById('dropdown-menu-css');
      if (cssLink) {
        cssLink.remove();
      }
    };
  }, []);

  return (
    <Router>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          {/* Public Routes */}
          <Route element={<AuthLayout><Outlet /></AuthLayout>}>
            <Route path="/" element={<LoginForm />} />
            <Route path="/login" element={<LoginForm />} />
            <Route path="/signup" element={<SignupForm />} />
          </Route>

          {/* Admin-Only Routes */}
          <Route element={<PageLayout><Outlet /></PageLayout>}>
            <Route element={<ProtectedRoute allowedRoles={[1]} />}>
              <Route path="/users-list" element={<UserList />} />
              <Route path="/users-roles" element={<UserRoles />} />
              <Route path="/email-patterns" element={<EmailPatterns />} />
              <Route path="/servers" element={<Servers />} />
              <Route path="/server-ips" element={<ServerIPs />} />
              <Route path="/server-capacity" element={<ServerCapacity />} />
              <Route path="/blocked-ips" element={<BlockedIPs />} />
              <Route path="/api-history" element={<ApiRequestHistory />} />
              <Route path="/bounce-messages" element={<BounceMessages />} />
              <Route path="/reports" element={<ReportsDashboard />} />
              <Route path="/user-reports/:userId" element={<UserReportsStats />} />
              <Route path="/report-stats/:userId/:requestId?" element={<ReportsStats />} />
              <Route path="/view-credit-history/:userId" element={<ViewCredits />} />
            </Route>

            {/* User-Only Routes - Admin can also access these */}
            <Route element={<ProtectedRoute allowedRoles={[2]} />}>
              <Route path="/logs-history" element={<ErrorLogs />} />
              <Route path="/bulk-email-verify" element={<BulkEmailVerifier />} />
              <Route path="/bulk-email-finder" element={<BulkEmailFinder />} />
              <Route path="/verify-email" element={<VerifyEmail />} />
              <Route path="/email-finder" element={<EmailFinder />} />
            </Route>
          </Route>

          <Route path="/not-authorized" element={<NotAuthorized />} />
          <Route path="*" element={<PageNotFound />} />
        </Routes>
      </Suspense>
    </Router>
  );
};

export default AppRouter;