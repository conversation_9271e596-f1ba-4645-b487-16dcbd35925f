import React, { useEffect, useState } from "react";
import apiClient from "../../../core/config/api";

interface EmailFinderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFindSuccess: () => void;
  dailyLimitExceeded?: boolean;
}

interface LimitErrorResponse {
  error: string;
  message: string;
  daily_limit: number;
  remaining: number;
  attempted: number;
}

const EmailFinderModal: React.FC<EmailFinderModalProps> = ({
  isOpen,
  onClose,
  onFindSuccess,
  dailyLimitExceeded = false,
}) => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [domain, setDomain] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [limitError, setLimitError] = useState<LimitErrorResponse | null>(null);

  useEffect(() => {
    if (!isOpen) {
      setFirstName("");
      setLastName("");
      setDomain("");
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
      setLimitError(null);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (dailyLimitExceeded) {
      setGeneralError("You have reached your daily limit. Please try again tomorrow.");
      return;
    }

    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");
    setLimitError(null);

    try {
      const response = await apiClient.post('/finder-requests', {
        request_body: [{
          first_name: firstName,
          last_name: lastName,
          domain: domain
        }],
        request_type: 'finder'
      });

      setSuccessMessage("Email finder request submitted successfully!");
      setFirstName("");
      setLastName("");
      setDomain("");
      onFindSuccess();

      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      const errorData = err.response?.data || {};
      
      // Handle limit exceeded error
      if (errorData.error === "limit_exceeded") {
        setLimitError({
          error: errorData.error,
          message: errorData.message,
          daily_limit: errorData.daily_limit,
          remaining: errorData.remaining,
          attempted: errorData.attempted
        });
        return;
      }

      // Handle validation errors
      if (errorData.detail) {
        const apiErrors = errorData.detail;
        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              if (field === "first_name") newErrors.firstName = error.msg;
              if (field === "last_name") newErrors.lastName = error.msg;
              if (field === "domain") newErrors.domain = error.msg;
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to submit request");
        }
      } else {
        setGeneralError(errorData.message || err.message || "Failed to submit request. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Find Email</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {limitError && (
            <div className="alert alert-warning">
              <div className="limit-error-message">
                <p>{limitError.message}</p>
                <ul className="limit-error-details">
                  <li>Daily limit: {limitError.daily_limit?.toLocaleString('en-US') || 'N/A'}</li>
                  <li>Remaining today: {limitError.remaining?.toLocaleString('en-US') || 'N/A'}</li>
                  <li>Attempted: {limitError.attempted?.toLocaleString('en-US') || 'N/A'}</li>
                </ul>
              </div>
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="form-group">
            <label>First Name</label>
            <input
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              placeholder="Enter first name"
              className={errors.firstName ? "input-error" : ""}
              required
              disabled={dailyLimitExceeded || loading || Boolean(limitError)}
            />
            {errors.firstName && (
              <span className="error-text">{errors.firstName}</span>
            )}
          </div>

          <div className="form-group">
            <label>Last Name</label>
            <input
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              placeholder="Enter last name"
              className={errors.lastName ? "input-error" : ""}
              required
              disabled={dailyLimitExceeded || loading || Boolean(limitError)}
            />
            {errors.lastName && (
              <span className="error-text">{errors.lastName}</span>
            )}
          </div>

          <div className="form-group">
            <label>Domain</label>
            <input
              type="text"
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
              placeholder="Enter domain (e.g., example.com)"
              className={errors.domain ? "input-error" : ""}
              required
              disabled={dailyLimitExceeded || loading || Boolean(limitError)}
            />
            {errors.domain && (
              <span className="error-text">{errors.domain}</span>
            )}
          </div>

          <div className="form-actions">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={dailyLimitExceeded || loading || Boolean(limitError)}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Submitting...
                </>
              ) : "Find Email"}
            </button>
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EmailFinderModal;