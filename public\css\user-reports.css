/* User Reports Container */

/* Card Header */
.card-header {
  background: var(--color-primary-dark);
  color: var(--color-white);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.card-header h5 {
  font-size: 1.25rem;
  font-weight: 600;
}

/* Filter Controls - User Reports Specific */
.user-reports-dashboard .card .user-reports-filter-container {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
  padding-bottom: 10px;
  align-items: flex-end;
  width: 100%;
}

/* Reduce gap between date pickers */
.user-reports-dashboard .card .user-reports-filter-container .user-reports-date-picker + .user-reports-date-picker {
  margin-left: -60px;
}

/* Reduce gap between end date and dropdown */
.user-reports-dashboard .card .user-reports-filter-container .user-reports-date-picker:nth-of-type(2) + .user-reports-filter-select {
  margin-left: -120px;
}

/* Search Input - User Reports Specific */
.user-reports-dashboard .card .user-reports-filter-container .user-reports-search-container {
  position: relative;
  flex-grow: 2;
  max-width: none;
  width: 40%;
}

.user-reports-search-input {
  width: 100%;
  height: 42px;
  padding: 8px 18px;
  font-size: 14px;
  border: 1px solid #0082a3;
  border-radius: 8px;
  box-shadow: inset 0 1px 2px rgba(0,0,0,.075);
}

.user-reports-search-input:focus {
  outline: none;
  border-color: #0082a3;
  box-shadow: 0 0 0 2px rgba(0, 130, 163, 0.25);
}

.user-reports-search-input-clear {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 20px;
  line-height: 1;
  color: #999;
  padding: 0;
}

/* Date Inputs - User Reports Specific */
.user-reports-dashboard .card .user-reports-filter-container .user-reports-date-picker {
  position: relative;
  width: 250px;
  min-width: 250px;
}

/* Reset any potentially problematic CSS for the date picker */
.user-reports-date-picker * {
  box-sizing: border-box;
}

.user-reports-date-picker-input {
  width: 100%;
  height: 42px;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: inset 0 1px 2px rgba(0,0,0,.075);
}

/* Hide any DatePicker built-in clear buttons */
.react-datepicker__close-icon {
  display: none !important;
}

/* Ensure our clear button isn't affected by any CSS */
.user-reports-date-picker span[style] {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Remove any old styling for date clear buttons */
.user-reports-clear-date {
  display: none !important;
}

/* Fix for date picker calendar overlay */
.react-datepicker {
  position: absolute !important;
  z-index: 9999 !important;
  margin-top: 4px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  background-color: white !important;
  border: 1px solid #aeaeae !important;
}

.react-datepicker-popper {
  z-index: 9999 !important;
}

.date-picker-popper {
  z-index: 9999 !important;
}

/* Create spacing below the filters to prevent overlap */
.user-reports-filter-container {
  margin-bottom: 15px !important;
}

/* Select Dropdowns - User Reports Specific */
.user-reports-dashboard .card .user-reports-filter-container .user-reports-filter-select {
  width: 15%;
  min-width: 150px;
  height: 42px;
  padding: 0 12px;
  font-size: 14px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: inset 0 1px 2px rgba(0,0,0,.075);
  background-color: white;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23666' d='M0 2l4 4 4-4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 25px;
  position: relative;
  z-index: 100;  /* Higher than table but lower than date picker */
}

/* Uploads Table */
.uploads-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
  font-size: 0.875rem;
}

/* Action Dropdown Styles  */
.custom-dropdown-container {
  position: relative;
  display: inline-block;
}

.custom-dropdown-toggle {
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: white;
  color: #555;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: none;
  cursor: pointer;
}

.custom-dropdown-toggle:hover {
  background-color: #e9ecef;
}

.uploads-table th,
.uploads-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.uploads-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
  font-weight: 500;
  padding: 10px var(--spacing-xs);
}

.uploads-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

/* Column Widths */
.id-col {
  width: 8%;
  white-space: nowrap;
  color: #333;
  font-weight: normal;
}

.project-col {
  width: 15%;
  white-space: nowrap;
}

.type-col {
  width: 12%;
  white-space: nowrap;
}

.count-col {
  width: 8%;
  white-space: nowrap;
  text-align: center;
}

.progress-col {
  width: 12%;
  white-space: nowrap;
  min-width: 120px;
}

.status-col {
  width: 10%;
  white-space: nowrap;
}

.date-col {
  width: 15%;
  white-space: nowrap;
}

.action-col {
  width: 8%;
  text-align: center;
  white-space: nowrap;
  position: relative; /* Ensure proper stacking context */
}

/* Numbers in Count column should be centered */
.uploads-table td.count-col {
  text-align: center;
}

/* Override status badges to have plain text instead of colorful badges */
.status-badge {
  font-weight: normal;
  text-transform: capitalize;
  color: #333;
  padding: 0;
  background-color: transparent;
}

/* Progress Bar Styles (matching EmailFinder) */
.progress {
  height: 20px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin: 0;
}

.progress-bar {
  height: 100%;
  line-height: 20px;
  color: white;
  text-align: center;
  transition: width 0.6s ease;
  font-size: 11px;
  font-weight: 500;
}

/* Progress bar colors */
.bg-success { background-color: #28a745; }
.bg-info { background-color: #17a2b8; }
.bg-warning { background-color: #ffc107; color: #212529; }

/* Back Button */
.btn-outline-light {
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transition: all 0.2s ease;
}

.btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: white;
  color: white;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 992px) {
  .uploads-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .uploads-table th,
  .uploads-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .progress-col {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .card-header h5 {
    font-size: 1.1rem;
  }

  .uploads-table {
    font-size: 0.8125rem;
  }

  .progress-bar {
    font-size: 10px;
  }
}

/* Bootstrap Dropdown Styles for Portal */
.action-col .dropdown {
  display: flex;
  justify-content: center;
  width: 100%;
}

.action-col .dropdown .btn {
  margin: 0 auto;
  width: 80px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-col .dropdown .dropdown-menu {
  transform: translate(-50%, 0);
  left: 50%;
  right: auto;
  text-align: left;
  min-width: 100px;
  max-width: 150px;
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.15rem 0.4rem; /* Smaller padding */
  font-size: 0.75rem;     /* Smaller font size */
  min-width: auto;        /* Override any default min-width from btn or btn-sm if necessary */
  border-radius: 0 !important; /* Ensure straight lines */
}

/* Ensure no border radius on any dropdowns throughout the app */
.dropdown-menu, 
.dropdown-toggle,
.btn-sm,
.btn-outline-secondary,
.dropdown button {
  border-radius: 0 !important;
}

/* Restore spinner border-radius to make spinners circular again */
.spinner-border,
.spinner-border-sm,
.spinner,
[class*="spinner-"] {
  border-radius: 50% !important; /* Override to keep spinners circular */
}

/* Make sure dropdown menus in portals have straight edges */
body > div[style*="position: absolute"] {
  border-radius: 0 !important;
}