import React from 'react';
import './PartialDownloadConfirmationModal.css';

interface PartialDownloadConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  fileName: string;
  progress: number;
}

const PartialDownloadConfirmationModal: React.FC<PartialDownloadConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  fileName,
  progress,
}) => {
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [message, setMessage] = React.useState<{text: string; isError: boolean} | null>(null);

  const getProgressBarColor = (progress: number): string => {
    if (progress === 100) return 'bg-success';
    if (progress > 70) return 'bg-info';
    if (progress > 30) return '';
    return 'bg-warning';
  };

  const handleConfirm = async () => {
    setIsProcessing(true);
    setMessage(null);
    try {
      await onConfirm();
      setMessage({
        text: `Partial results for "${fileName}" downloaded successfully!`,
        isError: false
      });
      setTimeout(() => onClose(), 1500);
    } catch (error) {
      setMessage({
        text: error instanceof Error ? error.message : 'Failed to download partial results',
        isError: true
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Download Partial Results</h3>
        </div>
        <div className="modal-body">
          {message ? (
            <div className={message.isError ? "error-message" : "success-message"}>
              {message.text}
            </div>
          ) : (
            <>
              <p>
                The verification for "{fileName}" is only {progress}% complete.
              </p>
              <p>Would you like to download the available partial results?</p>
              <div className="progress-warning">
                <div 
                  className={`progress-bar ${getProgressBarColor(progress)}`} 
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </>
          )}
        </div>
        <div className="form-actions">
          {!message && (
            <>
              <button 
                type="button" 
                onClick={handleConfirm}
                disabled={isProcessing}
                className="btn btn-primary"
              >
                {isProcessing ? 'Downloading...' : 'Download Partial Results'}
              </button>
              <button 
                type="button" 
                onClick={onClose}
                disabled={isProcessing}
                className="cancel-button"
              >
                Cancel
              </button>
            </>
          )}
          {message && (
            <button 
              type="button" 
              onClick={onClose}
              className="close-button"
            >
              Close
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PartialDownloadConfirmationModal;