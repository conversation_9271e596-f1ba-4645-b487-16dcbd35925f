import apiClient from '../core/config/api';

let refreshTimeout: number | null = null;

export const refreshAccessToken = async (): Promise<string | null> => {
    try {
        const currentToken = localStorage.getItem('accessToken');
        if (!currentToken) throw new Error('No access token available');

        const response = await apiClient.post(
            '/auth/token/refresh',
            {},
            {
                headers: {
                    Authorization: `Bearer ${currentToken.replace(/^"(.*)"$/, '$1')}`
                }
            }
        );

        const { access_token, expires_in } = response.data;
        localStorage.setItem('accessToken', access_token);
        scheduleTokenRefresh(expires_in);
        return access_token;
    } catch (error) {
        throw error;
    }
};

export const scheduleTokenRefresh = (expiresIn: number) => {
    if (refreshTimeout) {
        clearTimeout(refreshTimeout);
    }

    // Refresh 1 minute before expiration
    const refreshTime = (expiresIn - 60) * 1000;

    refreshTimeout = window.setTimeout(async () => {
        await refreshAccessToken();
    }, refreshTime);
};

export const clearAuthTokens = () => {
    localStorage.removeItem('accessToken');
    if (refreshTimeout) {
        clearTimeout(refreshTimeout);
        refreshTimeout = null;
    }
};