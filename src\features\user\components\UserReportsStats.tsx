import { saveAs } from 'file-saver';
import React, { useEffect, useRef, useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useNavigate, useParams } from 'react-router-dom';
import Pagination from '../../../components/Pagination';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import Header from '../../../layouts/Header/components/Header';
import DropdownMenuPortal from '../../email/components/DropdownMenuPortal';
import { ViewReportsIcon, DownloadIcon } from "../../../components/ActionIcons";

interface UserRequestHistory {
  id: number;
  project_name: string;
  file_name: string;
  status: string;
  request_type: string;
  created_at: string;
  data_count: number;
  progress: number;
}

const UserReportsStats: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [requests, setRequests] = useState<UserRequestHistory[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [requestType, setRequestType] = useState<string>('all');
  const [requestStatus, setRequestStatus] = useState<string>('all');
  const [userName, setUserName] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [downloadingFiles, setDownloadingFiles] = useState<{ [key: string]: boolean }>({});
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  useEffect(() => {
    return () => {
    };
  }, [openActionMenuId]);

  const fetchUserName = async () => {
    try {
      const response = await apiClient.get(`/user/${userId}`);
      const firstName = response.data.first_name || '';
      const lastName = response.data.last_name || '';
      setUserName(`${firstName} ${lastName}`.trim());
      setError(null);
    } catch (error: any) {
      if (error.response?.status === 404) {
        setError('User not found');
        setTimeout(() => {
          navigate('/users-list');
        }, 3000);
      } else {
        setUserName('User');
        setError('Failed to load user information');
      }
    }
  };

  const fetchUserRequests = async () => {
    setLoading(true);
    try {
      // Create an object for our request parameters
      const params: any = {
        page: currentPage,
        page_size: itemsPerPage,
      };

      // Add search term if present
      if (searchTerm.trim()) {
        params.search = searchTerm.trim();
      }

      // Add date filters if present - set exact dates with start/end of day
      if (startDate) {
        // Create a copy of the date to avoid mutating the original
        const startDateCopy = new Date(startDate);
        // Adjust for timezone offset to prevent date shift
        startDateCopy.setHours(0, 0, 0, 0);
        const startDateStr = startDateCopy.toISOString().split('T')[0];
        params.start_date = startDateStr;
      }

      if (endDate) {
        // Create a copy of the date to avoid mutating the original
        const endDateCopy = new Date(endDate);
        // Adjust for timezone offset to prevent date shift
        endDateCopy.setHours(23, 59, 59, 999);
        const endDateStr = endDateCopy.toISOString().split('T')[0];
        params.end_date = endDateStr;
      }

      // For exact day filtering - if only one date is set (either start or end), use it for exact day filtering
      if ((startDate && !endDate) || (!startDate && endDate)) {
        // If only one date is set, filter for that exact day
        const exactDate = startDate || endDate;
        const exactDateCopy = new Date(exactDate!);
        // Adjust for timezone offset to prevent date shift
        exactDateCopy.setHours(12, 0, 0, 0); // Use noon to avoid any timezone issues
        const exactDateStr = exactDateCopy.toISOString().split('T')[0];
        params.exact_date = exactDateStr;
      }

      // Add request type filter - ensure it matches API expected format
      if (requestType && requestType !== 'all') {
        params.type = requestType.toLowerCase();
      }

      // Add status filter - ensure it matches API expected format
      if (requestStatus && requestStatus !== 'all') {
        params.status = requestStatus.toLowerCase();
      }

      // Make sure we're using the correct API endpoint
      const endpoint = `/user/${userId}/reports`;

      const response = await apiClient.get(endpoint, { params });

      setRequests(response.data.data);
      setTotalPages(response.data.total_pages);
      setError(null);
    } catch (error: any) {
      if (error.response?.status === 404) {
        setError('User not found');
      } else {
        setError('Failed to load report data');
      }
      setRequests([]);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  // Define a reference to track if this is the first render
  const isFirstRender = useRef(true);

  // Add a reference to track filter states to detect changes
  const filtersRef = useRef({
    searchTerm,
    startDate,
    endDate,
    requestType,
    requestStatus
  });

  useEffect(() => {
    if (userId) {
      fetchUserName();
    }
  }, [userId]);

  useEffect(() => {
    if (!userId || isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // If filters changed, reset to page 1 and fetch
    const filterChanged =
      filtersRef.current.searchTerm !== searchTerm ||
      filtersRef.current.startDate !== startDate ||
      filtersRef.current.endDate !== endDate ||
      filtersRef.current.requestType !== requestType ||
      filtersRef.current.requestStatus !== requestStatus;

    // Update filter reference
    filtersRef.current = {
      searchTerm,
      startDate,
      endDate,
      requestType,
      requestStatus
    };

    if (filterChanged) {
      setCurrentPage(1);
      fetchUserRequests();
    }
  }, [userId, searchTerm, startDate, endDate, requestType, requestStatus]);

  // Third useEffect for pagination only
  useEffect(() => {
    if (userId && !isFirstRender.current) {
      fetchUserRequests();
    }
  }, [userId, currentPage, itemsPerPage]);

  const getRequestTypeLabel = (type: string) => {
    switch (type.toLowerCase()) {
      case 'verifier': return 'Email Verifier';
      case 'finder': return 'Email Finder';
      default: return type;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle users per page change
  const handlePerPageChange = (newPerPage: number) => {
    setItemsPerPage(newPerPage);
    setCurrentPage(1);
  };

  // Status color function is no longer needed since status cards were removed

  const handleBackToUserList = () => {
    navigate('/users-list');
  };

  const downloadResults = async (id: number) => {
    const stateKey = `results-${id}`;
    if (downloadingFiles[stateKey]) {
      return;
    }

    setDownloadingFiles(prev => ({ ...prev, [stateKey]: true }));

    try {
      let endpoint = '';
      const requestType = requests.find(req => req.id === id)?.request_type.toLowerCase();

      if (requestType === 'verifier') {
        endpoint = `/verifier-requests/${id}/download-results`;
      } else if (requestType === 'finder') {
        endpoint = `/finder-requests/${id}/download-results`;
      } else {
        throw new Error('Unsupported request type');
      }

      const response = await apiClient.get(endpoint, {
        responseType: 'blob'
      });

      const filename = response.headers['content-disposition']
        ?.split('filename=')[1]
        ?.replace(/"/g, '') || `results_${id}.csv`;

      saveAs(response.data, filename);
    } catch (error) {
    } finally {
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
    }
  };

  const viewStatistics = (id: number) => {
    // Navigate to the detailed statistics page for this request
    navigate(`/report-stats/${userId}/${id}`);
  };

  return (
    <div className="dashboard-container user-reports-dashboard" id="user-reports-dashboard">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0 mb-4">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0 text-white">
                <i className="fas fa-chart-bar me-2"></i>User Reports - {userName}
              </h5>
              <button
                className="btn btn-sm btn-outline-light"
                onClick={handleBackToUserList}
              >
                <i className="fas fa-arrow-left me-1"></i> Back to User List
              </button>
            </div>
          </div>

          <div className="card-body p-3">
            {error && (
              <div className="alert alert-danger mb-4" role="alert">
                {error}
                {error === 'User not found' && (
                  <div className="mt-2">Redirecting to users list...</div>
                )}
              </div>
            )}

            {/* Filter Controls using CSS classes */}
            <div className="user-reports-filter-container">
              <div className="user-reports-search-container" style={{ width: '900px', maxWidth: '900px' }}>
                <input
                  type="text"
                  placeholder="Search by project name"
                  value={searchTerm}
                  onChange={(e) => {
                    // Update the search term immediately for the UI
                    setSearchTerm(e.target.value);
                  }}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      setCurrentPage(1); // Reset to page 1 when searching
                    }
                  }}
                  className="user-reports-search-input"
                />
                {searchTerm && (
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setCurrentPage(1); // Reset to page 1 when clearing search
                    }}
                    className="user-reports-search-input-clear"
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>

              {/* START DATE - Completely separate button approach */}
              <div className="user-reports-date-picker" style={{ width: '250px', maxWidth: '250px', position: 'relative' }}>
                <DatePicker
                  selected={startDate}
                  onChange={(date: Date | null) => {
                    if (date) {
                      const fixedDate = new Date(date);
                      fixedDate.setHours(12, 0, 0, 0);
                      setStartDate(fixedDate);
                    } else {
                      setStartDate(undefined);
                    }
                    setCurrentPage(1);
                  }}
                  placeholderText="Start Date"
                  className="user-reports-date-picker-input"
                  dateFormat="MM/dd/yyyy"
                  isClearable={false}
                  maxDate={new Date()}
                />
                {startDate && (
                  <span
                    onClick={() => {
                      setStartDate(undefined);
                      setCurrentPage(1);
                      fetchUserRequests();
                    }}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      color: '#999',
                      fontSize: '18px',
                      cursor: 'pointer',
                      fontWeight: 'bold',
                      zIndex: 9999
                    }}
                  >
                    ×
                  </span>
                )}
              </div>

              {/* END DATE - Completely separate button approach */}
              <div className="user-reports-date-picker" style={{ width: '250px', maxWidth: '250px', position: 'relative' }}>
                <DatePicker
                  selected={endDate}
                  onChange={(date: Date | null) => {
                    if (date) {
                      const fixedDate = new Date(date);
                      fixedDate.setHours(12, 0, 0, 0);
                      setEndDate(fixedDate);
                    } else {
                      setEndDate(undefined);
                    }
                    setCurrentPage(1);
                  }}
                  placeholderText="End Date"
                  className="user-reports-date-picker-input"
                  dateFormat="MM/dd/yyyy"
                  maxDate={new Date()}
                  isClearable={false}
                />
                {endDate && (
                  <span
                    onClick={() => {
                      setEndDate(undefined);
                      setCurrentPage(1);
                      fetchUserRequests();
                    }}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      color: '#999',
                      fontSize: '18px',
                      cursor: 'pointer',
                      fontWeight: 'bold',
                      zIndex: 9999
                    }}
                  >
                    ×
                  </span>
                )}
              </div>

              <select
                className="user-reports-filter-select"
                style={{ marginLeft: '-60px' }}
                value={requestStatus}
                onChange={(e) => setRequestStatus(e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
              </select>

              <select
                className="user-reports-filter-select"
                value={requestType}
                onChange={(e) => setRequestType(e.target.value)}
              >
                <option value="all">All Types</option>
                <option value="verifier">Email Verifier</option>
                <option value="finder">Email Finder</option>
              </select>
            </div>

            <div className="table-responsive">
              <table className="uploads-table">
                <thead>
                  <tr style={{ backgroundColor: "var(--color-table-th)", color: "white" }}>
                    <th className="id-col">Request ID</th>
                    <th className="project-col">Project Name</th>
                    {/* <th className="project-col">Filename</th> */}
                    <th className="type-col">Request Type</th>
                    <th className="count-col">Count</th>
                    <th className="progress-col">Progress</th>
                    <th className="status-col">Status</th>
                    <th className="date-col">Created At</th>
                    <th className="action-col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="text-center py-4">
                        <div className="spinner-border text-primary" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : requests.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center">
                        No reports found
                      </td>
                    </tr>
                  ) : (
                    requests.map((request) => (
                      <tr key={request.id}>
                        <td className="id-col">#{request.id}</td>
                        <td className="project-col">{request.project_name || 'N/A'}</td>
                        {/* <td className="project-col">{request.file_name}</td> */}
                        <td className="type-col">{getRequestTypeLabel(request.request_type)}</td>
                        <td className="count-col">{request.data_count.toLocaleString()}</td>
                        <td className="progress-col">
                          <div className="progress">
                            <div
                              className={`progress-bar ${getProgressBarColor(request.progress || 0)}`}
                              style={{ width: `${request.progress || 0}%` }}
                            >
                              {request.progress || 0}%
                            </div>
                          </div>
                        </td>
                        <td className="status-col">
                          <span className={`status-badge`}>
                            {request.status}
                          </span>
                        </td>
                        <td className="date-col">{formatDate(request.created_at)}</td>
                        <td className="action-col">
                          <div className="dropdown" style={{ position: 'relative' }}>
                            <button
                              ref={(el) => { buttonRefs.current[request.id] = el }}
                              type="button"
                              className="btn btn-outline-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenActionMenuId(openActionMenuId === request.id ? null : request.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                                padding: '0.2rem 0.5rem',
                                fontSize: '0.75rem',
                                height: '24px',
                                width: '80px',
                                cursor: 'pointer',
                                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === request.id}
                              buttonElement={buttonRefs.current[request.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {/* Stats Option */}
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  viewStatistics(request.id);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px'
                                }}
                              >
                                <ViewReportsIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">Stats</span>
                              </button>

                              {/* Download Option - only for completed requests */}
                              {request.status.toLowerCase() === 'completed' && (
                                <button
                                  type="button"
                                  className="dropdown-item"
                                  onClick={() => {
                                    downloadResults(request.id);
                                    setOpenActionMenuId(null);
                                  }}
                                  disabled={downloadingFiles[`results-${request.id}`]}
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    width: '100%',
                                    textAlign: 'left',
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    padding: '0.25rem 0.75rem',
                                    fontSize: '0.8rem',
                                    cursor: downloadingFiles[`results-${request.id}`] ? 'not-allowed' : 'pointer',
                                    borderRadius: '0',
                                    height: '24px',
                                    opacity: downloadingFiles[`results-${request.id}`] ? 0.7 : 1
                                  }}
                                >
                                  {downloadingFiles[`results-${request.id}`] ? (
                                    <>
                                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                      Downloading...
                                    </>
                                  ) : (
                                    <>
                                      <DownloadIcon width="16" height="16" fill="#0082A3" />
                                      <span className="ms-2">Download</span>
                                    </>
                                  )}
                                </button>
                              )}
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {requests.length > 0 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                onPerPageChange={handlePerPageChange}
                perPage={itemsPerPage}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserReportsStats;

function getProgressBarColor(progress: number): string {
  if (progress === 100) return 'bg-success';
  if (progress > 70) return 'bg-info';
  if (progress > 30) return '';
  return 'bg-warning';
}