/* NotAuthorized.css */
.not-authorized-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f8f9fa;
    padding: 2rem;
}

.not-authorized-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
    overflow: hidden;
}

.not-authorized-header {
    background-color: #164966;
    padding: 1.5rem;
    text-align: center;
}

.not-authorized-header h2 {
    margin: 0;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 600;
}

.not-authorized-body {
    padding: 2rem;
    text-align: center;
}

.error-icon {
    margin-bottom: 1.5rem;
}

.error-icon svg {
    stroke-width: 1.5;
}

.not-authorized-body h3 {
    color: #dc3545;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.error-message {
    color: #212529;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.error-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
}

.home-link {
    display: inline-flex;
    align-items: center;
    background-color: #164966;
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.home-link:hover {
    background-color: #1d5d82;
    color: #fff;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .not-authorized-container {
        padding: 1rem;
    }

    .not-authorized-body {
        padding: 1.5rem;
    }
}