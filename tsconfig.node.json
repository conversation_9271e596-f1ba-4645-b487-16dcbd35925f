{"compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.cache/tsconfig.node.tsbuildinfo", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "jsx": "preserve", "target": "ES2020", "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["vite.config.ts", "src"]}