// src/features/server-capacity/components/AddServerCapacityModal.tsx
import React, { useState, useEffect } from 'react';
import apiClient from '../../../core/config/api';
import { ServerCapacityType, ServerDropdownOption } from '../../../types/models/ServerCapacityType';

interface AddServerCapacityModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSuccess: (newCapacity: ServerCapacityType) => void;
  servers: ServerDropdownOption[];
}

const AddServerCapacityModal: React.FC<AddServerCapacityModalProps> = ({
  isOpen,
  onClose,
  onAddSuccess,
  servers,
}) => {
  const [formData, setFormData] = useState({
    server_id: 0,
    total_ips: 0,
    active_ips: 0,
    base_capacity: 0,
    current_capacity: 0,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    if (!isOpen) {
      setFormData({
        server_id: 0,
        total_ips: 0,
        active_ips: 0,
        base_capacity: 0,
        current_capacity: 0,
      });
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
    }
  }, [isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    const trimmedValue = value.trim(); // Trim whitespace
  
    setFormData(prev => ({
      ...prev,
      [name]: name === "server_id" ? Number(trimmedValue) : Number(trimmedValue) || 0,
    }));
  };
  

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.post("/server-capacities", formData);
      setSuccessMessage("Server capacity added successfully!");
      onAddSuccess(response.data);
      
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      if (err.response?.data?.detail) {
        const apiErrors = err.response.data.detail;

        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              newErrors[field] = error.msg;
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to add server capacity");
        }
      } else {
        setGeneralError(err.message || "Failed to add server capacity. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Server Capacity</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="row mb-3">
            <div className="col-md-12">
              <div className="form-group">
                <label className="form-label">Server</label>
                <select
                  name="server_id"
                  value={formData.server_id}
                  onChange={handleInputChange}
                  className={`form-select ${errors.server_id ? "is-invalid" : ""}`}
                  required
                >
                  <option value="0">Select a Server</option>
                  {servers.map((server) => (
                    <option key={server.id} value={server.id}>
                      {server.server_name}
                    </option>
                  ))}
                </select>
                {errors.server_id && (
                  <div className="invalid-feedback">{errors.server_id}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Total IPs</label>
                <input
                  type="number"
                  name="total_ips"
                  value={formData.total_ips}
                  onChange={handleInputChange}
                  className={`form-control ${errors.total_ips ? "is-invalid" : ""}`}
                  required
                  min="0"
                />
                {errors.total_ips && (
                  <div className="invalid-feedback">{errors.total_ips}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Active IPs</label>
                <input
                  type="number"
                  name="active_ips"
                  value={formData.active_ips}
                  onChange={handleInputChange}
                  className={`form-control ${errors.active_ips ? "is-invalid" : ""}`}
                  required
                  min="0"
                />
                {errors.active_ips && (
                  <div className="invalid-feedback">{errors.active_ips}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Base Capacity (requests/min)</label>
                <input
                  type="number"
                  name="base_capacity"
                  value={formData.base_capacity}
                  onChange={handleInputChange}
                  className={`form-control ${errors.base_capacity ? "is-invalid" : ""}`}
                  required
                  min="0"
                />
                {errors.base_capacity && (
                  <div className="invalid-feedback">{errors.base_capacity}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Current Capacity</label>
                <input
                  type="number"
                  name="current_capacity"
                  value={formData.current_capacity}
                  onChange={handleInputChange}
                  className={`form-control ${errors.current_capacity ? "is-invalid" : ""}`}
                  required
                  min="0"
                />
                {errors.current_capacity && (
                  <div className="invalid-feedback">{errors.current_capacity}</div>
                )}
              </div>
            </div>
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading || formData.server_id === 0}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Adding...
                </>
              ) : "Add Capacity"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddServerCapacityModal;