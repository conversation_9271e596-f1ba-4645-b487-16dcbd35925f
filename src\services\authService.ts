import axios from "axios";
import { API_CONFIG } from "../core/config/api";
import { AuthResponse, RegisterPayload } from "../types/models/auth";

const apiClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
});

apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem("accessToken");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      const refreshToken = localStorage.getItem("refreshToken");
      if (refreshToken) {
        const { data } = await apiClient.post<AuthResponse>("/auth/refresh", {
          refreshToken,
        });
        localStorage.setItem("accessToken", data.access_token);
        localStorage.setItem("refreshToken", data.refresh_token || "");
        return apiClient(originalRequest);
      }
    }
    return Promise.reject(error);
  }
);

export const authService = {
  register: async (payload: RegisterPayload) => {
    const { data } = await apiClient.post<AuthResponse>("/auth/register", payload);
    return data;
  },

  login: async (credentials: { email: string; password: string }) => {
    try {
      const { data } = await apiClient.post<AuthResponse>("/auth/login", credentials);
      return data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.message || "Login failed";
        throw new Error(message);
      }
      throw new Error("Login failed");
    }
  },
};