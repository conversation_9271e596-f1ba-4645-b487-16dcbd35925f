.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: var(--color-background);
  }
  
  .spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .spinner-circle {
    width: 16px;
    height: 16px;
    background-color: var(--color-primary-light);
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out;
  }
  
  .spinner-circle:nth-child(2) {
    animation-delay: -0.32s;
    background-color: var(--color-primary-dark);
  }
  
  .spinner-circle:nth-child(3) {
    animation-delay: -0.16s;
    background-color: var(--color-primary-light);
  }
  
  @keyframes bounce {
  
    0%,
    80%,
    100% {
      transform: scale(0);
    }
  
    40% {
      transform: scale(1);
    }
  }
  
  .loading-text {
    margin-top: 16px;
    font-size: 1rem;
    color: var(--color-primary-dark);
    font-weight: 500;
  }
  
  .loading-spinner {
    background: linear-gradient(135deg, var(--color-background) 0%, #E6F3FF 100%);
  }