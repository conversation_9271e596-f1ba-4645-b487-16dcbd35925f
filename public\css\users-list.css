/* User List Container */
.user-list-container {
  padding: var(--spacing-md);
}

/* Card Header */
.card-header {
  background: var(--color-primary-dark);
  color: var(--color-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.card-header h5 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

/* Filters Container */
.filters-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 15px;
  padding: 10px 0;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  width: 100%;
}


.filter-group {
  position: relative;
}

.search-group {
  flex: 1 1 300px;
  min-width: 250px;
  position: relative;
}

.right-filters {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  flex: 0 1 auto;
  /* Prevent expanding but allow shrinking */
  justify-content: flex-end;
  /* Align elements to the right */
}

.date-filters-container {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.date-picker-container {
  position: relative;
  min-width: 150px;
  flex: 1;
}

.filter-select-container {
  min-width: 200px;
  flex: 1;
}


.clear-date {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: transparent;
  font-size: 18px;
  line-height: 1;
  color: #777;
  cursor: pointer;
  z-index: 2;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input {
  width: 100%;
  height: 38px;
  padding: 8px 16px;
  padding-right: 30px;
  font-size: 14px;
  border: 1px solid #0082a3;
  border-radius: 6px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, .075);
}

.clear-search {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: transparent;
  font-size: 18px;
  line-height: 1;
  color: #777;
  cursor: pointer;
  z-index: 2;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Date picker styling */
.date-picker-popper {
  z-index: 9999;
}

.react-datepicker {
  border: 1px solid #d1d5db;
  font-family: inherit;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.react-datepicker__header {
  background-color: var(--color-table-th);
  border-bottom: 1px solid #d1d5db;
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  color: white;
}

.react-datepicker__current-month,
.react-datepicker__day-name {
  color: white;
}

.react-datepicker__navigation {
  top: 8px;
}

.react-datepicker__day--selected {
  background-color: #396A7D;
  color: white;
}

.react-datepicker__day--keyboard-selected {
  background-color: rgba(57, 106, 125, 0.7);
  color: white;
}

.date-picker-container .form-control {
  height: 38px;
  min-width: 150px;
  font-size: 14px;
  padding: 8px 12px;
  padding-right: 30px;
  border: 1px solid #0082a3;
  border-radius: 6px;
}

/* Table Controls */
.table-controls {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

/* Users Table */
.users-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
  font-size: 0.875rem;
}

.users-table th,
.users-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Column Widths */
.users-table .name-col {
  width: 13%;
  white-space: nowrap;
}

.users-table .email-col {
  width: 22%;
  white-space: nowrap;
  text-align: center;
}

.users-table .role-col {
  width: 8%;
  white-space: nowrap;
}

.users-table .status-col {
  width: 8%;
  white-space: nowrap;
}

.users-table .max-limit-col {
  width: 13%;
  white-space: nowrap;
  text-align: center;
  padding-right: 0;
}

.users-table .remaining-limit-col {
  width: 10%;
  white-space: nowrap;
  text-align: center;
  padding-right: 0;
}

.users-table .date-col {
  width: 16%;
  white-space: nowrap;
  text-align: right;
}

.users-table .action-col {
  width: 10%;
  white-space: nowrap;
}

/* Table Header */
.users-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
  font-weight: 500;
  text-align: left;
  padding: 8px var(--spacing-xs);
}

.users-table th.email-col {
  text-align: center;
}

.users-table th.max-limit-col,
.users-table th.remaining-limit-col {
  text-align: center;
  padding-right: 0;
  padding-left: 0;
  font-weight: 500;
  overflow: visible;
}

.users-table th.date-col {
  text-align: right;
  padding-right: 30px;
}

/* Numeric Columns */
.max-limit-col,
.remaining-limit-col {
  font-variant-numeric: tabular-nums;
  font-family: inherit;
  font-size: 0.875rem;
}

/* Action Column Specific Styles */
.users-table .action-col {
  text-align: center;
  padding: var(--spacing-xs);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xs);
}

.action-icon {
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-icon svg {
  width: 16px;
  height: 16px;
}

/* Add styles for custom dropdown container in action column */
.custom-dropdown-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.15rem 0.4rem;
  /* Smaller padding */
  font-size: 0.75rem;
  /* Smaller font size */
  min-width: auto;
  /* Override any default min-width from btn or btn-sm if necessary */
  border-radius: 0 !important;
  /* Ensure straight lines */
  margin: 0 auto;
  /* Center the button */
}

/* Center the dropdown within the action column */
.action-col .dropdown {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Custom hover effect for the dropdown items */
.dropdown-item:hover,
.dropdown-item:focus {
  background-color: rgba(22, 73, 102, 0.1);
  /* Light version of your theme color #164966 */
  color: #164966;
  /* Optional: darken text on hover for better contrast if needed */
}

/* Straight borders for dropdown menu items */
.dropdown-item {
  border-radius: 0 !important;
}

.custom-dropdown-toggle {
  margin: 0 auto;
}

.edit-icon {
  color: var(--color-primary-dark);
}

.edit-icon:hover {
  color: var(--color-primary-light);
  background-color: rgba(0, 123, 255, 0.1);
}

.delete-icon {
  color: var(--color-error);
}

.delete-icon:hover {
  color: var(--color-error-dark);
  background-color: rgba(220, 53, 69, 0.1);
}

/* Hover Effect */
.users-table tbody tr:hover {
  background-color: rgba(0, 130, 163, 0.05);
}

/* Responsive Design */
@media (max-width: 992px) {
  .users-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .users-table th,
  .users-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .users-table .action-col {
    width: 120px;
  }
}

@media (max-width: 768px) {
  .card-header h5 {
    font-size: 1.1rem;
  }

  .users-table {
    font-size: 0.8125rem;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-group,
  .date-filters-container,
  .filter-select-container {
    width: 100%;
  }

  .date-picker-container {
    min-width: 100%;
  }
}

/* Filter Select Dropdown */
.filter-select {
  height: 38px;
  min-width: 160px;
  font-size: 14px;
  padding: 8px 12px;
  border: 1px solid #0082a3;
  border-radius: 6px;
  background-color: white;
  color: #333;
  cursor: pointer;
  appearance: menulist;
}

.filter-select:focus {
  outline: none;
  border-color: #164966;
  box-shadow: 0 0 0 2px rgba(22, 73, 102, 0.25);
}

.filter-select option {
  padding: 8px;
}