export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  role_id: number;
  account_status: string;
  created_at: string;
  phone_number: string | null;
  profile_image_url?: string | null;
  updated_at?: string;
  max_upload_limit: number;
}

export interface Role {
  id: number;
  name: string;
}

export interface UserLimits {
  max_limit: number;
  used_today: number;
  remaining: number;
}