import React, { useState, useEffect } from 'react';
import apiClient from '../../../core/config/api';
import { BlockedIPType, ServerDropdownOption } from '../../../types/models/BlockedIPType';

interface AddBlockedIPModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSuccess: (newBlockedIP: BlockedIPType) => void;
  servers: ServerDropdownOption[];
}

interface ServerIPOption {
  id: number;
  ip_address: string;
}

const AddBlockedIPModal: React.FC<AddBlockedIPModalProps> = ({
  isOpen,
  onClose,
  onAddSuccess,
  servers,
}) => {
  const [formData, setFormData] = useState({
    server_id: 0,
    ip_address: '',
    blocked_reason: '',
    blocked_until: '',
    is_active: true,
    is_notified: false,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [ipOptions, setIpOptions] = useState<ServerIPOption[]>([]);
  const [loadingIps, setLoadingIps] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setFormData({
        server_id: 0,
        ip_address: '',
        blocked_reason: '',
        blocked_until: '',
        is_active: true,
        is_notified: false,
      });
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
      setIpOptions([]);
    }
  }, [isOpen]);

  // Fetch server IPs when server_id changes
  useEffect(() => {
    const fetchServerIPs = async () => {
      if (formData.server_id === 0) {
        setIpOptions([]);
        return;
      }
      
      setLoadingIps(true);
      try {
        const response = await apiClient.get(`/server-ips/by-server/${formData.server_id}`);
        setIpOptions(response.data.map((ip: any) => ({
          id: ip.id,
          ip_address: ip.ip_address
        })));
        
        // Auto-select the first IP if available
        if (response.data.length > 0) {
          setFormData(prev => ({
            ...prev,
            ip_address: response.data[0].ip_address
          }));
        }
      } catch (err) {
        console.error("Failed to fetch server IPs:", err);
        setGeneralError("Failed to load IP addresses for this server.");
      } finally {
        setLoadingIps(false);
      }
    };

    if (formData.server_id) {
      fetchServerIPs();
    }
  }, [formData.server_id]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'is_active' || name === 'is_notified' ? value === 'true' : 
              name === 'server_id' ? Number(value) : 
              value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.post("/blocked-ips", formData);
      setSuccessMessage("Blocked IP added successfully!");
      onAddSuccess(response.data);
      
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      if (err.response?.data?.detail) {
        const apiErrors = err.response.data.detail;

        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              newErrors[field] = error.msg;
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to add blocked IP");
        }
      } else {
        setGeneralError(err.message || "Failed to add blocked IP. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Blocked IP</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Server</label>
                <select
                  name="server_id"
                  value={formData.server_id}
                  onChange={handleInputChange}
                  className={`form-select ${errors.server_id ? "is-invalid" : ""}`}
                  required
                >
                  <option value="0">Select a Server</option>
                  {servers.map((server) => (
                    <option key={server.id} value={server.id}>
                      {server.server_name}
                    </option>
                  ))}
                </select>
                {errors.server_id && (
                  <div className="invalid-feedback">{errors.server_id}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">IP Address</label>
                {loadingIps ? (
                  <div className="form-control d-flex align-items-center justify-content-center">
                    <div className="spinner-border spinner-border-sm me-2" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    Loading IPs...
                  </div>
                ) : formData.server_id > 0 && ipOptions.length > 0 ? (
                  <select
                    name="ip_address"
                    value={formData.ip_address}
                    onChange={handleInputChange}
                    className={`form-select ${errors.ip_address ? "is-invalid" : ""}`}
                    required
                  >
                    <option value="">Select an IP</option>
                    {ipOptions.map((ip) => (
                      <option key={ip.id} value={ip.ip_address}>
                        {ip.ip_address}
                      </option>
                    ))}
                  </select>
                ) : (
                  <input
                    type="text"
                    name="ip_address"
                    value={formData.ip_address}
                    onChange={handleInputChange}
                    className={`form-control ${errors.ip_address ? "is-invalid" : ""}`}
                    required
                    placeholder={formData.server_id > 0 ? "No IPs available for this server" : "Select a server first"}
                  />
                )}
                {errors.ip_address && (
                  <div className="invalid-feedback">{errors.ip_address}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Blocked Reason</label>
                <input
                  type="text"
                  name="blocked_reason"
                  value={formData.blocked_reason}
                  onChange={handleInputChange}
                  className={`form-control ${errors.blocked_reason ? "is-invalid" : ""}`}
                />
                {errors.blocked_reason && (
                  <div className="invalid-feedback">{errors.blocked_reason}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Blocked Until</label>
                <input
                  type="date"
                  name="blocked_until"
                  value={formData.blocked_until}
                  onChange={handleInputChange}
                  className={`form-control ${errors.blocked_until ? "is-invalid" : ""}`}
                />
                {errors.blocked_until && (
                  <div className="invalid-feedback">{errors.blocked_until}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Active</label>
                <select
                  name="is_active"
                  value={String(formData.is_active)}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="true">Yes</option>
                  <option value="false">No</option>
                </select>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Notified</label>
                <select
                  name="is_notified"
                  value={String(formData.is_notified)}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="true">Yes</option>
                  <option value="false">No</option>
                </select>
              </div>
            </div>
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading || !formData.ip_address.trim() || formData.server_id === 0}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                  Adding...
                </>
              ) : "Add Blocked IP"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddBlockedIPModal;