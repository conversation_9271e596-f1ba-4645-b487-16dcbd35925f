import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>ton, ButtonGroup, Spinner, Table } from 'react-bootstrap';
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Cell, Legend, ResponsiveContainer, <PERSON>lt<PERSON>, <PERSON>Axis, YAxis } from 'recharts';
import apiClient from '../../../core/config/api';

interface ServerIPDistributionItem {
  server_ip_id: number;
  ip_address: string;
  request_count: number;
  server_id?: number;
  first_request_time: string;
  last_request_time: string;
}

interface ServerIPDistributionChartProps {
  isActive: boolean;
}

const COLORS = [
  '#0082A3', '#3CACAE', '#58C9C7', '#89E9E0', '#C9F9EF',
  '#005F75', '#007A8C', '#4EDADC', '#B5F5F6', '#D6FAFA'
];

const ServerIPDistributionChart: React.FC<ServerIPDistributionChartProps> = ({ isActive }) => {
  const [serverIPData, setServerIPData] = useState<ServerIPDistributionItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
  const hasLoadedRef = useRef(false);

  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();

    const fetchServerIPData = async () => {
      if (!isActive) return;

      try {
        setLoading(true);
        setError(null);

        const response = await apiClient.get('/server-ip-distribution', {
          signal: controller.signal
        });

        if (!isMounted) return;

        const sortedData = Array.isArray(response.data)
          ? response.data.sort((a: ServerIPDistributionItem, b: ServerIPDistributionItem) =>
            b.request_count - a.request_count)
          : [];

        setServerIPData(sortedData);
        setLoading(false);
        hasLoadedRef.current = true;
      } catch (err: any) {
        if (!isMounted) return;

        console.error('Error fetching server IP distribution:', err);
        setError(err.response?.data?.detail || err.message || 'Failed to load data');
        setLoading(false);
      }
    };

    if (!hasLoadedRef.current) {
      fetchServerIPData();
    }

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [isActive]);

  const totalRequests = serverIPData.reduce((sum, item) => sum + item.request_count, 0);

  const handleDownload = () => {
    const headers = ['IP Address', 'Request Count', 'First Request', 'Last Request', 'Percentage'];
    const csvRows = [
      headers.join(','),
      ...serverIPData.map(item => {
        const percentage = totalRequests > 0
          ? ((item.request_count / totalRequests) * 100).toFixed(2)
          : '0';
        return [
          `"${item.ip_address}"`,
          item.request_count,
          new Date(item.first_request_time).toISOString(),
          new Date(item.last_request_time).toISOString(),
          `${percentage}%`
        ].join(',');
      })
    ];

    csvRows.push(`Total,${totalRequests},,,100%`);

    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'server_ip_distribution.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderChartView = () => {
    if (serverIPData.length === 0) {
      return <div className="text-center p-5">No server IP distribution data available</div>;
    }

    const chartData = serverIPData.map(item => ({
      name: item.ip_address,
      requests: item.request_count,
      percentage: Math.round((item.request_count / totalRequests) * 100)
    }));

    // Calculate dynamic height based on number of items (minimum 400px)
    const chartHeight = Math.max(100, chartData.length * 30);
    const containerHeight = 600; // Fixed container height for scroll

    return (
      <div className="p-1">
        <div
          style={{
            height: containerHeight,
            overflowY: 'auto',
            border: '1px solid #eee',
            borderRadius: '4px'
          }}
        >
          <div style={{ height: chartHeight, minHeight: containerHeight }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                layout="vertical"
                margin={{ top: 20, right: 30, left: -50, bottom: 20 }}
              >
                <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                <XAxis
                  type="number"
                  tick={{ fontSize: 12 }}
                />
                <YAxis
                  dataKey="name"
                  type="category"
                  width={200} // Increased width for IP addresses
                  tick={{ fontSize: 12 }}
                  interval={0}
                  tickFormatter={(value) => value.length > 25 ? `${value.slice(0, 25)}...` : value}
                />
                <Tooltip
                  formatter={(value: number) => value.toLocaleString()}
                  labelFormatter={(label) => (
                    <div style={{ wordWrap: 'break-word', whiteSpace: 'pre-wrap' }}>
                      IP: {label}
                    </div>
                  )}
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    padding: '10px',
                    maxWidth: '300px',
                    wordWrap: 'break-word'
                  }}
                />
                <Legend
                  wrapperStyle={{ paddingTop: '10px' }}
                />
                <Bar
                  dataKey="requests"
                  name="Request Count"
                  fill="#0082A3"
                  label={{
                    position: 'right',
                    formatter: (value: number) => value.toLocaleString(),
                    fontSize: 12,
                    offset: 10 // Added offset from bars
                  }}
                >
                  {chartData.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  };

  const renderTableView = () => (
    <div className="table-responsive mt-3">
      <Table striped bordered hover className="reports-table">
        <thead className="table-header">
          <tr>
            <th>IP Address</th>
            <th>Request Count</th>
            <th>First Request</th>
            <th>Last Request</th>
            <th>Percentage</th>
          </tr>
        </thead>
        <tbody>
          {serverIPData.map((item, index) => {
            const percentage = totalRequests > 0
              ? Math.round((item.request_count / totalRequests) * 100)
              : 0;
            return (
              <tr key={index}>
                <td>
                  <span
                    className="color-indicator"
                    style={{
                      display: 'inline-block',
                      width: '12px',
                      height: '12px',
                      backgroundColor: COLORS[index % COLORS.length],
                      marginRight: '8px',
                      borderRadius: '2px'
                    }}
                  ></span>
                  {item.ip_address}
                </td>
                <td>{item.request_count.toLocaleString()}</td>
                <td>{new Date(item.first_request_time).toLocaleString()}</td>
                <td>{new Date(item.last_request_time).toLocaleString()}</td>
                <td className="fw-bold">{percentage}%</td>
              </tr>
            );
          })}
          <tr className="table-active fw-bold">
            <td>Total</td>
            <td>{totalRequests.toLocaleString()}</td>
            <td colSpan={2}></td>
            <td>100%</td>
          </tr>
        </tbody>
      </Table>
    </div>
  );

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
        <Spinner animation="border" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="d-flex flex-column justify-content-center align-items-center" style={{ height: '300px' }}>
        <p className="text-danger mb-3">{error}</p>
        <Button
          variant="outline-primary"
          onClick={() => {
            hasLoadedRef.current = false;
            setLoading(true);
            setError(null);
          }}
        >
          <i className="bi bi-arrow-clockwise me-1"></i> Retry
        </Button>
      </div>
    );
  }

  // const renderSummary = () => (
  //   <div className="mt-3 p-3 bg-light border-top">
  //     <div className="d-flex align-items-center mb-2 fw-bold">
  //       <span
  //         className="color-indicator"
  //         style={{
  //           display: 'inline-block',
  //           width: '12px',
  //           height: '12px',
  //           backgroundColor: 'var(--color-text)',
  //           marginRight: '8px',
  //           borderRadius: '2px'
  //         }}
  //       ></span>
  //       <span className="small">
  //         Total Requests: {totalRequests.toLocaleString()}
  //       </span>
  //     </div>
  //     {serverIPData.map((item, index) => {
  //       const percentage = totalRequests > 0
  //         ? Math.round((item.request_count / totalRequests) * 100)
  //         : 0;
  //       return (
  //         <div key={index} className="d-flex align-items-center mb-2">
  //           <span
  //             className="color-indicator"
  //             style={{
  //               display: 'inline-block',
  //               width: '12px',
  //               height: '12px',
  //               backgroundColor: COLORS[index % COLORS.length],
  //               marginRight: '8px',
  //               borderRadius: '2px'
  //             }}
  //           ></span>
  //           <span className="small">
  //             {item.ip_address}: {item.request_count.toLocaleString()} ({percentage}%)
  //           </span>
  //         </div>
  //       );
  //     })}
  //   </div>
  // );


  return (
    <div>
      <div className="d-flex justify-content-between mb-3">
        <ButtonGroup>
          <Button
            variant={viewMode === 'chart' ? 'primary' : 'outline-primary'}
            onClick={() => setViewMode('chart')}
          >
            Chart
          </Button>
          <Button
            variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
            onClick={() => setViewMode('table')}
          >
            Table
          </Button>
        </ButtonGroup>
        <Button
          variant="outline-primary"
          onClick={handleDownload}
          disabled={serverIPData.length === 0}
        >
          Export
        </Button>
      </div>

      {viewMode === 'chart' ? renderChartView() : renderTableView()}

      <div className="mt-3">
        <strong>Total Requests:</strong> {totalRequests.toLocaleString()}
      </div>
    </div>
  );
};

export default ServerIPDistributionChart;