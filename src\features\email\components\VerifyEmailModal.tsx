import React, { useState, useEffect } from "react";
import apiClient from "../../../core/config/api";

interface VerifyEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVerifySuccess: () => void;
  dailyLimitExceeded?: boolean;
}

interface LimitErrorResponse {
  error: string;
  message: string;
  daily_limit: number;
  remaining: number;
  attempted: number;
}

const VerifyEmailModal: React.FC<VerifyEmailModalProps> = ({
  isOpen,
  onClose,
  onVerifySuccess,
  dailyLimitExceeded = false,
}) => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [limitError, setLimitError] = useState<LimitErrorResponse | null>(null);

  useEffect(() => {
    if (!isOpen) {
      setEmail("");
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
      setLimitError(null);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (dailyLimitExceeded) {
      setGeneralError("You have reached your daily limit. Please try again tomorrow.");
      return;
    }

    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");
    setLimitError(null);

    try {
      const response = await apiClient.post('/verifier-requests', {
        request_body: [{ email }],
        request_type: 'verifier'
      });

      setSuccessMessage("Email verification request submitted successfully!");
      setEmail("");
      onVerifySuccess();

      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      const errorData = err.response?.data || {};
      
      // Handle limit exceeded error
      if (errorData.error === "limit_exceeded") {
        setLimitError({
          error: errorData.error,
          message: errorData.message,
          daily_limit: errorData.daily_limit,
          remaining: errorData.remaining,
          attempted: errorData.attempted
        });
        return;
      }

      // Handle validation errors
      if (errorData.detail) {
        const apiErrors = errorData.detail;
        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              if (field === "request_body") {
                newErrors.email = error.msg;
              } else {
                newErrors[field] = error.msg;
              }
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to verify email");
        }
      } else {
        setGeneralError(errorData.message || err.message || "Failed to verify email. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Verify Email</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {limitError && (
            <div className="alert alert-warning">
              <div className="limit-error-message">
                <p>{limitError.message}</p>
                <ul className="limit-error-details">
                  <li>Daily limit: {limitError.daily_limit?.toLocaleString('en-US') || 'N/A'}</li>
                  <li>Remaining today: {limitError.remaining?.toLocaleString('en-US') || 'N/A'}</li>
                  <li>Attempted: {limitError.attempted?.toLocaleString('en-US') || 'N/A'}</li>
                </ul>
              </div>
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="form-group">
            <label>Email Address</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email to verify"
              className={errors.email ? "input-error" : ""}
              required
              disabled={dailyLimitExceeded || loading || Boolean(limitError)}
            />
            {errors.email && (
              <span className="error-text">{errors.email}</span>
            )}
          </div>

          <div className="form-actions">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={dailyLimitExceeded || loading || Boolean(limitError)}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Submitting...
                </>
              ) : "Verify Email"}
            </button>
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VerifyEmailModal;