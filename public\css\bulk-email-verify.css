/* Bulk Email Verifier Container */
.bulk-email-verifier-container {
  padding: var(--spacing-md);
}

/* Form Styling */
.bulk-upload-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* Uploads Table */
.uploads-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}

.uploads-table th,
.uploads-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Specific override for action column to allow dropdowns to be visible */
.uploads-table td.action-col {
  overflow: visible; /* Allow the dropdown to overflow the cell without being clipped */
}

.uploads-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
}

.uploads-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

.bulk-upload-form {
  padding: var(--spacing-sm);
}

.project-col {
  width: 25%;
  white-space: nowrap;
}

.uploaded-col{
  width: 12%;
  white-space: nowrap;
}

.file-col {
  width: 20%;
  white-space: nowrap;
}

.data-col {
  width: 8%;
  white-space: nowrap;
}

.status-col {
  width: 10%;
  white-space: nowrap;
}

.count-col {
  width: 8%;
  white-space: nowrap;
}

.progress-col {
  width: 10%;
  white-space: nowrap;
}

.date-col {
  width: 13%;
  white-space: nowrap;
}

.action-col {
  width: 9%;
  white-space: nowrap;
}

.limit-error-message {
  margin-bottom: 1rem;
}

.limit-error-details {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.limit-error-details li {
  margin-bottom: 0.25rem;
}

.limit-error-suggestion {
  font-style: italic;
  margin-top: 0.5rem;
}

/* Custom styles for the Actions dropdown specific to BulkEmailVerifier component */
/* ======================================================================== */

/* Custom styles for the Actions button */
.bulk-verifier-actions-btn {
  padding: 0.15rem 0.4rem; /* Smaller padding */
  font-size: 0.75rem;     /* Smaller font size */
  min-width: auto;        /* Override any default min-width */
}

/* Custom hover effect for dropdown items */
.bulk-verifier-dropdown-item:hover,
.bulk-verifier-dropdown-item:focus {
  background-color: rgba(22, 73, 102, 0.1); /* Light version of theme color #164966 */
  color: #164966; /* Darken text on hover for better contrast */
}

/* Hide unexpected icon next to the Download button */
.bulk-verifier-dropdown-item > svg,
.bulk-verifier-dropdown-item > i {
  display: none !important;
}

.bulk-verifier-dropdown-item::before {
  display: none !important; /* Hide if it's a ::before pseudo-element */
  content: "" !important;    /* Also clear content for pseudo-element */
}