/* Credit Usage Table Styles */
.credit-usage-view .users-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md, 1rem);
  font-size: 0.875rem;
}

.credit-usage-view .users-table th,
.credit-usage-view .users-table td {
  padding: 0.5rem;
  text-align: left;
  border-bottom: 1px solid #396A7D; /* Correct blue shade to match table header */
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Filter Controls - Credit Usage Specific */
.credit-usage-view .filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
  padding: 10px 0;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.credit-usage-view .filter-group {
  position: relative;
}

.credit-usage-view .search-group {
  flex: 1 1 450px; /* Changed from fixed width to relative with minimum width */
  max-width: 1043px; /* Still limit the maximum width */
  position: relative;
}

.credit-usage-view .right-filters {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  flex: 0 1 auto; /* Prevent expanding but allow shrinking */
  justify-content: flex-end; /* Align elements to the right */
}

.credit-usage-view .date-filters-container {
  display: flex;
  gap: 15px;
  align-items: center;
}

.credit-usage-view .date-picker-container {
  position: relative;
  min-width: 160px;
  flex: 0 1 auto; /* Allow shrinking but not growing */
  width: auto; /* Allow container to adjust to content */
}

.credit-usage-view .clear-date {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: transparent;
  font-size: 18px;
  line-height: 1;
  color: #777;
  cursor: pointer;
  z-index: 2;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.credit-usage-view .search-input {
  width: 100%;
  height: 38px;
  padding: 8px 16px;
  padding-right: 30px;
  font-size: 14px;
  border: 1px solid #0082a3;
  border-radius: 6px;
  box-shadow: inset 0 1px 2px rgba(0,0,0,.075);
}

.credit-usage-view .clear-search {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: transparent;
  font-size: 18px;
  line-height: 1;
  color: #777;
  cursor: pointer;
  z-index: 2;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Date picker styling */
.credit-usage-view .date-picker-popper {
  z-index: 9999;
}

.credit-usage-view .react-datepicker {
  border: 1px solid #d1d5db;
  font-family: inherit;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.credit-usage-view .react-datepicker__header {
  background-color: var(--color-table-th);
  border-bottom: 1px solid #d1d5db;
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  color: white;
}

.credit-usage-view .react-datepicker__current-month, 
.credit-usage-view .react-datepicker__day-name {
  color: white;
}

.credit-usage-view .react-datepicker__navigation {
  top: 8px;
}

.credit-usage-view .react-datepicker__day--selected {
  background-color: #396A7D;
  color: white;
}

.credit-usage-view .react-datepicker__day--keyboard-selected {
  background-color: rgba(57, 106, 125, 0.7);
  color: white;
}

.credit-usage-view .date-filter .form-control {
  height: 38px;
  min-width: 150px;
  font-size: 14px;
  padding: 8px 12px;
  padding-right: 30px;
  border: 1px solid #0082a3;
  border-radius: 6px;
}

/* Column Widths */
.credit-usage-view .users-table .id-col {
  width: 5%;
  white-space: nowrap;
}

.credit-usage-view .users-table .user-col {
  width: 15%;
  white-space: nowrap;
}

.credit-usage-view .users-table .email-col {
  width: 20%;
  white-space: nowrap;
  text-align: left;
}

.credit-usage-view .users-table .credits-col {
  width: 10%;
  white-space: nowrap;
  text-align: center;
}

.credit-usage-view .users-table .limit-col {
  width: 10%;
  white-space: nowrap;
  text-align: center;
}

.credit-usage-view .users-table .remaining-col {
  width: 10%;
  white-space: nowrap;
  text-align: center;
}

.credit-usage-view .users-table .created-col,
.credit-usage-view .users-table .updated-col {
  width: 15%;
  white-space: nowrap;
  text-align: center;
}

/* Table Header */
.credit-usage-view .users-table th {
  background-color: var(--color-table-th, #396A7D);
  color: white;
  font-weight: 500;
  text-align: left;
  padding: 8px 0.5rem;
}

.credit-usage-view .users-table th.email-col {
  text-align: left;
}

.credit-usage-view .users-table th.credits-col,
.credit-usage-view .users-table th.limit-col,
.credit-usage-view .users-table th.remaining-col,
.credit-usage-view .users-table th.created-col,
.credit-usage-view .users-table th.updated-col {
  text-align: center;
  font-weight: 500;
  overflow: visible;
}

/* Numeric Columns */
.credit-usage-view .credits-col,
.credit-usage-view .limit-col,
.credit-usage-view .remaining-col {
  font-variant-numeric: tabular-nums;
  font-family: inherit;
  font-size: 0.875rem;
}

/* Row hover effect */
.credit-usage-view .users-table tbody tr:hover {
  background-color: rgba(22, 73, 102, 0.05);
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .credit-usage-view .users-table {
    table-layout: auto;
  }
  
  .credit-usage-view .users-table th,
  .credit-usage-view .users-table td {
    white-space: normal;
  }
  
  .credit-usage-view .filters-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .credit-usage-view .search-group {
    flex: 0 0 100%;
    width: 100%;
    margin-bottom: 10px;
  }
  
  .credit-usage-view .right-filters {
    width: 100%;
  }
}

/* Remove box highlight/outline from input boxes and date pickers when focused */
.search-input-container input:focus,
.form-control:focus {
  outline: none;
  border-color: #0082a3;
  box-shadow: 0 0 0 1px rgba(0, 130, 163, 0.15);
}

/* Date pickers should keep gray border */
.date-picker-container .react-datepicker__input-container input:focus,
.date-picker-container .form-control:focus {
  outline: none;
  border-color: #ced4da;
  box-shadow: 0 0 0 1px rgba(206, 212, 218, 0.25);
}

/* Input styling to match UserList */
.search-input-container .form-control {
  width: 100%;
  height: 38px;
  padding: 8px 16px;
  padding-right: 30px;
  font-size: 14px;
  border: 1px solid #0082a3;
  border-radius: 6px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, .075);
}

/* Date picker styling */
.date-picker-container .form-control {
  height: 38px;
  min-width: 150px;
  font-size: 14px;
  padding: 8px 12px;
  padding-right: 30px;
  border: 1px solid #ced4da;
  border-radius: 6px;
}

/* Ensure the clear buttons are properly positioned and visible */
.search-input-container span,
.date-picker-container span {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 18px;
  cursor: pointer;
  font-weight: bold;
  z-index: 9999;
}

/* Center the DatePicker popup */
.custom-datepicker-popper {
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 9999 !important;
  margin-top: 5px !important;
  position: absolute !important;
}

/* Ensure the popup always appears below the input */
.react-datepicker-popper[data-placement^="bottom"] {
  margin-top: 10px !important;
}

/* Override default positioning for the DatePicker */
.react-datepicker-wrapper {
  display: block;
  width: 100%;
  position: relative;
}

.react-datepicker-popper {
  position: absolute !important;
  transform: none !important;
  top: 100% !important;
  left: 0 !important;
  right: 0 !important;
  margin: 0 auto !important;
  width: 100% !important;
  max-width: 325px !important;
}

/* Ensure proper positioning context */
.date-picker-container {
  position: relative;
}


/* Credit Summary Cards - Updated Style */
.total-metrics-card {
  background: var(--color-background);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 5px 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 120px;
  border-left: 4px solid var(--color-primary-dark);
}

.total-metrics-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.12);
}

.metric-item {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.metric-item span {
  font-size: 0.75rem;
  color: var(--color-text-light);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.metric-value {
  font-size: 1rem;
  color: var(--color-primary-light);
  font-weight: bold;
  margin-top: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .total-metrics-card {
    height: 110px;
    padding: 1.2rem;
  }
}

@media (max-width: 992px) {
  .total-metrics-card {
    height: 100px;
    padding: 1rem;
  }
  
  .metric-item span {
    font-size: 0.7rem;
  }
  
  .metric-value {
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .total-metrics-card {
    height: auto;
    min-height: 90px;
  }
}