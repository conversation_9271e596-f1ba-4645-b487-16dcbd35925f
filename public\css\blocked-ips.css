/* Server capacity Container */
.server-capacity-container {
  padding: var(--spacing-md);
}

/* Servers Table */
.servers-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}

.servers-table th,
.servers-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.servers-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
}

.servers-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

.server-col {
  width: 18%;
  white-space: nowrap;
}

.ip-col {
  width: 20%;
  white-space: nowrap;
}

.reason-col {
  width: 20%;
  white-space: nowrap;
}

.status-col {
  width: 12%;
  white-space: nowrap;
}

.date-col {
  width: 15%;
  white-space: nowrap;
}

.action-col {
  width: 9%;
  white-space: nowrap;
}