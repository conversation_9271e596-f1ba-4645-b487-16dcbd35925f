import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, LabelList, Cell } from 'recharts';
import { Button, ButtonGroup, Table, Form, Spinner } from 'react-bootstrap';
import apiClient from '../../../core/config/api';

interface EmailValidationStatus {
  status: string;
  count: number;
}

interface EmailValidationStatusChartProps {
  isActive: boolean;
}

const COLORS = {
  Valid: '#28a745',
  'Catch-All': '#ffc107',
  Invalid: '#dc3545'
};

const EmailValidationStatusChart: React.FC<EmailValidationStatusChartProps> = ({ isActive }) => {
  const [statuses, setStatuses] = useState<EmailValidationStatus[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
  const [dateRange, setDateRange] = useState<'7days' | '30days'>('7days');
  const hasLoadedRef = useRef(false);

  const axisStyle = {
    fontSize: '0.7rem',
    fill: 'var(--color-text)'
  };

  const tickStyle = {
    fontSize: '0.65rem',
    fill: 'var(--color-text-light)'
  };

  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();

    const fetchData = async () => {
      try {
        if (!isActive) {
          return;
        }
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        params.append('date_range',
          dateRange === '7days' ? 'last7days' :
            dateRange === '30days' ? 'last30days' : 'last7days'
        );

        const response = await apiClient.get(`/validation-status/distribution?${params.toString()}`, {
          signal: controller.signal
        });

        if (!isMounted) return;

        let statusData = [];

        if (response.data?.statuses) {
          statusData = response.data.statuses;
        } else if (Array.isArray(response.data)) {
          statusData = response.data;
        } else if (response.data?.items) {
          statusData = response.data.items;
        } else {
          throw new Error('Unexpected data format from server');
        }

        if (isMounted) {
          setStatuses(statusData);
          hasLoadedRef.current = true;
          setLoading(false);
        }
      } catch (err: any) {
        if (isMounted && err.name !== 'AbortError') {
          setError('Failed to load validation status data');
          hasLoadedRef.current = true;
          setLoading(false);
        }
      }
    };

    // Reset the loaded state when dateRange changes
    hasLoadedRef.current = false;
    fetchData();

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [isActive, dateRange]);

  const totalEmails = statuses.reduce((sum, status) => sum + status.count, 0);
  const enhancedData = statuses.map(status => ({
    ...status,
    percentage: Math.round((status.count / totalEmails) * 100)
  }));

  const handleDownload = () => {
    const headers = ['Status', 'Count', 'Percentage'];
    const csvRows = [
      headers.join(','),
      ...enhancedData.map(status => `"${status.status}",${status.count},${status.percentage}%`)
    ];

    csvRows.push(`Total,${totalEmails},100%`);

    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'validation_status.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderTableView = () => (
    <div className="table-responsive mt-3">
      <Table striped bordered hover className="reports-table">
        <thead className="table-header">
          <tr>
            <th>Status</th>
            <th>Count</th>
            <th>Percentage</th>
          </tr>
        </thead>
        <tbody>
          {enhancedData.map((status, index) => (
            <tr key={index}>
              <td>
                <span
                  className="color-indicator"
                  style={{
                    display: 'inline-block',
                    width: '12px',
                    height: '12px',
                    backgroundColor: COLORS[status.status as keyof typeof COLORS],
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}
                ></span>
                {status.status}
              </td>
              <td>{status.count.toLocaleString()}</td>
              <td className="fw-bold">{status.percentage}%</td>
            </tr>
          ))}
          <tr className="table-active fw-bold">
            <td>Total</td>
            <td>{totalEmails.toLocaleString()}</td>
            <td>100%</td>
          </tr>
        </tbody>
      </Table>
    </div>
  );

  const renderChartView = () => (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart
        data={enhancedData}
        margin={{ top: 20, right: 30, left: 20, bottom: 25 }}
        style={{ fontFamily: 'inherit' }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" opacity={0.3} />
        <XAxis
          dataKey="status"
          tick={tickStyle}
          style={axisStyle}
        />
        <YAxis
          tickFormatter={(value) => value.toLocaleString()}
          tick={tickStyle}
          style={axisStyle}
          width={60}
        />
        <Tooltip
          formatter={(value: number) => [value.toLocaleString(), 'Count']}
          contentStyle={{
            backgroundColor: 'var(--color-background)',
            borderColor: 'var(--color-border)',
            borderRadius: '4px',
            fontSize: '0.8rem'
          }}
          itemStyle={{ color: 'var(--color-text)' }}
        />
        <Legend
          wrapperStyle={{
            fontSize: '0.8rem',
            paddingTop: '10px',
            color: 'var(--color-text)'
          }}
        />
        <Bar dataKey="count" name="Number of Emails" barSize={60}>
          {enhancedData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[entry.status as keyof typeof COLORS]} />
          ))}
          <LabelList
            dataKey="count"
            position="top"
            formatter={(value: number, entry: any) => `${value.toLocaleString()} (${entry.percentage}%)`}
            style={{
              fontWeight: 'bold',
              fontSize: '0.7rem',
              fill: 'var(--color-text)'
            }}
          />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );

  const renderSummaryLabels = () => (
    <div className="mt-3">
      {enhancedData.map((status, index) => (
        <div key={index} className="d-flex align-items-center mb-2">
          <span
            className="color-indicator"
            style={{
              display: 'inline-block',
              width: '12px',
              height: '12px',
              backgroundColor: COLORS[status.status as keyof typeof COLORS],
              marginRight: '8px',
              borderRadius: '2px'
            }}
          ></span>
          <span className="small">
            {status.status}: {status.count.toLocaleString()} ({status.percentage}%)
          </span>
        </div>
      ))}
      <div className="d-flex align-items-center mb-2 fw-bold">
        <span
          className="color-indicator"
          style={{
            display: 'inline-block',
            width: '12px',
            height: '12px',
            backgroundColor: 'var(--color-text)',
            marginRight: '8px',
            borderRadius: '2px'
          }}
        ></span>
        <span className="small">
          Total: {totalEmails.toLocaleString()} (100%)
        </span>
      </div>
    </div>
  );

  if (loading) return (
    <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
      <Spinner animation="border" variant="primary" />
    </div>
  );

  if (error) return (
    <div className="alert alert-danger my-3">{error}</div>
  );

  return (
    <div className="bg-transparent">
      <div className="d-flex flex-wrap justify-content-between align-items-center gap-3 mb-3">
        <div className="d-flex flex-wrap gap-2">
          <ButtonGroup>
            <Button
              variant={viewMode === 'chart' ? 'primary' : 'outline-primary'}
              onClick={() => setViewMode('chart')}
              size="sm"
            >
              <i className="bi bi-graph-up me-1"></i> Chart
            </Button>
            <Button
              variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
              onClick={() => setViewMode('table')}
              size="sm"
            >
              <i className="bi bi-table me-1"></i> Table
            </Button>
          </ButtonGroup>
        </div>

        <div className="d-flex flex-wrap align-items-center gap-2">
          <Form.Select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value as '7days' | '30days')}
            className="form-select-sm"
            style={{
              width: '140px',
              backgroundColor: 'var(--color-background)',
              borderColor: 'var(--color-border)',
              color: 'var(--color-text)'
            }}
          >
            <option value="7days">Last 7 Days</option>
            <option value="30days">Last 30 Days</option>
          </Form.Select>

          <Button
            variant="outline-primary"
            onClick={handleDownload}
            size="sm"
            disabled={statuses.length === 0}
            style={{ borderColor: 'var(--color-border)', color: 'var(--color-text)' }}
          >
            <i className="bi bi-download me-1"></i> Export
          </Button>
        </div>
      </div>

      <div className="chart-container bg-white shadow-sm">
        {viewMode === 'chart' ? renderChartView() : renderTableView()}
        {renderSummaryLabels()}
      </div>

      {statuses.length > 0 && (
        <div className="mt-3 text-end text-muted small">
          Showing {statuses.length} status categories
        </div>
      )}
    </div>
  );
};

export default EmailValidationStatusChart;