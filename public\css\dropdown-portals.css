/* Dropdown-specific styling with straight edges */
.dropdown-item,
.actions-btn-custom,
.custom-dropdown-toggle,
.filter-select,
.date-picker,
.custom-dropdown-menu,
.dropdown button,
.dropdown-menu {
  border-radius: 0 !important;
}

/* No rounded corners for dropdown items on hover */
.dropdown-item:hover {
  background-color: rgba(0, 130, 163, 0.1);
  color: #164966;
}

/* Make sure dropdown menus in portals have straight edges */
body > div.dropdown-portal {
  border-radius: 0 !important;
}

/* Fix for dropdown positioning */
.dropdown {
  position: relative;
}

/* Ensure dropdown content has no rounded corners */
.dropdown-menu .dropdown-item {
  border-radius: 0 !important;
}

/* Specifically target date picker which sometimes uses its own styling */
.dropdown .react-datepicker,
.dropdown .react-datepicker__header,
.dropdown .react-datepicker__day,
.dropdown .react-datepicker__day-name,
.dropdown .react-datepicker__current-month,
.dropdown .react-datepicker__month,
.dropdown .react-datepicker__month-container,
.dropdown .react-datepicker__navigation,
.dropdown .react-datepicker__triangle {
  border-radius: 0 !important;
}

/* Hide icons in dropdown menu items */
.dropdown-item > i,
.dropdown-item > svg,
.dropdown-item > span[class*="icon"],
.dropdown-item > span[class*="material-"],
.dropdown-item::before,
/* Target Google Material icons and Font Awesome icons */
.dropdown-item i.material-icons,
.dropdown-item i.fa,
.dropdown-item i.fas,
.dropdown-item i.far,
.dropdown-item i.fab,
.dropdown-item svg.svg-inline--fa,
/* Target specific case from the screenshot */
button.dropdown-item > svg,
/* Extra aggressive targeting */
.dropdown-menu svg,
.dropdown-menu i {
  display: block !important;
}

/* Remove any padding or spacing for icons */
.dropdown-item {
  padding-left: 1rem !important;
}

/* Specifically target Google material icons if they're being used */
.dropdown-menu .material-icons,
.dropdown-menu .material-symbols-outlined,
.dropdown-menu i.material-icons {
  display: none !important;
} 