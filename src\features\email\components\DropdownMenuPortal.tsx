import React, { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';

interface DropdownMenuPortalProps {
  children: React.ReactNode;
  buttonElement: HTMLButtonElement | null;
  isOpen: boolean;
  onClose: () => void;
  menuStyle?: React.CSSProperties; // Optional prop for custom menu styling
  className?: string; // Added className prop
}

const DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = ({
  children,
  buttonElement,
  isOpen,
  onClose,
  menuStyle,
  className, // Added className parameter
}) => {
  const [position, setPosition] = useState<{ top: number; left: number }>({ top: -9999, left: -9999 }); // Start offscreen
  const [positionCalculated, setPositionCalculated] = useState(false); // Track if position has been calculated
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const calculatePosition = () => {
      if (buttonElement) {
        const rect = buttonElement.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Calculate dropdown dimensions
        const dropdownWidth = 120; // Updated to match new minWidth
        const dropdownHeight = 80; // Approximate height for two menu items

        // Calculate left position, ensuring the dropdown doesn't go off-screen
        let leftPos = rect.left + window.scrollX - 1; // Increased negative offset to shift further left
        
        // Keep dropdown on screen
        if (leftPos + dropdownWidth > viewportWidth - 10) {
          leftPos = viewportWidth - dropdownWidth - 10; // Minimum 10px from right edge
        }
        
        // Ensure dropdown doesn't go off left edge
        if (leftPos < 10) {
          leftPos = 10; // Minimum 10px from left edge
        }

        // Calculate top position, checking if there's enough space below
        let topPos = rect.bottom + window.scrollY;

        // If dropdown would go off bottom of screen, position it above the button
        if (rect.bottom + dropdownHeight > viewportHeight && rect.top > dropdownHeight) {
          topPos = rect.top - dropdownHeight + window.scrollY;
        }

        setPosition({
          top: topPos,
          left: leftPos,
        });
        setPositionCalculated(true); // Mark position as calculated
      }
    };

    if (isOpen && buttonElement) {
      // Use setTimeout to ensure the position is calculated after the DOM is updated
      setTimeout(calculatePosition, 0);
      window.addEventListener('resize', calculatePosition);
      window.addEventListener('scroll', calculatePosition, true);
    } else {
      // Reset position status when closed
      setPositionCalculated(false);
    }

    return () => {
      window.removeEventListener('resize', calculatePosition);
      window.removeEventListener('scroll', calculatePosition, true);
    };
  }, [isOpen, buttonElement]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Prevent handling if the event has already been processed
      if ((event as any)._dropdownHandled) return;

      if (
        isOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonElement &&
        !buttonElement.contains(event.target as Node)
      ) {
        // Mark the event as handled to prevent duplicate processing
        (event as any)._dropdownHandled = true;
        onClose();
      }
    };

    if (isOpen) {
      // Use a small timeout to ensure this runs after other click handlers
      const timeoutId = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 0);

      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }

    return undefined;
  }, [isOpen, onClose, buttonElement]);

  // Don't render anything if dropdown is closed, button is missing, or position not calculated
  if (!isOpen || !buttonElement || !positionCalculated) {
    return null;
  }

  const defaultMenuStyle: React.CSSProperties = {
    position: 'absolute',
    top: `${position.top}px`,
    left: `${position.left}px`,
    minWidth: '120px', // Reduced from 150px
    width: 'auto', // Allow width to adjust to content
    padding: '0', // Remove padding from outer container
    backgroundColor: 'white',
    boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
    border: '1px solid #dee2e6',
    borderRadius: '4px',
    zIndex: 9999, // Ensure it's above all other content
    opacity: 1, // Full opacity once positioned correctly
    transition: 'opacity 0.1s ease-in-out', // Smooth fade in
    overflow: 'hidden', // Prevent content from spilling out
    transform: 'translateX(-20px)', // Increased shift to the left
  };

  const combinedStyles = { 
    ...defaultMenuStyle, 
    ...menuStyle,
    // Ensure these properties always take precedence
    left: `${position.left}px`,
    transform: 'translateX(-20px)'
  };

  return ReactDOM.createPortal(
    <div
      ref={menuRef}
      style={combinedStyles}
      className={`dropdown-portal dropdown-menu show custom-dropdown-menu ${className || ''}`}
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()} // Prevent mousedown from closing the dropdown
    >
      <div style={{ padding: '0.025rem 0' }}>
        {children}
      </div>
    </div>,
    document.body
  );
};

export default DropdownMenuPortal;