/* Server capacity Container */
.server-capacity-container {
  padding: var(--spacing-md);
}

/* Servers Table */
.servers-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}

.servers-table th {
  padding: var(--spacing-xs);
  text-align: center !important;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.servers-table td {
  padding: var(--spacing-sm);
  text-align: center;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.servers-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
}

.servers-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

/* Actions Column */
.servers-table td:last-child {
  white-space: nowrap;
}

/* Bootstrap Dropdown Styles - Using our custom dropdown-menu.css instead */

/* Column Alignments */
.id-col {
  width: 6%;
  text-align: center;
}

.server-col {
  width: 20%;
  text-align: center;
}

.ip-col {
  width: 12%;
  text-align: center;
}

.ips-col {
  width: 12%;
  text-align: center;
}

.capacity-col {
  width: 12%;
  text-align: center;
}

.date-col {
  width: 15%;
  text-align: center;
}

.action-col {
  width: 9%;
  text-align: center;
}

/* Center the dropdown button in the action column */
.action-col .dropdown {
  display: flex;
  justify-content: center;
}

/* Ensure the button appears correctly */
.action-col .dropdown .btn {
  margin: 0 auto;
  width: 80px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Position the dropdown menu correctly */
.action-col .dropdown .dropdown-menu {
  transform: translate(-50%, 0);
  left: 50%;
  right: auto;
  text-align: left;
  min-width: 100px;
  max-width: 150px;
}

/* Styles for DropdownMenuPortal in server capacity */
.server-capacity-dropdown-menu {
  position: absolute;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1050;
  padding: 0.25rem 0;
  text-align: left;
  min-width: 120px;
  max-width: 150px;
}

/* Using our custom dropdown-menu.css instead */

/* Show icons next to dropdown items */
.dropdown-item > svg,
.dropdown-item > i {
  display: inline-flex !important;
  margin-right: 8px;
}

/* Restore spinner border-radius to make spinners circular again */
.spinner-border,
.spinner-border-sm,
.spinner,
[class*="spinner-"] {
  border-radius: 50% !important; /* Override to keep spinners circular */
}

/* Make sure dropdown menus in portals have straight edges */
body > div[style*="position: absolute"] {
  border-radius: 0 !important;
}

