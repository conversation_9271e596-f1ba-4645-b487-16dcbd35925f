.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #e0e0e0;
    background-color: #164966;
    border-radius: 8px 8px 0 0;
    margin: -1.5rem -1.5rem 1.5rem -1.5rem;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #fff;
}

/* Form Group */
.form-group {
    margin-bottom: var(--spacing-sm);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--color-primary-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-xs);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.form-actions button {
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--color-white);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    white-space: nowrap;
    font-size: var(--spacing-custom-xs);
}

.form-actions button[type="submit"] {
    background: var(--color-table-th);
    color: var(--color-white);
}

.form-actions button[type="submit"]:hover {
    background: var(--color-primary-light);
}

.form-actions button[type="button"] {
    background: #ccc;
    color: var(--color-primary-dark);
}

.form-actions button[type="button"]:hover {
    background: #bbb;
}

.form-actions button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}