import React, { useState, useEffect } from "react";
import apiClient from "../../../core/config/api";

interface AddServerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSuccess: (newServer: any) => void;
}

const AddServerModal: React.FC<AddServerModalProps> = ({
  isOpen,
  onClose,
  onAddSuccess,
}) => {
  const [formData, setFormData] = useState({
    server_name: "",
    server_url: "",
    domain_ip: "",
    server_purpose: "default",
    server_password: "",
    comments: "",
    is_active: true,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    if (!isOpen) {
      // Reset form when modal closes
      setFormData({
        server_name: "",
        server_url: "",
        domain_ip: "",
        server_purpose: "default",
        server_password: "",
        comments: "",
        is_active: true,
      });
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
    }
  }, [isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    const trimmedValue = value.trim();
  
    setFormData(prev => ({
      ...prev,
      [name]: name === "is_active" ? trimmedValue === "true" : trimmedValue,
    }));
  };
  

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.post("/servers", formData);
      setSuccessMessage("Server added successfully!");
      onAddSuccess(response.data);
      
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      if (err.response?.data?.detail) {
        const apiErrors = err.response.data.detail;

        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              newErrors[field] = error.msg;
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to add server");
        }
      } else {
        setGeneralError(err.message || "Failed to add server. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Server</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          {/* First Row */}
          <div className="row mb-3">
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">Server Name</label>
                <input
                  type="text"
                  name="server_name"
                  value={formData.server_name}
                  onChange={handleInputChange}
                  className={`form-control ${errors.server_name ? "is-invalid" : ""}`}
                  maxLength={255}
                  required
                />
                {errors.server_name && (
                  <div className="invalid-feedback">{errors.server_name}</div>
                )}
              </div>
            </div>
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">Server Password</label>
                <input
                  type="password"
                  name="server_password"
                  value={formData.server_password}
                  onChange={handleInputChange}
                  className={`form-control ${errors.server_password ? "is-invalid" : ""}`}
                  maxLength={255}
                  required
                />
                {errors.server_password && (
                  <div className="invalid-feedback">{errors.server_password}</div>
                )}
              </div>
            </div>
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">Server URL</label>
                <input
                  type="text"
                  name="server_url"
                  value={formData.server_url}
                  onChange={handleInputChange}
                  className={`form-control ${errors.server_url ? "is-invalid" : ""}`}
                  maxLength={255}
                />
                {errors.server_url && (
                  <div className="invalid-feedback">{errors.server_url}</div>
                )}
              </div>
            </div>
          </div>

          {/* Second Row */}
          <div className="row mb-3">
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">Domain IP</label>
                <input
                  type="text"
                  name="domain_ip"
                  value={formData.domain_ip}
                  onChange={handleInputChange}
                  className={`form-control ${errors.domain_ip ? "is-invalid" : ""}`}
                  required
                />
                {errors.domain_ip && (
                  <div className="invalid-feedback">{errors.domain_ip}</div>
                )}
              </div>
            </div>
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">Server Purpose</label>
                <select
                  name="server_purpose"
                  value={formData.server_purpose}
                  onChange={handleInputChange}
                  className={`form-select ${errors.server_purpose ? "is-invalid" : ""}`}
                >
                  <option value="default">Default</option>
                  <option value="single">Single</option>
                  <option value="demo">Demo</option>
                </select>
                {errors.server_purpose && (
                  <div className="invalid-feedback">{errors.server_purpose}</div>
                )}
              </div>
            </div>
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">Status</label>
                <select
                  name="is_active"
                  value={String(formData.is_active)}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>
          </div>

          {/* Third Row */}
          <div className="row mb-3">
            <div className="col-md-12">
              <div className="form-group">
                <label className="form-label">Comments</label>
                <textarea
                  name="comments"
                  value={formData.comments}
                  onChange={handleInputChange}
                  className={`form-control ${errors.comments ? "is-invalid" : ""}`}
                  rows={3}
                />
                {errors.comments && (
                  <div className="invalid-feedback">{errors.comments}</div>
                )}
              </div>
            </div>
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading || !formData.server_name.trim()}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Adding...
                </>
              ) : "Add Server"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddServerModal;