import React, { useState, useEffect, useRef } from "react";
import Header from "../../../layouts/Header/components/Header";
import Sidebar from "../../../components/Sidebar";
import apiClient from "../../../core/config/api";
import AddServerModal from "./AddServerModal";
import Pagination from "../../../components/Pagination";
import DeleteConfirmationModal from "../../user/components/DeleteConfirmationModal";
import EditServerModal from "./EditServerModal";
import DropdownMenuPortal from "../../email/components/DropdownMenuPortal";
import { DeleteIcon, EditIcon, ActivateIcon, InactivateIcon } from "../../../components/ActionIcons";

interface ServerType {
  id: number;
  server_name: string;
  server_url: string;
  domain_ip: string;
  server_purpose: string;
  server_password: string;
  comments: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const Servers: React.FC = () => {
  const [servers, setServers] = useState<ServerType[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedServer, setSelectedServer] = useState<ServerType | null>(null);
  const [serverToDelete, setServerToDelete] = useState<ServerType | null>(null);
  const [activeCount, setActiveCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});
  // New state for selected servers and check all
  const [selectedServers, setSelectedServers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [totalServerIds, setTotalServerIds] = useState<number[]>([]);
  // State for bulk delete modal
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  // State for bulk activate modal
  const [isBulkActivateModalOpen, setIsBulkActivateModalOpen] = useState(false);
  // State for bulk inactivate modal
  const [isBulkInactivateModalOpen, setIsBulkInactivateModalOpen] = useState(false);
  // Calculate the starting number for the current page
  const startingNumber = (currentPage - 1) * dataPerPage;
  const [bulkActionsOpen, setBulkActionsOpen] = useState(false);
  const [loadingAllIds, setLoadingAllIds] = useState(false);

  const fetchActiveCount = async () => {
    try {
      const response = await apiClient.get("/servers/count/active");
      setActiveCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch active servers count:", error);
    }
  };

  const fetchServers = async () => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {
        page: currentPage,
        page_size: dataPerPage,
      };

      if (searchTerm) {
        params.server_name_contains = searchTerm;
      }

      const response = await apiClient.get("/servers", { params });
      setServers(response.data.data);
      setTotalPages(response.data.total_pages);
    } catch (err) {
      setError("Failed to fetch servers. Please try again later.");
      console.error("Failed to fetch servers:", err);
    } finally {
      setLoading(false);
    }
  };

  // New function to fetch all server IDs
  const fetchAllServerIds = async () => {
    setLoadingAllIds(true);
    try {
      // Instead of using the /count endpoint, get the total from the first page response
      const params: any = {
        page: 1,
        page_size: 100,
      };

      if (searchTerm) {
        params.server_name_contains = searchTerm;
      }

      // Get first page and extract total count from response
      const firstPageResponse = await apiClient.get("/servers", { params });
      const totalCount = firstPageResponse.data.total || 0;

      const pageSize = 100; // Use a reasonable page size
      const totalPagesToFetch = Math.ceil(totalCount / pageSize);

      // Start with IDs from first page
      let allIds: number[] = firstPageResponse.data.data.map((server: ServerType) => server.id);

      // Fetch remaining pages if needed
      for (let page = 2; page <= totalPagesToFetch; page++) {
        const nextParams: any = {
          page: page,
          page_size: pageSize,
        };

        if (searchTerm) {
          nextParams.server_name_contains = searchTerm;
        }

        const response = await apiClient.get("/servers", { params: nextParams });
        const pageIds = response.data.data.map((server: ServerType) => server.id);
        allIds = [...allIds, ...pageIds];
      }

      setTotalServerIds(allIds);
      return allIds;
    } catch (err) {
      console.error("Failed to fetch all server IDs:", err);
      return [];
    } finally {
      setLoadingAllIds(false);
    }
  };

  useEffect(() => {
    fetchServers();
    fetchActiveCount();
    // Fetch all server IDs when search term changes
    fetchAllServerIds();
  }, [currentPage, dataPerPage, searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
    setSelectedServers([]);
    setSelectAll(false);
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    setCurrentPage(1);
    setSelectedServers([]);
    setSelectAll(false);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setDataPerPage(newPerPage);
    setCurrentPage(1);
  };

  const handleAddServer = async () => {
    try {
      await fetchServers();
      fetchActiveCount();
      await fetchAllServerIds();
      setIsAddModalOpen(false);
    } catch (err) {
      setError("Failed to add server. Please try again.");
      console.error("Failed to add server:", err);
    }
  };

  const handleUpdateServer = (updatedServer: ServerType) => {
    setServers(servers.map(server =>
      server.id === updatedServer.id ? updatedServer : server
    ));
    fetchActiveCount();
    setIsEditModalOpen(false);
  };

  const handleDeleteConfirm = async () => {
    if (!serverToDelete) return;

    try {
      await apiClient.delete(`/servers/${serverToDelete.id}`);
      setServers(servers.filter(server => server.id !== serverToDelete.id));
      fetchActiveCount();
      await fetchAllServerIds();
      setIsDeleteModalOpen(false);

      // Remove the deleted server from selected servers if it was selected
      if (selectedServers.includes(serverToDelete.id)) {
        setSelectedServers(prev => prev.filter(id => id !== serverToDelete.id));
      }
    } catch (err) {
      setError("Failed to delete server. Please try again.");
      console.error("Failed to delete server:", err);
    }
  };

  // Handle checkbox selection
  const handleSelectServer = (id: number) => {
    setSelectedServers(prev => {
      if (prev.includes(id)) {
        setSelectAll(false);
        return prev.filter(serverId => serverId !== id);
      } else {
        const newSelected = [...prev, id];
        // Check if all servers on the current page are selected
        const allCurrentPageSelected = servers.every(server =>
          newSelected.includes(server.id)
        );
        // Check if all servers across all pages are selected
        const allServersSelected = totalServerIds.length > 0 &&
          totalServerIds.every(id => newSelected.includes(id));

        if (allServersSelected) {
          setSelectAll(true);
        }

        return newSelected;
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = async () => {
    if (selectAll) {
      // Immediate UI feedback
      setSelectAll(false);
      setSelectedServers([]);
    } else {
      // Immediate UI feedback
      setSelectAll(true);
      setLoading(true);

      try {
        // Get all server IDs if we don't have them yet
        let allIds = totalServerIds;
        if (allIds.length === 0) {
          allIds = await fetchAllServerIds();
        }

        // Update selected servers with all IDs
        setSelectedServers(allIds);
      } catch (err) {
        console.error("Failed to select all servers:", err);
        // If there was an error, revert the UI state
        setSelectAll(false);
      } finally {
        setLoading(false);
      }
    }
  };

  // Bulk activate/deactivate selected servers
  const handleBulkStatusChange = async (activate: boolean) => {
    if (selectedServers.length === 0) return;

    setLoading(true);
    try {
      // Process each selected server
      const updatePromises = selectedServers.map(serverId => {
        return apiClient.patch(`/servers/${serverId}`, { is_active: activate });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchServers();
      fetchActiveCount();
      // Don't reset selections to maintain multi-page selections
      // setSelectedServers([]);
      // setSelectAll(false);
    } catch (err) {
      setError(`Failed to ${activate ? 'activate' : 'deactivate'} servers. Please try again.`);
      console.error(`Failed to ${activate ? 'activate' : 'deactivate'} servers:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk activate for selected servers
  const handleBulkActivate = () => {
    if (selectedServers.length === 0) return;
    setIsBulkActivateModalOpen(true);
  };

  // Handle bulk inactivate for selected servers
  const handleBulkInactivate = () => {
    if (selectedServers.length === 0) return;
    setIsBulkInactivateModalOpen(true);
  };

  // Handle bulk delete for selected servers
  const handleBulkDelete = () => {
    if (selectedServers.length === 0) return;
    setIsBulkDeleteModalOpen(true);
  };

  // Handle bulk activate confirmation
  const handleBulkActivateConfirm = async () => {
    if (selectedServers.length === 0) return;

    setLoading(true);
    try {
      // Process each selected server
      const updatePromises = selectedServers.map(serverId => {
        return apiClient.put(`/servers/${serverId}`, { is_active: true });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchServers();
      fetchActiveCount();
      setSelectedServers([]);
      setSelectAll(false);
      setIsBulkActivateModalOpen(false);
    } catch (err) {
      setError(`Failed to activate servers. Please try again.`);
      console.error(`Failed to activate servers:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk inactivate confirmation
  const handleBulkInactivateConfirm = async () => {
    if (selectedServers.length === 0) return;

    setLoading(true);
    try {
      // Process each selected server
      const updatePromises = selectedServers.map(serverId => {
        return apiClient.put(`/servers/${serverId}`, { is_active: false });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchServers();
      fetchActiveCount();
      setSelectedServers([]);
      setSelectAll(false);
      setIsBulkInactivateModalOpen(false);
    } catch (err) {
      setError(`Failed to inactivate servers. Please try again.`);
      console.error(`Failed to inactivate servers:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk delete confirmation
  const handleBulkDeleteConfirm = async () => {
    if (selectedServers.length === 0) return;

    setLoading(true);
    try {
      // Process each selected server
      const deletePromises = selectedServers.map(serverId => {
        return apiClient.delete(`/servers/${serverId}`);
      });

      await Promise.all(deletePromises);

      // Refresh the data
      await fetchServers();
      fetchActiveCount();
      await fetchAllServerIds();
      setSelectedServers([]);
      setSelectAll(false);
      setIsBulkDeleteModalOpen(false);
    } catch (err) {
      setError(`Failed to delete servers. Please try again.`);
      console.error(`Failed to delete servers:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Click outside handler to close dropdown menu
  const handleClickOutside = (event: MouseEvent) => {
    if (openActionMenuId !== null) {
      let clickedInsideDropdown = false;

      // Check if click was inside the current open dropdown button
      const openButtonRef = buttonRefs.current[openActionMenuId];
      if (openButtonRef && openButtonRef.contains(event.target as Node)) {
        clickedInsideDropdown = true;
      }

      // Check if click was inside dropdown menu
      const dropdownElements = document.querySelectorAll('.dropdown-portal');
      dropdownElements.forEach(element => {
        if (element.contains(event.target as Node)) {
          clickedInsideDropdown = true;
        }
      });

      // Also check for custom dropdown items
      const dropdownItemElements = document.querySelectorAll('.custom-dropdown-item');
      dropdownItemElements.forEach(element => {
        if (element.contains(event.target as Node)) {
          clickedInsideDropdown = true;
        }
      });

      if (!clickedInsideDropdown) {
        setOpenActionMenuId(null);
      }
    }
  };

  // Add click outside listener
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openActionMenuId]);

  // Handle button click - either open modal or toggle dropdown
  const handleButtonClick = () => {
    if (selectedServers.length > 0) {
      // If items are selected, toggle the bulk actions dropdown
      setBulkActionsOpen(!bulkActionsOpen);
    } else {
      // If no items selected, directly open the Add Server modal
      setIsAddModalOpen(true);
    }
  };

  // Close bulk actions dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById('actionsDropdown');
      const dropdownMenu = document.querySelector('.dropdown-menu.show');

      // Check if click is outside both the button and the dropdown menu
      if (bulkActionsOpen &&
          dropdown &&
          !dropdown.contains(event.target as Node) &&
          dropdownMenu &&
          !dropdownMenu.contains(event.target as Node)) {
        setBulkActionsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [bulkActionsOpen]);

  // Get page title with selected count
  const getPageTitle = () => {
    let title = `Server List`;

    if (activeCount > 0) {
      title += ` (${activeCount})`;
    }

    return title;
  };

  return (
    <div className="dashboard-container">
      <style>
        {`
          #actionsDropdown {
            border-radius: 0.5rem !important;
          }
          #actionsDropdown.dropdown-toggle {
            border-radius: 0.5rem !important;
          }
        `}
      </style>
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0 text-white">
                <i className="fas fa-server me-2"></i>{getPageTitle()}
              </h5>
            </div>
          </div>
          <div className="card-body p-3">
            <div className="filters-container mb-3">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search server..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={handleClearSearch}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>

              <div className="filter-group right-filters">
                {/* Single unified dropdown for all actions */}
                <div className="position-relative d-inline-block">
                  <button
                    className={`btn btn-primary ${selectedServers.length > 0 ? 'dropdown-toggle' : ''}`}
                    type="button"
                    id="actionsDropdown"
                    onClick={handleButtonClick}
                    disabled={loading}
                    style={{
                      border: 'none',
                      borderRadius: '0.5rem !important',
                      padding: '0.375rem 0.75rem',
                      fontSize: '0.875rem',
                      fontWeight: '400'
                    }}
                  >
                    {selectedServers.length > 0 ? (
                      <>Bulk Actions ({selectedServers.length})</>
                    ) : (
                      <><i className="fas fa-plus me-1"></i> Add Server</>
                    )}
                  </button>
                  {bulkActionsOpen && selectedServers.length > 0 && (
                    <div
                      className="dropdown-menu show"
                      style={{
                        position: 'absolute',
                        top: '100%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        minWidth: '180px',
                        padding: '0.5rem 0',
                        margin: '0.125rem 0 0',
                        backgroundColor: '#fff',
                        border: '1px solid rgba(0,0,0,.15)',
                        borderRadius: '0.375rem',
                        boxShadow: '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
                        zIndex: 1000
                      }}
                    >
                      {/* Show bulk actions only when items are selected */}
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkActivate();
                          setBulkActionsOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#212529',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <ActivateIcon width="16" height="16" fill="#28a745" />
                        <span className="ms-2">Activate Selected</span>
                      </button>
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkInactivate();
                          setBulkActionsOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#212529',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <InactivateIcon width="16" height="16" fill="#dc3545" />
                        <span className="ms-2">Inactivate Selected</span>
                      </button>
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkDelete();
                          setBulkActionsOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#D9363E',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <DeleteIcon width="16" height="16" fill="#D9363E" />
                        <span className="ms-2">Delete Selected</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {error && <div className="error-message">{error}</div>}

            <div className="table-responsive">
              <table className="servers-table" style={{ tableLayout: 'fixed', width: '100%' }}>
                <thead>
                  <tr style={{ background: "#4d7a8c" }}>
                    <th style={{ width: '60px', padding: '0.75rem 0', textAlign: 'center' }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: '0 auto',
                        width: '100%'
                      }}>
                        <div style={{
                          width: '18px',
                          height: '18px',
                          backgroundColor: selectAll ? '#00A3CC' : 'transparent',
                          border: selectAll ? 'none' : '1px solid #dee2e6',
                          borderRadius: '3px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={handleSelectAll}
                        >
                          {selectAll && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                              <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                            </svg>
                          )}
                        </div>
                      </div>
                    </th>
                    <th style={{ width: '80px', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>ID</th>
                    <th style={{ width: '18%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Name</th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Primary IP</th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Purpose</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Status</th>
                    <th style={{ width: '20%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Updated At</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="text-center">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : servers.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center">
                        {searchTerm ?
                          'No servers found matching your search' :
                          'No servers found'}
                      </td>
                    </tr>
                  ) : (
                    servers.map((server, i) => (
                      <tr key={server.id}>
                        <td style={{ width: '60px', padding: '0.5rem' }}>
                          <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: '100%'
                          }}>
                            <div style={{
                              width: '18px',
                              height: '18px',
                              backgroundColor: selectedServers.includes(server.id) ? '#00A3CC' : 'transparent',
                              border: selectedServers.includes(server.id) ? 'none' : '1px solid #dee2e6',
                              borderRadius: '3px',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              cursor: 'pointer'
                            }}
                            onClick={() => handleSelectServer(server.id)}
                            >
                              {selectedServers.includes(server.id) && (
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                                  <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                                </svg>
                              )}
                            </div>
                          </div>
                        </td>
                        <td style={{ width: '80px', padding: '0.5rem 0.75rem' }}>#{startingNumber + i + 1}</td>
                        <td style={{ width: '18%', padding: '0.5rem 0.75rem' }}>{server.server_name}</td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem' }}>{server.domain_ip}</td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem' }}>{server.server_purpose}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem' }}>
                          <span className={`badge bg-${server.is_active ? 'success' : 'danger'}`}>
                            {server.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td style={{ width: '20%', padding: '0.5rem 0.75rem' }}>{new Date(server.updated_at).toLocaleDateString('en-US', {
                          month: '2-digit',
                          day: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit',
                          hour12: true
                        })}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem' }}>
                          <div className="custom-dropdown-container">
                            <button
                              ref={(el) => { buttonRefs.current[server.id] = el }}
                              type="button"
                              className="btn btn-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                setOpenActionMenuId(openActionMenuId === server.id ? null : server.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                                padding: '0.2rem 0.5rem',
                                fontSize: '0.75rem',
                                height: '24px',
                                width: '80px',
                                cursor: 'pointer',
                                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === server.id}
                              buttonElement={buttonRefs.current[server.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {/* Edit Option */}
                              <button
                                type="button"
                                className="dropdown-item custom-dropdown-item"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedServer(server);
                                  setIsEditModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.375rem 1rem',
                                  fontSize: '0.875rem',
                                  cursor: 'pointer',
                                  borderRadius: '0'
                                }}
                              >
                                <EditIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">Edit</span>
                              </button>

                              <button
                                type="button"
                                className="dropdown-item custom-dropdown-item"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setServerToDelete(server);
                                  setIsDeleteModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.375rem 1rem',
                                  fontSize: '0.875rem',
                                  cursor: selectedServers.length > 0 ? 'not-allowed' : 'pointer',
                                  borderRadius: '0',
                                  opacity: selectedServers.length > 0 ? '0.5' : '1'
                                }}
                                disabled={selectedServers.length > 0}
                              >
                                <DeleteIcon width="16" height="16" fill={selectedServers.length > 0 ? "#999" : "#D9363E"} />
                                <span className="ms-2" style={{ color: selectedServers.length > 0 ? '#999' : 'inherit' }}>Delete</span>
                              </button>
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={handlePerPageChange}
              perPage={dataPerPage}
            />
          </div>
        </div>

        <AddServerModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onAddSuccess={handleAddServer}
        />

        {isEditModalOpen && selectedServer && (
          <EditServerModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onEditSuccess={handleUpdateServer}
            server={selectedServer}
          />
        )}

        {isDeleteModalOpen && serverToDelete && (
          <DeleteConfirmationModal
            isOpen={isDeleteModalOpen}
            onClose={() => {
              setIsDeleteModalOpen(false);
              setServerToDelete(null);
            }}
            onConfirm={handleDeleteConfirm}
            userName={serverToDelete.server_name}
          />
        )}

        {/* Bulk Activate Confirmation Modal */}
        {isBulkActivateModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkActivateModalOpen}
            onClose={() => setIsBulkActivateModalOpen(false)}
            onConfirm={handleBulkActivateConfirm}
            userName={`${selectedServers.length} selected servers`}
            entityType="Server"
            actionType="activate"
          />
        )}

        {/* Bulk Inactivate Confirmation Modal */}
        {isBulkInactivateModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkInactivateModalOpen}
            onClose={() => setIsBulkInactivateModalOpen(false)}
            onConfirm={handleBulkInactivateConfirm}
            userName={`${selectedServers.length} selected servers`}
            entityType="Server"
            actionType="inactivate"
          />
        )}

        {/* Bulk Delete Confirmation Modal */}
        {isBulkDeleteModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkDeleteModalOpen}
            onClose={() => setIsBulkDeleteModalOpen(false)}
            onConfirm={handleBulkDeleteConfirm}
            userName={`${selectedServers.length} selected servers`}
            entityType="Server"
          />
        )}
      </div>
    </div>
  );
};

export default Servers;