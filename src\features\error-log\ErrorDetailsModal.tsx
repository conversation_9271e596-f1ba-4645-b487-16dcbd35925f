import React, { useEffect, useState } from 'react';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
}

interface RequestHistory {
  id: number;
  project_name: string | null;
  request_type: string;
  status: string;
  created_at: string;
}

interface ErrorLog {
  id: number;
  user_id: number | null;
  request_history_id: number | null;
  request_origin: string;
  level: string;
  error_code: string | null;
  message: string;
  details: Record<string, unknown>;
  stack_trace: string | null;
  created_at: string;
  user: User | null;
  request_history: RequestHistory | null;
}

interface ErrorDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  errorLog: ErrorLog | null;
}

const ErrorDetailsModal: React.FC<ErrorDetailsModalProps> = ({
  isOpen,
  onClose,
  errorLog,
}) => {
  const [activeTab, setActiveTab] = useState<'details' | 'stack'>('details');

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen || !errorLog) return null;

  const getLevelBadgeClass = (level: string) => {
    switch (level.toLowerCase()) {
      case 'error': return 'badge bg-danger';
      case 'warning': return 'badge bg-warning text-dark';
      case 'info': return 'badge bg-info text-dark';
      case 'debug': return 'badge bg-secondary';
      default: return 'badge bg-primary';
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'badge bg-success';
      case 'failed': return 'badge bg-danger';
      case 'processing': return 'badge bg-warning text-dark';
      case 'pending': return 'badge bg-secondary';
      default: return 'badge bg-primary';
    }
  };

  const getRequestTypeBadgeClass = (type: string) => {
    switch (type.toLowerCase()) {
      case 'verifier': return 'badge bg-info text-dark';
      case 'finder': return 'badge bg-primary';
      default: return 'badge bg-secondary';
    }
  };

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const modalOverlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1050,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'auto'
  };

  const modalStyle: React.CSSProperties = {
    backgroundColor: 'white',
    borderRadius: '6px',
    maxWidth: '800px',
    width: '100%',
    margin: '30px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)',
    display: 'flex',
    flexDirection: 'column'
  };

  const headerStyle: React.CSSProperties = {
    background: "#164966",
    color: "white",
    padding: '1rem 1.5rem',
    borderTopLeftRadius: '6px',
    borderTopRightRadius: '6px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  };

  return (
    <div style={modalOverlayStyle} onClick={handleBackdropClick}>
      <div style={modalStyle} onClick={e => e.stopPropagation()}>
        <div style={headerStyle}>
          <h5 className="m-0">Log Details</h5>
          <button
            type="button"
            className="btn-close btn-close-white"
            aria-label="Close"
            onClick={onClose}
          />
        </div>

        <div style={{ padding: '1.5rem' }}>
          {/* First row - Request ID (left) and Level (right) */}
          <div className="row mb-3">
            <div className="col-md-3 text-start">
              {errorLog.request_history && (
                <>
                  <strong>Status:</strong> <span className={getStatusBadgeClass(errorLog.request_history.status)}>
                    {errorLog.request_history.status.toUpperCase()}
                  </span>
                </>
              )}
            </div>
            <div className="col-md-3 text-center">
              {errorLog.request_history && (
                <>
                  <strong>Request Type:</strong> <span className={getRequestTypeBadgeClass(errorLog.request_history.request_type)}>
                    {errorLog.request_history.request_type.toUpperCase()}
                  </span>
                </>
              )}
            </div>
            <div className="col-md-3 text-end">
              <strong>Log Level:</strong> <span className={getLevelBadgeClass(errorLog.level)}>
                {errorLog.level.toUpperCase()}
              </span>
            </div>
          </div>

          {/* Third row - User (left) and Created At (right) */}
          <div className="row mb-3">
            <div className="col-md-6 text-start">
              <strong>User:</strong> {errorLog.request_origin !== "unknown" ? errorLog.request_origin : errorLog?.user?.email || 'N/A'}
            </div>
            <div className="col-md-6 text-end">
              <strong>Created At:</strong> {new Date(errorLog.created_at).toLocaleString()}
            </div>
          </div>

          {/* Project Name (full width when present) */}
          {errorLog.request_history?.project_name && (
            <div className="row mb-3">
              <div className="col-12 text-start">
                <strong>Project:</strong> {errorLog.request_history.project_name}
              </div>
            </div>
          )}

          {/* Message (full width) */}
          <div className="row mb-3">
            <div className="col-12 text-start">
              <strong>Message:</strong> {errorLog.message}
            </div>
          </div>

          {/* Tabs section */}
          <div className="mt-4">
            <ul className="nav nav-tabs">
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'details' ? 'active' : ''}`}
                  onClick={() => setActiveTab('details')}
                >
                  Details
                </button>
              </li>
              {errorLog.stack_trace && (
                <li className="nav-item">
                  <button
                    className={`nav-link ${activeTab === 'stack' ? 'active' : ''}`}
                    onClick={() => setActiveTab('stack')}
                  >
                    Stack Trace
                  </button>
                </li>
              )}
            </ul>

            <div className="tab-content mt-3">
              {activeTab === 'details' && (
                <div className="tab-pane active">
                  <pre className="bg-light p-3" style={{
                    maxHeight: '200px',
                    overflowY: 'auto',
                    borderRadius: '4px',
                    fontSize: '13px',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {JSON.stringify(errorLog.details, null, 2)}
                  </pre>
                </div>
              )}

              {activeTab === 'stack' && errorLog.stack_trace && (
                <div className="tab-pane active">
                  <pre className="bg-light p-3" style={{
                    maxHeight: '200px',
                    overflowY: 'auto',
                    borderRadius: '4px',
                    fontSize: '13px',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {errorLog.stack_trace}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>

        <div style={{
          padding: '1rem 1.5rem',
          borderTop: '1px solid #dee2e6',
          display: 'flex',
          justifyContent: 'flex-end'
        }}>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorDetailsModal;