import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AuthResponse } from "../../../types/models/auth";

interface AuthState {
  user: {
    email: string;
    first_name: string;
    last_name?: string;
    role_id: number;
  } | null;
  accessToken: string | null;
  refreshToken: string | null;
  roleId: number | null;
  isLoading: boolean;
}

const initialState: AuthState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  roleId: null,
  isLoading: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<AuthResponse>) => {
      // Map the API response to the Redux state
      state.user = {
        email: action.payload.email,
        first_name: action.payload.first_name,
        last_name: action.payload.last_name,
        role_id: action.payload.role_id,
      };
      state.accessToken = action.payload.access_token;
      state.refreshToken = action.payload.refresh_token || null;
      state.roleId = action.payload.role_id;
      state.isLoading = false;

      // Store data in localStorage as plain values
      // localStorage.setItem("user", JSON.stringify(state.user)); 
      localStorage.setItem("accessToken", action.payload.access_token);
      localStorage.setItem("refreshToken", action.payload.refresh_token || "");
      localStorage.setItem("roleId", String(action.payload.role_id));
    },
    logout: (state) => {
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.roleId = null;
      state.isLoading = false;

      // Clear localStorage on logout
      localStorage.removeItem("user");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("refreshToken");
      localStorage.removeItem("roleId");
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

export const { setCredentials, logout, setLoading } = authSlice.actions;
export default authSlice.reducer;