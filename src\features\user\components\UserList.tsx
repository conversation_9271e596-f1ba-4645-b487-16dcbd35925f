import React, { useEffect, useRef, useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useNavigate } from "react-router-dom";
import Pagination from "../../../components/Pagination";
import Sidebar from "../../../components/Sidebar";
import apiClient from "../../../core/config/api";
import Header from "../../../layouts/Header/components/Header";
import { User } from "../../../types/models/UserType";
import DropdownMenuPortal from '../../email/components/DropdownMenuPortal';
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import EditUserModal from "./EditUserModal";
import { DeleteIcon, EditIcon, ViewCreditsIcon, ViewReportsIcon } from "../../../components/ActionIcons";

interface EnhancedUser extends User {
  used_today: number;
  remaining: number;
}

const UserList: React.FC = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState<EnhancedUser[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage, setUsersPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<Date | null | undefined>(null);
  const [endDate, setEndDate] = useState<Date | null | undefined>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [activeUserCount, setActiveUserCount] = useState(0);
  const [sortConfig, setSortConfig] = useState({
    field: "first_name",
    direction: "asc" as "asc" | "desc",
    role: "all" as number | "all",
    accountStatus: null as string | null,
  });
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [tableKey, setTableKey] = useState(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  const fetchActiveUserCount = async () => {
    try {
      const response = await apiClient.get("/user/count/active");
      setActiveUserCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch active user count:", error);
      setError("Failed to fetch active user count. Please try again.");
    }
  };

  const fetchUsers = async () => {
    if (sortConfig.field === 'remaining') {
      await fetchSortedUsersByRemaining(sortConfig.direction, sortConfig.accountStatus);
    } else {
      await fetchUsersBySorting(sortConfig.field, sortConfig.direction, sortConfig.accountStatus);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchActiveUserCount();
  }, [currentPage, usersPerPage, searchQuery, sortConfig, startDate, endDate]);

  const handleDelete = async (id: number) => {
    try {
      await apiClient.delete(`/user/${id}`);
      setUsers(users.filter(user => user.id !== id));
      fetchActiveUserCount();
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : (error as any)?.response?.data?.message || 'Failed to delete user. Please try again.';
      setError(errorMessage);
    }
  };

  const handleUpdateUser = (updatedUser: User) => {
    setUsers(users.map(user => user.id === updatedUser.id ? { ...updatedUser, used_today: user.used_today, remaining: user.remaining } : user));
    fetchActiveUserCount();
  };

  const getRoleName = (roleId: number) => roleId === 1 ? "Admin" : "User";

  const handlePerPageChange = (newPerPage: number) => {
    setUsersPerPage(newPerPage);
    setCurrentPage(1);
  };

  const getContactName = (firstName: string, lastName: string | null) =>
    `${firstName || ''} ${lastName || ''}`.trim();

  const formatNumber = (num: number): string =>
    num >= 1000 ? num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") : num.toString();

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;

    const sortOptions: Record<string, { field: string; direction: string; status?: string }> = {
      name_asc: { field: "first_name", direction: "asc" },
      name_desc: { field: "first_name", direction: "desc" },
      limit_asc: { field: "max_upload_limit", direction: "asc" },
      limit_desc: { field: "max_upload_limit", direction: "desc" },
      remaining_asc: { field: "remaining", direction: "asc" },
      remaining_desc: { field: "remaining", direction: "desc" },
      created_at_asc: { field: "created_at", direction: "asc" },
      created_at_desc: { field: "created_at", direction: "desc" },
      status_active: { field: "first_name", direction: "asc", status: "active" },
      status_inactive: { field: "first_name", direction: "asc", status: "inactive" }
    };

    const selectedOption = sortOptions[value] || sortOptions.name_asc;

    setSortConfig({
      ...sortConfig,
      field: selectedOption.field,
      direction: selectedOption.direction as "asc" | "desc",
      accountStatus: selectedOption.status || null
    });
  };

  const fetchUsersBySorting = async (field: string, direction: string, accountStatus: string | null) => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {
        page: currentPage,
        page_size: usersPerPage,
        field,
        direction
      };

      if (searchQuery) params.email_contains = searchQuery;
      if (sortConfig.role !== "all") params.role_id = sortConfig.role;
      if (accountStatus) params.account_status = accountStatus;

      if (startDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        params.created_after = start.toISOString();
      }

      if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        params.created_before = end.toISOString();
      }

      const response = await apiClient.get("/user", { params });
      setUsers(response.data.data || []);
      setTotalPages(response.data.total_pages || 1);
      setCurrentPage(response.data.page || 1);
    } catch (error) {
      console.error("Failed to fetch sorted users:", error);
      setError("Failed to fetch users. Please try again or contact support.");
    } finally {
      setLoading(false);
    }
  };

  const fetchSortedUsersByRemaining = async (direction: string, accountStatus: string | null) => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {
        page: currentPage,
        page_size: usersPerPage
      };

      if (searchQuery) params.email_contains = searchQuery;
      if (sortConfig.role !== "all") params.role_id = sortConfig.role;
      if (accountStatus) params.account_status = accountStatus;

      if (startDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        params.created_after = start.toISOString();
      }

      if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        params.created_before = end.toISOString();
      }

      const response = await apiClient.get(`/user/with-limits/sort-by-remaining/${direction}`, { params });
      setUsers(response.data.data || []);
      setTotalPages(response.data.total_pages || 1);
      setCurrentPage(response.data.page || 1);
    } catch (error) {
      console.error("Failed to fetch users sorted by remaining credits:", error);
      setError("Failed to sort users by remaining credits. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const getSortSelectValue = () => {
    if (sortConfig.accountStatus === "active") return "status_active";
    if (sortConfig.accountStatus === "inactive") return "status_inactive";

    const sortMap: Record<string, string> = {
      "first_name_asc": "name_asc",
      "first_name_desc": "name_desc",
      "max_upload_limit_asc": "limit_asc",
      "max_upload_limit_desc": "limit_desc",
      "remaining_asc": "remaining_asc",
      "remaining_desc": "remaining_desc",
      "created_at_asc": "created_at_asc",
      "created_at_desc": "created_at_desc"
    };

    return sortMap[`${sortConfig.field}_${sortConfig.direction}`] || "name_asc";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const dropdownActions = [
    {
      label: "View Reports",
      icon: <ViewReportsIcon width="16" height="16" fill="#0082A3" style={{ minWidth: '16px', display: 'block' }} />,
      action: (user: User) => navigate(`/user-reports/${user.id}`)
    },
    {
      label: "View Credits",
      icon: <ViewCreditsIcon width="16" height="16" fill="#0082A3" style={{ minWidth: '16px', display: 'block' }} />,
      action: (user: User) => navigate(`/view-credit-history/${user.id}`)
    },
    {
      label: "Edit",
      icon: <EditIcon width="16" height="16" fill="#0082A3" style={{ minWidth: '16px', display: 'block' }} />,
      action: (user: User) => {
        setSelectedUser(user);
        setIsEditModalOpen(true);
      }
    },
    {
      label: "Delete",
      icon: <DeleteIcon width="16" height="16" fill="#D9363E" style={{ minWidth: '16px', display: 'block' }} />,
      action: (user: User) => {
        setUserToDelete(user);
        setIsDeleteModalOpen(true);
      }
    }
  ];

  return (
    <div className="dashboard-container user-management-dashboard">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <h5 className="mb-0 text-white">
              <i className="fas fa-users me-2"></i>Users List
              {activeUserCount > 0 && <span>({activeUserCount})</span>}
            </h5>
          </div>

          <div className="card-body p-3">
            {error && (
              <div className="alert alert-danger" role="alert">
                {error}
                <button
                  type="button"
                  className="btn-close float-end"
                  onClick={() => setError(null)}
                  aria-label="Close"
                ></button>
              </div>
            )}

            <div className="filters-container">
              <div className="filter-row">
                <div className="filter-group search-group">
                  <input
                    type="text"
                    placeholder="Search by email..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="search-input"
                  />
                  {searchQuery && (
                    <button
                      className="clear-search"
                      onClick={() => setSearchQuery('')}
                      aria-label="Clear search"
                    >
                      &times;
                    </button>
                  )}
                </div>
              </div>

              <div className="filter-row">
                <div className="date-filters-container">
                  <div className="date-picker-container">
                    <DatePicker
                      selected={startDate || undefined}
                      onChange={(date) => {
                        const today = new Date();
                        today.setHours(23, 59, 59, 999);
                        const selectedDate = date || null;

                        if (selectedDate && selectedDate > today) return;
                        if (selectedDate && endDate && selectedDate > endDate) {
                          setEndDate(selectedDate);
                        }

                        setStartDate(selectedDate);
                      }}
                      selectsStart
                      startDate={startDate || undefined}
                      endDate={endDate || undefined}
                      maxDate={new Date()}
                      className="form-control"
                      placeholderText="Select start date"
                      dateFormat="MM/dd/yyyy"
                      isClearable
                    />
                  </div>

                  <div className="date-picker-container">
                    <DatePicker
                      selected={endDate || undefined}
                      onChange={(date) => {
                        const today = new Date();
                        today.setHours(23, 59, 59, 999);
                        const selectedDate = date || null;

                        if (selectedDate && selectedDate > today) return;
                        if (selectedDate && startDate && selectedDate < startDate) return;

                        setEndDate(selectedDate);
                      }}
                      selectsEnd
                      startDate={startDate || undefined}
                      endDate={endDate || undefined}
                      minDate={startDate || undefined}
                      maxDate={new Date()}
                      className="form-control"
                      placeholderText="Select end date"
                      dateFormat="MM/dd/yyyy"
                      isClearable
                    />
                  </div>
                </div>

                <div className="filter-select-container text-end">
                  <select
                    value={getSortSelectValue()}
                    onChange={handleSortChange}
                    className="filter-select"
                  >
                    <option value="name_asc">Contact Name (A-Z)</option>
                    <option value="name_desc">Contact Name (Z-A)</option>
                    <option value="limit_asc">Daily Limit (Low to High)</option>
                    <option value="limit_desc">Daily Limit (High to Low)</option>
                    <option value="remaining_asc">Remaining Credits (Low to High)</option>
                    <option value="remaining_desc">Remaining Credits (High to Low)</option>
                    <option value="created_at_asc">Created Date (Ascending)</option>
                    <option value="created_at_desc">Created Date (Descending)</option>
                    <option value="status_active">Active</option>
                    <option value="status_inactive">Inactive</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="table-responsive">
              <table className="users-table" key={tableKey}>
                <thead>
                  <tr>
                    <th className="name-col">Contact Name</th>
                    <th className="email-col">Email</th>
                    <th className="role-col">Role</th>
                    <th className="status-col">Status</th>
                    <th className="max-limit-col">Daily Upload Limit</th>
                    <th className="remaining-limit-col">Remaining</th>
                    <th className="date-col">Created At</th>
                    <th className="action-col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="text-center">Loading...</td>
                    </tr>
                  ) : users.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center">No users found</td>
                    </tr>
                  ) : (
                    users.map((user) => (
                      <tr key={user.id}>
                        <td className="name-col">{getContactName(user.first_name, user.last_name)}</td>
                        <td className="email-col">{user.email}</td>
                        <td className="role-col">{getRoleName(user.role_id)}</td>
                        <td className="status-col">{user.account_status}</td>
                        <td className="max-limit-col">{formatNumber(user.max_upload_limit || 0)}</td>
                        <td className="remaining-limit-col">
                          {formatNumber(user.remaining || 0)}
                        </td>
                        <td className="date-col">{formatDate(user.created_at)}</td>
                        <td className="action-col">
                          <div className="dropdown" style={{ position: 'relative' }}>
                            <button
                              ref={(el: HTMLButtonElement | null) => {
                                buttonRefs.current[user.id] = el;
                              }}
                              type="button"
                              className="btn btn-outline-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenActionMenuId(openActionMenuId === user.id ? null : user.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                borderRadius: '0',
                              }}
                            >
                              <span>Actions</span>
                            </button>
                            <DropdownMenuPortal
                              isOpen={openActionMenuId === user.id}
                              buttonElement={buttonRefs.current[user.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '140px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {dropdownActions.map((action, index) => (
                                <button
                                  key={index}
                                  type="button"
                                  className="dropdown-item"
                                  onClick={() => {
                                    action.action(user);
                                    setOpenActionMenuId(null);
                                  }}
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    width: '100%',
                                    textAlign: 'left',
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    padding: '0.25rem 0.75rem',
                                    fontSize: '0.875rem',
                                    cursor: 'pointer',
                                    borderRadius: '0',
                                    gap: '6px',
                                    minHeight: '28px',
                                    overflow: 'visible'
                                  }}
                                >
                                  {action.icon}
                                  {action.label}
                                </button>
                              ))}
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={handlePerPageChange}
              perPage={usersPerPage}
            />

            {isEditModalOpen && (
              <EditUserModal
                isOpen={isEditModalOpen}
                onClose={() => setIsEditModalOpen(false)}
                user={selectedUser}
                onUpdate={handleUpdateUser}
              />
            )}

            {isDeleteModalOpen && userToDelete && (
              <DeleteConfirmationModal
                isOpen={isDeleteModalOpen}
                onClose={() => {
                  setIsDeleteModalOpen(false);
                  setUserToDelete(null);
                }}
                onConfirm={() => handleDelete(userToDelete.id)}
                userName={`${userToDelete.first_name} ${userToDelete.last_name}`}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserList;