import React, { useState, useEffect, useRef } from "react";
import Header from "../../../layouts/Header/components/Header";
import Sidebar from "../../../components/Sidebar";
import Pagination from "../../../components/Pagination";
import apiClient from "../../../core/config/api";
import AddRoleModal from "./AddRoleModal";
import EditRoleModal from "./EditRoleModal";
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import DropdownMenuPortal from '../../email/components/DropdownMenuPortal';
import { DeleteIcon, EditIcon } from "../../../components/ActionIcons";

interface UserRole {
  id: number;
  name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const UserRoles: React.FC = () => {
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [roleToDelete, setRoleToDelete] = useState<UserRole | null>(null);
  const [activeRolesCount, setActiveRolesCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{[key: number]: HTMLButtonElement | null}>({}); 

  const fetchRoles = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get("/user-roles/", {
        params: {
          page: currentPage,
          page_size: dataPerPage,
          search: searchQuery,
        },
      });
      setRoles(response.data);
      setTotalPages(Math.ceil(response.data.length / dataPerPage));
    } catch (error) {
      console.error("Failed to fetch roles:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchActiveRolesCount = async () => {
    try {
      const response = await apiClient.get("/user-roles/count/active");
      setActiveRolesCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch active roles count:", error);
    }
  };

  useEffect(() => {
    fetchRoles();
    fetchActiveRolesCount();
  }, [currentPage, dataPerPage, searchQuery]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setDataPerPage(newPerPage);
    setCurrentPage(1);
  };

  const handleDeleteConfirm = async () => {
    if (!roleToDelete) return;

    try {
      await apiClient.delete(`/user-roles/${roleToDelete.id}/`);
      setRoles(roles.filter((role) => role.id !== roleToDelete.id));
      setIsDeleteModalOpen(false);
      fetchActiveRolesCount();
    } catch (error) {
      console.error("Failed to delete role:", error);
    }
  };

  const handleAddSuccess = (newRole: UserRole) => {
    setRoles([...roles, newRole]);
    setIsAddModalOpen(false);
    fetchActiveRolesCount();
  };

  const handleEditSuccess = (updatedRole: UserRole) => {
    setRoles(roles.map(role => role.id === updatedRole.id ? updatedRole : role));
    setIsEditModalOpen(false);
    fetchActiveRolesCount();
  };

  // Click outside handler to close dropdown menu
  const handleClickOutside = (event: MouseEvent) => {
    // We don't need this handler anymore as DropdownMenuPortal handles clicks outside
    // This is kept for backward compatibility but doesn't do anything
  };

  // Add click outside listener
  useEffect(() => {
    // We don't need this listener anymore as DropdownMenuPortal handles clicks outside
    return () => {
      // No cleanup needed
    };
  }, [openActionMenuId]);

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0 text-white">
                <i className="fas fa-users me-2"></i>User Roles{activeRolesCount > 0 && <span>({activeRolesCount})</span>}
              </h5>
            </div>
          </div>

          <div className="card-body p-3">
            <div className="filters-container mb-3">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search roles..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchQuery && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchQuery('')}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>

              <div className="filter-group right-filters">
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="btn btn-primary"
                >
                  <i className="fas fa-plus me-1"></i> Add Role
                </button>
              </div>
            </div>

            <div className="table-responsive">
              <table className="roles-table">
                <thead>
                  <tr>
                    <th className="id-col">ID</th>
                    <th className="name-col">Name</th>
                    <th className="status-col">Active</th>
                    <th className="date-col">Created At</th>
                    <th className="date-col">Updated At</th>
                    <th className="action-col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="text-center">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : roles.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="text-center">
                        {searchQuery ?
                          'No roles found matching your search' :
                          'No roles found'}
                      </td>
                    </tr>
                  ) : (
                    roles.map((role) => (
                      <tr key={role.id}>
                        <td>#{role.id}</td>
                        <td>{role.name}</td>
                        <td>
                          <span className={`badge bg-${role.is_active ? 'success' : 'danger'}`}>
                            {role.is_active ? 'Yes' : 'No'}
                          </span>
                        </td>
                        <td>{new Date(role.created_at).toLocaleString()}</td>
                        <td>{new Date(role.updated_at).toLocaleString()}</td>
                        <td className="action-col">
                          <div className="dropdown" style={{ position: 'relative' }}>
                            <button
                              ref={(el) => { buttonRefs.current[role.id] = el }}
                              type="button"
                              className="btn btn-outline-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenActionMenuId(openActionMenuId === role.id ? null : role.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '0',
                                padding: '0.1rem 0.25rem',
                                fontSize: '0.7rem',
                                height: '22px',
                                minWidth: '60px',
                                maxWidth: '70px'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === role.id}
                              buttonElement={buttonRefs.current[role.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  setSelectedRole(role);
                                  setIsEditModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px',
                                }}
                              >
                                <EditIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">Edit</span>
                              </button>
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  setRoleToDelete(role);
                                  setIsDeleteModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px',
                                }}
                              >
                                <DeleteIcon width="16" height="16" fill="#D9363E" />
                                <span className="ms-2">Delete</span>
                              </button>
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={handlePerPageChange}
              perPage={dataPerPage}
            />
          </div>
        </div>

        <AddRoleModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onAddSuccess={handleAddSuccess}
        />

        <EditRoleModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onEditSuccess={handleEditSuccess}
          role={selectedRole}
        />

        {isDeleteModalOpen && roleToDelete && (
          <DeleteConfirmationModal
            isOpen={isDeleteModalOpen}
            onClose={() => {
              setIsDeleteModalOpen(false);
              setRoleToDelete(null);
            }}
            onConfirm={handleDeleteConfirm}
            userName={roleToDelete.name}
          />
        )}
      </div>
    </div>
  );
};

export default UserRoles;