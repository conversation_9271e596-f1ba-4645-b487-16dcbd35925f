import React, { useEffect, useState } from 'react';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import { useNavigate, useParams } from 'react-router-dom';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, <PERSON>Axis } from 'recharts';
import Pagination from '../../../components/Pagination';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import Header from '../../../layouts/Header/components/Header';
// CSS is loaded dynamically in useEffect

interface CreditUsageRecord {
  id: number;
  user_id: number;
  credit_usage: number;
  created_at: string;
  updated_at: string;
  user_name: string;
  user_email: string;
  daily_upload_limit: number;
  remaining_credits: number;
}

interface CreditUsageResponse {
  items: CreditUsageRecord[];
  total: number;
}

interface CreditUsageChartData {
  date: string;
  credits: number;
  name: string;
}

interface CreditSummary {
  totalUsed: number;
  averageDaily: number;
  maxUsage: number;
  currentRemaining: number;
  dailyLimit: number;
}

const ViewCredits: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();

  // Load CSS dynamically
  useEffect(() => {
    const viewCreditsCssLink = document.createElement('link');
    viewCreditsCssLink.href = '/css/view-credit-history.css';
    viewCreditsCssLink.rel = 'stylesheet';
    viewCreditsCssLink.id = 'view-credits-css';
    document.head.appendChild(viewCreditsCssLink);

    // Cleanup on unmount
    return () => {
      const cssLink = document.getElementById('view-credits-css');
      if (cssLink) {
        cssLink.remove();
      }
    };
  }, []);

  const [creditUsages, setCreditUsages] = useState<CreditUsageRecord[]>([]);
  const [chartData, setChartData] = useState<CreditUsageChartData[]>([]);
  const [creditSummary, setCreditSummary] = useState<CreditSummary>({
    totalUsed: 0,
    averageDaily: 0,
    maxUsage: 0,
    currentRemaining: 0,
    dailyLimit: 0
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [userName, setUserName] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchCreditUsages = async () => {
    setLoading(true);
    try {
      const params: Record<string, any> = {
        page: currentPage,
        page_size: itemsPerPage,
      };

      if (searchTerm) params.search = searchTerm;

      // Handle date filtering using consistent format
      if (startDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        params.start_date = start.toISOString().split('T')[0];
      }

      if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        params.end_date = end.toISOString().split('T')[0];
      }

      const response = await apiClient.get<CreditUsageResponse>(
        `/credit-usage/user/${userId}`,
        { params }
      );

      setCreditUsages(response.data.items);
      setTotalRecords(response.data.total);

      // Set user info from the first record if available
      if (response.data.items.length > 0) {
        const firstRecord = response.data.items[0];
        setUserName(firstRecord.user_name);
      }

      // Process data for the chart and summary
      processChartData(response.data.items);
      calculateCreditSummary(response.data.items);

      setError(null);
    } catch (error: any) {
      console.error('Error fetching credit usages:', error);
      setError(error.response?.data?.detail || 'Failed to load credit usage data');
      setCreditUsages([]);
      setTotalRecords(0);
      setChartData([]);
      setCreditSummary({
        totalUsed: 0,
        averageDaily: 0,
        maxUsage: 0,
        currentRemaining: 0,
        dailyLimit: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const processChartData = (data: CreditUsageRecord[]) => {
    // Generate chart data directly from the visible records on the current page
    // Instead of grouping by date, we'll display each record individually
    const chartData: CreditUsageChartData[] = data.map(record => {
      // Format the date for display
      const recordDate = new Date(record.created_at);
      const dateStr = recordDate.toLocaleDateString();
      const timeStr = recordDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

      return {
        date: dateStr,
        credits: record.credit_usage,
        name: `${dateStr} ${timeStr}`
      };
    });

    // Sort by date
    chartData.sort((a, b) => new Date(a.name).getTime() - new Date(b.name).getTime());

    setChartData(chartData);
  };

  const calculateCreditSummary = (data: CreditUsageRecord[]) => {
    if (data.length === 0) {
      setCreditSummary({
        totalUsed: 0,
        averageDaily: 0,
        maxUsage: 0,
        currentRemaining: 0,
        dailyLimit: 0
      });
      return;
    }

    // Calculate total credits used
    const totalUsed = data.reduce((sum, record) => sum + record.credit_usage, 0);

    // Calculate daily usage
    const dateMap = new Map<string, number>();
    data.forEach(record => {
      const date = new Date(record.created_at).toISOString().split('T')[0];
      const currentCredits = dateMap.get(date) || 0;
      dateMap.set(date, currentCredits + record.credit_usage);
    });

    // Calculate average daily usage
    const dailyUsages = Array.from(dateMap.values());
    const averageDaily = dailyUsages.length > 0
      ? Math.round(dailyUsages.reduce((sum, val) => sum + val, 0) / dailyUsages.length)
      : 0;

    // Find max daily usage
    const maxUsage = dailyUsages.length > 0
      ? Math.max(...dailyUsages)
      : 0;

    // Get current remaining and daily limit from the most recent record
    const sortedData = [...data].sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
    const mostRecentRecord = sortedData[0];

    setCreditSummary({
      totalUsed,
      averageDaily,
      maxUsage,
      currentRemaining: mostRecentRecord.remaining_credits,
      dailyLimit: mostRecentRecord.daily_upload_limit
    });
  };

  useEffect(() => {
    // Clear any potentially cached dates
    setStartDate(null);
    setEndDate(null);
  }, []); // Empty dependency array means this runs once on mount

  useEffect(() => {
    fetchCreditUsages();
  }, [userId, currentPage, itemsPerPage, searchTerm, startDate, endDate]);

  const formatNumber = (num: number): string => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const handlePerPageChange = (newPerPage: number) => {
    setItemsPerPage(newPerPage);
    setCurrentPage(1);
  };

  const handleBackToUserList = () => {
    navigate('/users-list');
  };

  const handleExportData = () => {
    if (creditUsages.length === 0) return;

    try {
      // Create CSV header
      const headers = [
        'ID',
        'User',
        'Email',
        'Daily Limit',
        'Credits Used',
        'Remaining Credits',
        'Created At',
        'Updated At'
      ];

      // Convert the data to CSV rows
      const csvRows = [
        headers.join(','),
        ...creditUsages.map(record => {
          return [
            record.id,
            `"${record.user_name}"`, // Add quotes to handle commas in names
            `"${record.user_email}"`,
            record.daily_upload_limit,
            record.credit_usage,
            record.remaining_credits,
            `"${new Date(record.created_at).toLocaleString()}"`,
            `"${new Date(record.updated_at).toLocaleString()}"`
          ].join(',');
        })
      ];

      // Create CSV content
      const csvContent = csvRows.join('\n');

      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      const fileName = `credit_usage_${userName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;

      // Set link properties
      link.href = url;
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';

      // Add to document, click and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="custom-tooltip" style={{
          backgroundColor: '#fff',
          padding: '10px',
          border: '1px solid #ddd',
          borderRadius: '4px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
        }}>
          <p className="label" style={{ margin: 0, fontWeight: 600, color: '#164966' }}>
            {data.name}
          </p>
          <p className="credits" style={{ margin: 0, color: '#6c757d', fontSize: '0.85rem' }}>
            {`Credits Used: ${formatNumber(data.credits)}`}
          </p>
        </div>
      );
    }
    return null;
  };

  const formatXAxisTick = (value: string) => {
    // If we have many records, show abbreviated labels
    if (chartData.length > 5) {
      return value.substring(0, 5) + '...';
    }
    return value;
  };

  return (
    <div className="dashboard-container credit-usage-view">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0 text-white">
                <i className="fas fa-credit-card me-2"></i>Credit Usage History - {userName}
              </h5>
              <div>
                <button
                  className="btn btn-sm btn-outline-light"
                  onClick={handleBackToUserList}
                >
                  <i className="fas fa-arrow-left me-1"></i> Back to User List
                </button>
              </div>
            </div>
          </div>

          <div className="card-body p-3">
            {error && (
              <div className="alert alert-danger mb-4" role="alert">
                {error}
              </div>
            )}

            {/* Credit Usage Summary Cards */}
            <div className="credit-summary-cards mb-4">
              <div className="d-flex flex-wrap">
                <div className="summary-card-wrapper" style={{ flex: "1 1 20%", padding: "0 8px", minWidth: "200px" }}>
                  <div className="card summary-card h-100" style={{ backgroundColor: "var(--color-background)" }}>
                    <div className="card-body d-flex flex-column align-items-center justify-content-center">
                      <div className="summary-icon mb-2">
                        <i className="fas fa-coins" style={{ color: "#4e79a7" }}></i>
                      </div>
                      <h6 className="summary-title" style={{ whiteSpace: "nowrap", fontSize: "0.9rem" }}>Total Credits Used</h6>
                      <div className="summary-value">{formatNumber(creditSummary.totalUsed)}</div>
                    </div>
                  </div>
                </div>
                <div className="summary-card-wrapper" style={{ flex: "1 1 20%", padding: "0 8px", minWidth: "200px" }}>
                  <div className="card summary-card h-100" style={{ backgroundColor: "var(--color-background)" }}>
                    <div className="card-body d-flex flex-column align-items-center justify-content-center">
                      <div className="summary-icon mb-2">
                        <i className="fas fa-chart-line" style={{ color: "#f28e2c" }}></i>
                      </div>
                      <h6 className="summary-title" style={{ whiteSpace: "nowrap", fontSize: "0.9rem" }}>Average Daily Usage</h6>
                      <div className="summary-value">{formatNumber(creditSummary.averageDaily)}</div>
                    </div>
                  </div>
                </div>
                <div className="summary-card-wrapper" style={{ flex: "1 1 20%", padding: "0 8px", minWidth: "200px" }}>
                  <div className="card summary-card h-100" style={{ backgroundColor: "var(--color-background)" }}>
                    <div className="card-body d-flex flex-column align-items-center justify-content-center">
                      <div className="summary-icon mb-2">
                        <i className="fas fa-gauge-high" style={{ color: "#76b7b2" }}></i>
                      </div>
                      <h6 className="summary-title" style={{ whiteSpace: "nowrap", fontSize: "0.9rem" }}>Daily Upload Limit</h6>
                      <div className="summary-value">{formatNumber(creditSummary.dailyLimit)}</div>
                    </div>
                  </div>
                </div>
                <div className="summary-card-wrapper" style={{ flex: "1 1 20%", padding: "0 8px", minWidth: "200px" }}>
                  <div className="card summary-card h-100" style={{ backgroundColor: "var(--color-background)" }}>
                    <div className="card-body d-flex flex-column align-items-center justify-content-center">
                      <div className="summary-icon mb-2">
                        <i className="fas fa-arrow-trend-up" style={{ color: "#e15759" }}></i>
                      </div>
                      <h6 className="summary-title" style={{ whiteSpace: "nowrap", fontSize: "0.9rem" }}>Maximum Daily Usage</h6>
                      <div className="summary-value">{formatNumber(creditSummary.maxUsage)}</div>
                    </div>
                  </div>
                </div>
                <div className="summary-card-wrapper" style={{ flex: "1 1 20%", padding: "0 8px", minWidth: "200px" }}>
                  <div className="card summary-card h-100" style={{ backgroundColor: "var(--color-background)" }}>
                    <div className="card-body d-flex flex-column align-items-center justify-content-center">
                      <div className="summary-icon mb-2">
                        <i className="fas fa-wallet" style={{ color: "#59a14f" }}></i>
                      </div>
                      <h6 className="summary-title" style={{ whiteSpace: "nowrap", fontSize: "0.9rem" }}>Current Remaining Credits</h6>
                      <div className="summary-value">{formatNumber(creditSummary.currentRemaining)}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Credit Usage Chart */}
            <div className="credit-usage-chart-container mb-4">
              <h6 className="section-title mb-3">
                <i className="fas fa-chart-bar me-2"></i>
                Credit Usage Per Record
              </h6>
              {loading ? (
                <div className="text-center py-5">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              ) : chartData.length > 0 ? (
                <ResponsiveContainer width="100%" height={350}>
                  <BarChart
                    data={chartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                    barCategoryGap={15}
                    maxBarSize={100}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 11 }}
                      angle={chartData.length > 5 ? -45 : 0}
                      textAnchor={chartData.length > 5 ? "end" : "middle"}
                      height={chartData.length > 5 ? 60 : 30}
                      tickLine={false}
                      tickFormatter={formatXAxisTick}
                    />
                    <YAxis
                      tick={{ fontSize: 11 }}
                      axisLine={false}
                      tickLine={false}
                      tickFormatter={(value) => value === 0 ? '0' : value >= 1000 ? `${Math.floor(value / 1000)}k` : value.toString()}
                    />
                    <Tooltip content={<CustomTooltip />} cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }} />
                    <Bar
                      dataKey="credits"
                      name="Credits Used"
                      fill="#4e79a7"
                      radius={[4, 4, 0, 0]}
                      label={{
                        position: 'top',
                        formatter: (value: number) => value > 999 ? `${(value / 1000).toFixed(1)}k` : value,
                        fontSize: 11,
                        fill: 'var(--color-text)',
                        offset: 5
                      }}
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="alert alert-info">No credit usage data available for charting</div>
              )}
            </div>

            <div className="mb-4">
              <div className="card-header py-3" style={{ background: "#164966" }}>
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0 text-white">
                    <i className="fas fa-table me-2"></i>Credit Usage Records
                  </h5>
                  <div>
                    <button
                      className="btn btn-sm btn-outline-light"
                      onClick={handleExportData}
                      disabled={creditUsages.length === 0 || loading}
                    >
                      <i className="fas fa-download me-1"></i> Export Data
                    </button>
                  </div>
                </div>
              </div>

              <div className="card-body p-3">
                <div className="d-flex flex-wrap gap-3 mb-3">
                  {/* <div className="search-input-container" style={{ flex: '1', minWidth: '250px', position: 'relative' }}>
                    <input
                      type="text"
                      placeholder="Search by email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="form-control"
                    />
                    {searchTerm && (
                      <span
                        onClick={() => setSearchTerm('')}
                        aria-label="Clear search"
                        style={{
                          position: 'absolute',
                          right: '10px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          color: '#999',
                          fontSize: '18px',
                          cursor: 'pointer',
                          fontWeight: 'bold',
                          zIndex: 9999
                        }}
                      >
                        ×
                      </span>
                    )}
                  </div> */}

                  <div className="date-picker-container" style={{ width: '200px', position: 'relative' }}>
                    <DatePicker
                      selected={startDate}
                      onChange={(date) => {
                        const today = new Date();
                        today.setHours(23, 59, 59, 999);
                        const selectedDate = date || null;

                        if (selectedDate && selectedDate > today) return;
                        if (selectedDate && endDate && selectedDate > endDate) {
                          setEndDate(selectedDate);
                        }

                        setStartDate(selectedDate);
                      }}
                      selectsStart
                      startDate={startDate || undefined}
                      endDate={endDate || undefined}
                      maxDate={new Date()}
                      className="form-control"
                      placeholderText="Start Date"
                      dateFormat="MM/dd/yyyy"
                      isClearable={false}
                      popperClassName="custom-datepicker-popper"
                      popperPlacement="bottom-start"
                    />
                    {startDate && (
                      <span
                        onClick={() => setStartDate(null)}
                        style={{
                          position: 'absolute',
                          right: '10px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          color: '#999',
                          fontSize: '18px',
                          cursor: 'pointer',
                          fontWeight: 'bold',
                          zIndex: 9999
                        }}
                      >
                        ×
                      </span>
                    )}
                  </div>

                  <div className="date-picker-container" style={{ width: '200px', position: 'relative' }}>
                    <DatePicker
                      selected={endDate}
                      onChange={(date) => {
                        const today = new Date();
                        today.setHours(23, 59, 59, 999);
                        const selectedDate = date || null;

                        if (selectedDate && selectedDate > today) return;
                        if (selectedDate && startDate && selectedDate < startDate) return;

                        setEndDate(selectedDate);
                      }}
                      selectsEnd
                      startDate={startDate || undefined}
                      endDate={endDate || undefined}
                      minDate={startDate || undefined}
                      maxDate={new Date()}
                      className="form-control"
                      placeholderText="End Date"
                      dateFormat="MM/dd/yyyy"
                      isClearable={false}
                      popperClassName="custom-datepicker-popper"
                      popperPlacement="bottom-start"
                    />
                    {endDate && (
                      <span
                        onClick={() => setEndDate(null)}
                        style={{
                          position: 'absolute',
                          right: '10px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          color: '#999',
                          fontSize: '18px',
                          cursor: 'pointer',
                          fontWeight: 'bold',
                          zIndex: 9999
                        }}
                      >
                        ×
                      </span>
                    )}
                  </div>
                </div>

                <div className="table-responsive">
                  <table className="users-table">
                    <thead>
                      <tr style={{ backgroundColor: "#396A7D", color: "white" }}>
                        <th className="id-col">ID</th>
                        {/* <th className="user-col">User</th>
                        <th className="email-col">Email</th> */}
                        <th className="limit-col">Daily Limit</th>
                        <th className="credits-col">Credits Used</th>
                        <th className="remaining-col">Remaining</th>
                        <th className="created-col">Created At</th>
                        <th className="updated-col">Updated At</th>
                      </tr>
                    </thead>
                    <tbody>
                      {loading ? (
                        <tr>
                          <td colSpan={6} className="text-center">
                            Loading...
                          </td>
                        </tr>
                      ) : creditUsages.length === 0 ? (
                        <tr>
                          <td colSpan={6} className="text-center">
                            No credit usage records found
                          </td>
                        </tr>
                      ) : (
                        creditUsages.map((record) => (
                          <tr key={record.id}>
                            <td className="id-col">{record.id}</td>
                            {/* <td className="user-col">{record.user_name}</td>
                            <td className="email-col">{record.user_email}</td> */}
                            <td className="limit-col">{formatNumber(record.daily_upload_limit)}</td>
                            <td className="credits-col">{formatNumber(record.credit_usage)}</td>
                            <td className="remaining-col">{formatNumber(record.remaining_credits)}</td>
                            <td className="created-col">{new Date(record.created_at).toLocaleString()}</td>
                            <td className="updated-col">{new Date(record.updated_at).toLocaleString()}</td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>

                <div className="d-flex justify-content-center mt-3">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={Math.ceil(totalRecords / itemsPerPage)}
                    onPageChange={setCurrentPage}
                    onPerPageChange={handlePerPageChange}
                    perPage={itemsPerPage}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewCredits;