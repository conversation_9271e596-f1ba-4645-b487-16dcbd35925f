import React, { useState, useEffect, useRef } from 'react';
import Header from '../../../layouts/Header/components/Header';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import AddBlockedIPModal from './AddBlockedIPModal';
import Pagination from '../../../components/Pagination';
import DeleteConfirmationModal from '../../user/components/DeleteConfirmationModal';
import { BlockedIPType, ServerDropdownOption } from '../../../types/models/BlockedIPType';
import EditBlockedIPModal from './EditBlockedIPModal';
import DropdownMenuPortal from '../../email/components/DropdownMenuPortal';
import { DeleteIcon, EditIcon, ActivateIcon, InactivateIcon } from "../../../components/ActionIcons";

const BlockedIPs: React.FC = () => {
  const [blockedIPs, setBlockedIPs] = useState<BlockedIPType[]>([]);
  const [servers, setServers] = useState<ServerDropdownOption[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedBlockedIP, setSelectedBlockedIP] = useState<BlockedIPType | null>(null);
  const [blockedIPToDelete, setBlockedIPToDelete] = useState<BlockedIPType | null>(null);
  // New state for selected blocked IPs and check all
  const [selectedBlockedIPs, setSelectedBlockedIPs] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [totalBlockedIPIds, setTotalBlockedIPIds] = useState<number[]>([]);
  // State for bulk delete modal
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  // State for bulk activate modal
  const [isBulkActivateModalOpen, setIsBulkActivateModalOpen] = useState(false);
  // State for bulk inactivate modal
  const [isBulkInactivateModalOpen, setIsBulkInactivateModalOpen] = useState(false);
  // Calculate the starting number for the current page
  const startingNumber = (currentPage - 1) * itemsPerPage;
  const [activeCount, setActiveCount] = useState<number>(0);
  const [bulkActionsOpen, setBulkActionsOpen] = useState(false);
  const [loadingAllIds, setLoadingAllIds] = useState(false);
  // Add state for dropdown menu
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  const fetchActiveCount = async () => {
    try {
      const response = await apiClient.get("/blocked-ips/count/active");
      setActiveCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch active blocked IPs count:", error);
    }
  };

  const fetchBlockedIPs = async () => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {
        page: currentPage,
        page_size: itemsPerPage,
      };

      if (searchTerm) {
        params.ip_address_contains = searchTerm; // Using the backend filter parameter
      }

      const response = await apiClient.get('/blocked-ips', { params });
      setBlockedIPs(response.data.data);
      setTotalPages(Math.ceil(response.data.total / itemsPerPage));
    } catch (err) {
      setError('Failed to fetch blocked IPs. Please try again later.');
      console.error('Failed to fetch blocked IPs:', err);
    } finally {
      setLoading(false);
    }
  };

  // New function to fetch all blocked IP IDs
  const fetchAllBlockedIPIds = async () => {
    setLoadingAllIds(true);
    try {
      // Instead of using the /count endpoint, get the total from the first page response
      const params: any = {
        page: 1,
        page_size: 100,
      };

      if (searchTerm) {
        params.ip_address_contains = searchTerm;
      }

      // Get first page and extract total count from response
      const firstPageResponse = await apiClient.get('/blocked-ips', { params });
      const totalCount = firstPageResponse.data.total || 0;

      const pageSize = 100; // Use a reasonable page size
      const totalPagesToFetch = Math.ceil(totalCount / pageSize);

      // Start with IDs from first page
      let allIds: number[] = firstPageResponse.data.data.map((blockedIP: BlockedIPType) => blockedIP.id);

      // Fetch remaining pages if needed
      for (let page = 2; page <= totalPagesToFetch; page++) {
        const nextParams: any = {
          page: page,
          page_size: pageSize,
        };

        if (searchTerm) {
          nextParams.ip_address_contains = searchTerm;
        }

        const response = await apiClient.get('/blocked-ips', { params: nextParams });
        const pageIds = response.data.data.map((blockedIP: BlockedIPType) => blockedIP.id);
        allIds = [...allIds, ...pageIds];
      }

      setTotalBlockedIPIds(allIds);
      return allIds;
    } catch (err) {
      console.error('Failed to fetch all blocked IP IDs:', err);
      return [];
    } finally {
      setLoadingAllIds(false);
    }
  };

  const fetchServers = async () => {
    try {
      const params = {
        page: 1,
        page_size: 100
      };
      const response = await apiClient.get('/servers/', { params });
      const serverOptions = response.data.data.map((server: any) => ({
        id: server.id,
        server_name: server.server_name
      }));
      setServers(serverOptions);
    } catch (err) {
      console.error('Failed to fetch servers:', err);
    }
  };

  useEffect(() => {
    fetchBlockedIPs();
    fetchServers();
    fetchActiveCount();
    // Fetch all blocked IP IDs when search term changes
    fetchAllBlockedIPIds();
  }, [currentPage, itemsPerPage, searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
    setSelectedBlockedIPs([]);
    setSelectAll(false);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
    setSelectedBlockedIPs([]);
    setSelectAll(false);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setItemsPerPage(newPerPage);
    setCurrentPage(1);
  };

  const handleAddBlockedIP = async () => {
    try {
      await fetchBlockedIPs(); // Refresh the list after adding
      fetchActiveCount();
      await fetchAllBlockedIPIds();
      setIsAddModalOpen(false);
    } catch (err) {
      setError('Failed to add blocked IP. Please try again.');
      console.error('Failed to add blocked IP:', err);
    }
  };

  const handleUpdateBlockedIP = async (updatedBlockedIP: BlockedIPType) => {
    setBlockedIPs(blockedIPs.map(ip =>
      ip.id === updatedBlockedIP.id ? updatedBlockedIP : ip
    ));
    fetchActiveCount();
    await fetchAllBlockedIPIds();
    setIsEditModalOpen(false);
  };

  const handleDeleteConfirm = async () => {
    if (!blockedIPToDelete) return;

    try {
      await apiClient.delete(`/blocked-ips/${blockedIPToDelete.id}`);
      setBlockedIPs(blockedIPs.filter(ip => ip.id !== blockedIPToDelete.id));
      fetchActiveCount();
      await fetchAllBlockedIPIds();
      setIsDeleteModalOpen(false);

      // Remove the deleted IP from selected IPs if it was selected
      if (selectedBlockedIPs.includes(blockedIPToDelete.id)) {
        setSelectedBlockedIPs(prev => prev.filter(id => id !== blockedIPToDelete.id));
      }
    } catch (err) {
      setError('Failed to delete blocked IP. Please try again.');
      console.error('Failed to delete blocked IP:', err);
    }
  };

  // Handle checkbox selection
  const handleSelectBlockedIP = (id: number) => {
    setSelectedBlockedIPs(prev => {
      if (prev.includes(id)) {
        setSelectAll(false);
        return prev.filter(ipId => ipId !== id);
      } else {
        const newSelected = [...prev, id];
        // Check if all IPs on the current page are selected
        const allCurrentPageSelected = blockedIPs.every(ip =>
          newSelected.includes(ip.id)
        );

        // Check if all IPs across all pages are selected
        const allIPsSelected = totalBlockedIPIds.length > 0 &&
          totalBlockedIPIds.every(id => newSelected.includes(id));

        if (allIPsSelected) {
          setSelectAll(true);
        }

        return newSelected;
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = async () => {
    if (selectAll) {
      // Immediate UI feedback
      setSelectAll(false);
      setSelectedBlockedIPs([]);
    } else {
      // Immediate UI feedback
      setSelectAll(true);
      setLoading(true);

      try {
        // Get all blocked IP IDs if we don't have them yet
        let allIds = totalBlockedIPIds;
        if (allIds.length === 0) {
          allIds = await fetchAllBlockedIPIds();
        }

        // Update selected blocked IPs with all IDs
        setSelectedBlockedIPs(allIds);
      } catch (err) {
        console.error("Failed to select all blocked IPs:", err);
        // If there was an error, revert the UI state
        setSelectAll(false);
      } finally {
        setLoading(false);
      }
    }
  };

  // Bulk activate/deactivate selected blocked IPs
  const handleBulkStatusChange = async (activate: boolean) => {
    if (selectedBlockedIPs.length === 0) return;

    setLoading(true);
    try {
      // Process each selected blocked IP
      const updatePromises = selectedBlockedIPs.map(ipId => {
        return apiClient.patch(`/blocked-ips/${ipId}`, { is_active: activate });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchBlockedIPs();
      fetchActiveCount();
      // Don't reset selections to maintain multi-page selections
      // setSelectedBlockedIPs([]);
      // setSelectAll(false);
    } catch (err) {
      setError(`Failed to ${activate ? 'activate' : 'deactivate'} blocked IPs. Please try again.`);
      console.error(`Failed to ${activate ? 'activate' : 'deactivate'} blocked IPs:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk activate for selected blocked IPs
  const handleBulkActivate = () => {
    if (selectedBlockedIPs.length === 0) return;
    setIsBulkActivateModalOpen(true);
  };

  // Handle bulk inactivate for selected blocked IPs
  const handleBulkInactivate = () => {
    if (selectedBlockedIPs.length === 0) return;
    setIsBulkInactivateModalOpen(true);
  };

  // Handle bulk delete for selected blocked IPs
  const handleBulkDelete = () => {
    if (selectedBlockedIPs.length === 0) return;
    setIsBulkDeleteModalOpen(true);
  };

  // Handle bulk activate confirmation
  const handleBulkActivateConfirm = async () => {
    if (selectedBlockedIPs.length === 0) return;

    setLoading(true);
    try {
      // Process each selected blocked IP
      const updatePromises = selectedBlockedIPs.map(ipId => {
        return apiClient.patch(`/blocked-ips/${ipId}`, { is_active: true });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchBlockedIPs();
      fetchActiveCount();
      setSelectedBlockedIPs([]);
      setSelectAll(false);
      setIsBulkActivateModalOpen(false);
    } catch (err) {
      setError(`Failed to activate blocked IPs. Please try again.`);
      console.error(`Failed to activate blocked IPs:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk inactivate confirmation
  const handleBulkInactivateConfirm = async () => {
    if (selectedBlockedIPs.length === 0) return;

    setLoading(true);
    try {
      // Process each selected blocked IP
      const updatePromises = selectedBlockedIPs.map(ipId => {
        return apiClient.patch(`/blocked-ips/${ipId}`, { is_active: false });
      });

      await Promise.all(updatePromises);

      // Refresh the data
      await fetchBlockedIPs();
      fetchActiveCount();
      setSelectedBlockedIPs([]);
      setSelectAll(false);
      setIsBulkInactivateModalOpen(false);
    } catch (err) {
      setError(`Failed to inactivate blocked IPs. Please try again.`);
      console.error(`Failed to inactivate blocked IPs:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk delete confirmation
  const handleBulkDeleteConfirm = async () => {
    if (selectedBlockedIPs.length === 0) return;

    setLoading(true);
    try {
      // Process each selected blocked IP
      const deletePromises = selectedBlockedIPs.map(ipId => {
        return apiClient.delete(`/blocked-ips/${ipId}`);
      });

      await Promise.all(deletePromises);

      // Refresh the data
      await fetchBlockedIPs();
      fetchActiveCount();
      await fetchAllBlockedIPIds();
      setSelectedBlockedIPs([]);
      setSelectAll(false);
      setIsBulkDeleteModalOpen(false);
    } catch (err) {
      setError(`Failed to delete blocked IPs. Please try again.`);
      console.error(`Failed to delete blocked IPs:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Get page title with selected count
  const getPageTitle = () => {
    let title = `Blocked IPs`;

    if (activeCount > 0) {
      title += ` (${activeCount})`;
    }

    return title;
  };

  // Handle button click - either open modal or toggle dropdown
  const handleButtonClick = () => {
    if (selectedBlockedIPs.length > 0) {
      // If items are selected, toggle the bulk actions dropdown
      setBulkActionsOpen(!bulkActionsOpen);
    } else {
      // If no items selected, directly open the Add Blocked IP modal
      setIsAddModalOpen(true);
    }
  };

  // Close bulk actions dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById('actionsDropdown');
      const dropdownMenu = document.querySelector('.dropdown-menu.show');

      // Check if click is outside both the button and the dropdown menu
      if (bulkActionsOpen &&
          dropdown &&
          !dropdown.contains(event.target as Node) &&
          dropdownMenu &&
          !dropdownMenu.contains(event.target as Node)) {
        setBulkActionsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [bulkActionsOpen]);

  // Click outside handler to close dropdown menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openActionMenuId !== null) {
        let clickedInsideDropdown = false;

        // Check if click was inside the current open dropdown button
        const openButtonRef = buttonRefs.current[openActionMenuId];
        if (openButtonRef && openButtonRef.contains(event.target as Node)) {
          clickedInsideDropdown = true;
        }

        // Check if click was inside dropdown menu
        const dropdownElements = document.querySelectorAll('.dropdown-portal');
        dropdownElements.forEach(element => {
          if (element.contains(event.target as Node)) {
            clickedInsideDropdown = true;
          }
        });

        // Also check for custom dropdown items
        const dropdownItemElements = document.querySelectorAll('.custom-dropdown-item');
        dropdownItemElements.forEach(element => {
          if (element.contains(event.target as Node)) {
            clickedInsideDropdown = true;
          }
        });

        if (!clickedInsideDropdown) {
          setOpenActionMenuId(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openActionMenuId]);

  return (
    <div className="dashboard-container">
      <style>
        {`
          #actionsDropdown {
            border-radius: 0.5rem !important;
          }
          #actionsDropdown.dropdown-toggle {
            border-radius: 0.5rem !important;
          }
        `}
      </style>
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0 text-white">
                <i className="fas fa-ban me-2"></i>{getPageTitle()}
              </h5>
            </div>
          </div>
          <div className="card-body p-3">
            <div className="filters-container mb-3">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search by IP address..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={handleClearSearch}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>

              <div className="filter-group right-filters">
                {/* Single unified dropdown for all actions */}
                <div className="position-relative d-inline-block">
                  <button
                    className={`btn btn-primary ${selectedBlockedIPs.length > 0 ? 'dropdown-toggle' : ''}`}
                    type="button"
                    id="actionsDropdown"
                    onClick={handleButtonClick}
                    disabled={loading}
                    style={{
                      border: 'none',
                      borderRadius: '0.5rem !important',
                      padding: '0.375rem 0.75rem',
                      fontSize: '0.875rem',
                      fontWeight: '400'
                    }}
                  >
                    {selectedBlockedIPs.length > 0 ? (
                      <>Bulk Actions ({selectedBlockedIPs.length})</>
                    ) : (
                      <><i className="fas fa-plus me-1"></i> Add Blocked IP</>
                    )}
                  </button>
                  {bulkActionsOpen && selectedBlockedIPs.length > 0 && (
                    <div
                      className="dropdown-menu show"
                      style={{
                        position: 'absolute',
                        top: '100%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        minWidth: '180px',
                        padding: '0.5rem 0',
                        margin: '0.125rem 0 0',
                        backgroundColor: '#fff',
                        border: '1px solid rgba(0,0,0,.15)',
                        borderRadius: '0.375rem',
                        boxShadow: '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
                        zIndex: 1000
                      }}
                    >
                      {/* Show bulk actions only when items are selected */}
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkActivate();
                          setBulkActionsOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#212529',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <ActivateIcon width="16" height="16" fill="#28a745" />
                        <span className="ms-2">Activate Selected</span>
                      </button>
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkInactivate();
                          setBulkActionsOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#212529',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <InactivateIcon width="16" height="16" fill="#dc3545" />
                        <span className="ms-2">Inactivate Selected</span>
                      </button>
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          handleBulkDelete();
                          setBulkActionsOpen(false);
                        }}
                        disabled={loading}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          padding: '0.5rem 1rem',
                          fontSize: '0.9rem',
                          color: '#D9363E',
                          textDecoration: 'none',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e9ecef'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <DeleteIcon width="16" height="16" fill="#D9363E" />
                        <span className="ms-2">Delete Selected</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {error && <div className="error-message">{error}</div>}

            <div className="table-responsive">
              <table className="servers-table" style={{ tableLayout: 'fixed', width: '100%' }}>
                <thead>
                  <tr style={{ background: "#4d7a8c" }}>
                    <th style={{ width: '50px', padding: '0.75rem 0', textAlign: 'center' }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: '0 auto',
                        width: '100%'
                      }}>
                        <div style={{
                          width: '18px',
                          height: '18px',
                          backgroundColor: selectAll ? '#00A3CC' : 'transparent',
                          border: selectAll ? 'none' : '1px solid #dee2e6',
                          borderRadius: '3px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={handleSelectAll}
                        >
                          {selectAll && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                              <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                            </svg>
                          )}
                        </div>
                      </div>
                    </th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'left', color: 'white', paddingLeft: '0.75rem' }}>Server</th>
                    <th style={{ width: '15%', padding: '0.75rem 0', textAlign: 'left', color: 'white', paddingLeft: '0.75rem' }}>IP Address</th>
                    <th style={{ width: '20%', padding: '0.75rem 0', textAlign: 'left', color: 'white', paddingLeft: '0.75rem' }}>Blocked Reason</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'left', color: 'white', paddingLeft: '0.75rem' }}>Blocked Until</th>
                    <th style={{ width: '8%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Active</th>
                    <th style={{ width: '8%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Notified</th>
                    <th style={{ width: '14%', padding: '0.75rem 0', textAlign: 'left', color: 'white', paddingLeft: '0.75rem' }}>Created At</th>
                    <th style={{ width: '10%', padding: '0.75rem 0', textAlign: 'center', color: 'white' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={9} className="text-center">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : blockedIPs.length === 0 ? (
                    <tr>
                      <td colSpan={9} className="text-center">
                        {searchTerm ?
                          'No blocked IPs found matching your search' :
                          'No blocked IPs found'}
                      </td>
                    </tr>
                  ) : (
                    blockedIPs.map((blockedIP, i) => (
                      <tr key={blockedIP.id}>
                        <td style={{ width: '50px', padding: '0.5rem' }}>
                          <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: '100%'
                          }}>
                            <div style={{
                              width: '18px',
                              height: '18px',
                              backgroundColor: selectedBlockedIPs.includes(blockedIP.id) ? '#00A3CC' : 'transparent',
                              border: selectedBlockedIPs.includes(blockedIP.id) ? 'none' : '1px solid #dee2e6',
                              borderRadius: '3px',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              cursor: 'pointer'
                            }}
                            onClick={() => handleSelectBlockedIP(blockedIP.id)}
                            >
                              {selectedBlockedIPs.includes(blockedIP.id) && (
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                                  <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                                </svg>
                              )}
                            </div>
                          </div>
                        </td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem', textAlign: 'left' }}>{blockedIP.server_name}</td>
                        <td style={{ width: '15%', padding: '0.5rem 0.75rem', textAlign: 'left' }}>{blockedIP.ip_address}</td>
                        <td style={{ width: '20%', padding: '0.5rem 0.75rem', textAlign: 'left' }}>{blockedIP.blocked_reason || '-'}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem', textAlign: 'left' }}>{blockedIP.blocked_until ? new Date(blockedIP.blocked_until).toLocaleDateString() : '-'}</td>
                        <td style={{ width: '8%', padding: '0.5rem 0.75rem', textAlign: 'center' }}>
                          <span className={`badge bg-${blockedIP.is_active ? 'success' : 'danger'}`}>
                            {blockedIP.is_active ? 'Yes' : 'No'}
                          </span>
                        </td>
                        <td style={{ width: '8%', padding: '0.5rem 0.75rem', textAlign: 'center' }}>
                          <span className={`badge bg-${blockedIP.is_notified ? 'success' : 'secondary'}`}>
                            {blockedIP.is_notified ? 'Yes' : 'No'}
                          </span>
                        </td>
                        <td style={{ width: '14%', padding: '0.5rem 0.75rem', textAlign: 'left' }}>{new Date(blockedIP.created_at).toLocaleDateString()}</td>
                        <td style={{ width: '10%', padding: '0.5rem 0.75rem', textAlign: 'center' }}>
                          <div className="custom-dropdown-container">
                            <button
                              ref={(el) => { buttonRefs.current[blockedIP.id] = el }}
                              type="button"
                              className="btn btn-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                setOpenActionMenuId(openActionMenuId === blockedIP.id ? null : blockedIP.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                                padding: '0.2rem 0.5rem',
                                fontSize: '0.75rem',
                                height: '24px',
                                width: '80px',
                                cursor: 'pointer',
                                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === blockedIP.id}
                              buttonElement={buttonRefs.current[blockedIP.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {/* Edit Option */}
                              <button
                                type="button"
                                className="dropdown-item custom-dropdown-item"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedBlockedIP(blockedIP);
                                  setIsEditModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.375rem 1rem',
                                  fontSize: '0.875rem',
                                  cursor: 'pointer',
                                  borderRadius: '0'
                                }}
                              >
                                <EditIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">Edit</span>
                              </button>

                              <button
                                type="button"
                                className="dropdown-item custom-dropdown-item"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (selectedBlockedIPs.length === 0) {
                                setBlockedIPToDelete(blockedIP);
                                setIsDeleteModalOpen(true);
                                  }
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.375rem 1rem',
                                  fontSize: '0.875rem',
                                  cursor: selectedBlockedIPs.length > 0 ? 'not-allowed' : 'pointer',
                                  borderRadius: '0',
                                  opacity: selectedBlockedIPs.length > 0 ? '0.5' : '1'
                                }}
                                disabled={selectedBlockedIPs.length > 0}
                              >
                                <DeleteIcon width="16" height="16" fill={selectedBlockedIPs.length > 0 ? "#999" : "#D9363E"} />
                                <span className="ms-2" style={{ color: selectedBlockedIPs.length > 0 ? '#999' : 'inherit' }}>Delete</span>
                              </button>
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={handlePerPageChange}
              perPage={itemsPerPage}
            />
          </div>
        </div>

        <AddBlockedIPModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onAddSuccess={handleAddBlockedIP}
          servers={servers}
        />

        {isEditModalOpen && selectedBlockedIP && (
          <EditBlockedIPModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onEditSuccess={handleUpdateBlockedIP}
            blockedIP={selectedBlockedIP}
            servers={servers}
          />
        )}

        {isDeleteModalOpen && blockedIPToDelete && (
          <DeleteConfirmationModal
            isOpen={isDeleteModalOpen}
            onClose={() => {
              setIsDeleteModalOpen(false);
              setBlockedIPToDelete(null);
            }}
            onConfirm={handleDeleteConfirm}
            userName={blockedIPToDelete.ip_address}
            entityType="Blocked IP"
          />
        )}

        {/* Bulk Activate Confirmation Modal */}
        {isBulkActivateModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkActivateModalOpen}
            onClose={() => setIsBulkActivateModalOpen(false)}
            onConfirm={handleBulkActivateConfirm}
            userName={`${selectedBlockedIPs.length} selected blocked IPs`}
            entityType="Blocked IP"
            actionType="activate"
          />
        )}

        {/* Bulk Inactivate Confirmation Modal */}
        {isBulkInactivateModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkInactivateModalOpen}
            onClose={() => setIsBulkInactivateModalOpen(false)}
            onConfirm={handleBulkInactivateConfirm}
            userName={`${selectedBlockedIPs.length} selected blocked IPs`}
            entityType="Blocked IP"
            actionType="inactivate"
          />
        )}

        {/* Bulk Delete Confirmation Modal */}
        {isBulkDeleteModalOpen && (
          <DeleteConfirmationModal
            isOpen={isBulkDeleteModalOpen}
            onClose={() => setIsBulkDeleteModalOpen(false)}
            onConfirm={handleBulkDeleteConfirm}
            userName={`${selectedBlockedIPs.length} selected blocked IPs`}
            entityType="Blocked IP"
          />
        )}
      </div>
    </div>
  );
};

export default BlockedIPs;