{"name": "rightemails", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@reduxjs/toolkit": "^2.6.1", "@types/file-saver": "^2.0.7", "@types/react-datepicker": "^6.2.0", "axios": "^1.8.4", "axios-mock-adapter": "^2.1.0", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.13.1", "file-saver": "^2.0.5", "react": "^19.0.0", "react-bootstrap": "^2.10.10", "react-datepicker": "^8.2.1", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "recharts": "^2.15.3", "redux-persist": "^6.0.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.13", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}