import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Row, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import Header from '../../../layouts/Header/components/Header';

interface ValidationStatus {
  status: string;
  count: number;
}

interface DailyProgress {
  date: string;
  progress: number;
}

interface RequestInfo {
  id: number;
  project_name: string;
  file_name: string;
  status: string;
  request_type: string;
  created_at: string;
}

interface ReportData {
  validation_statuses: ValidationStatus[];
  request_info: RequestInfo | null;
  daily_progress: DailyProgress[];
}

const COLORS = {
  'Valid': 'rgba(40, 167, 69, 0.8)',
  'Invalid': 'rgba(220, 53, 69, 0.8)',
  'Catch-All': 'rgba(255, 193, 7, 0.8)',
  'Unknown': 'rgba(108, 117, 125, 0.8)'
};

const STATUS_COLORS: { [key: string]: string } = {
  'completed': 'reports-stats-status-completed',
  'pending': 'reports-stats-status-pending',
  'failed': 'reports-stats-status-failed',
  'processing': 'reports-stats-status-processing'
};

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="custom-tooltip" style={{
        backgroundColor: '#fff',
        padding: '10px',
        border: '1px solid #ddd',
        borderRadius: '4px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      }}>
        <p className="label" style={{ margin: 0, fontWeight: 600, color: '#164966' }}>
          {`${data.status}: ${data.count.toLocaleString()}`}
        </p>
        <p className="percent" style={{ margin: 0, color: '#6c757d', fontSize: '0.85rem' }}>
          {`(${data.percentage}%)`}
        </p>
      </div>
    );
  }
  return null;
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const ReportsStats: React.FC = () => {
  const { userId, requestId } = useParams<{ userId: string, requestId?: string }>();
  const navigate = useNavigate();
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReportStats = async () => {
      setLoading(true);
      try {
        let endpoint = `/user/${userId}/detailed-report-stats`;
        if (requestId) {
          endpoint += `?request_history_id=${requestId}`;
        }

        const response = await apiClient.get(endpoint);
        setReportData(response.data);
        setError(null);
      } catch (err: any) {
        console.error('Failed to fetch report stats:', err);
        setError('Failed to load report data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchReportStats();
    }
  }, [userId, requestId]);

  const handleBackToReports = () => {
    navigate(`/user-reports/${userId}`);
  };

  const processValidationStatuses = () => {
    if (!reportData?.validation_statuses || reportData.validation_statuses.length === 0) {
      return [];
    }

    const total = reportData.validation_statuses.reduce(
      (sum, item) => sum + item.count, 0
    );

    return reportData.validation_statuses.map(status => ({
      ...status,
      percentage: Math.round((status.count / total) * 100)
    }));
  };

  const validationData = processValidationStatuses();

  if (loading) {
    return (
      <div className="dashboard-container">
        <Header />
        <Sidebar />
        <div className="dashboard-content d-flex justify-content-center align-items-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        {error ? (
          <Card className="border-0">
            <Card.Header className="reports-stats-header">
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">
                  <i className="fas fa-chart-pie me-2"></i>
                  Report Statistics
                </h5>
                <Button
                  variant="outline-light"
                  size="sm"
                  onClick={handleBackToReports}
                >
                  <i className="fas fa-arrow-left me-1"></i> Back to Reports
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="reports-stats-body">
              <div className="alert alert-danger">{error}</div>
            </Card.Body>
          </Card>
        ) : (
          <>
            <div className="shadow-sm border-0 mb-4">
              <Card.Header className="reports-stats-header">
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">
                    <i className="fas fa-chart-pie me-2"></i>
                    Report Statistics
                  </h5>
                  <Button
                    variant="outline-light"
                    size="sm"
                    onClick={handleBackToReports}
                  >
                    <i className="fas fa-arrow-left me-1"></i> Back to Reports
                  </Button>
                </div>
              </Card.Header>
              <div className="reports-stats-body">
                {reportData?.request_info ? (
                  <div className="row g-2 mb-4">
                    <Col xs={12} md={4}>
                      <div className="total-metrics-card" title={reportData.request_info.project_name || 'N/A'}>
                        <div className="metric-item">
                          <span>Project Name <i className="bi bi-folder"></i></span>
                          <div className="metric-value text-truncate">
                            {reportData.request_info.project_name || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </Col>
                    <Col xs={12} md={4}>
                      <div className="total-metrics-card">
                        <div className="metric-item">
                          <span>Request Type <i className="bi bi-gear"></i></span>
                          <div className="metric-value text-capitalize">
                            {reportData.request_info.request_type}
                          </div>
                        </div>
                      </div>
                    </Col>
                    <Col xs={12} md={4}>
                      <div className="total-metrics-card">
                        <div className="metric-item">
                          <span>Status <i className="bi bi-info-circle"></i></span>
                          <div className="metric-value">
                            <span className={`status-badge ${STATUS_COLORS[reportData.request_info.status.toLowerCase()] || ''}`}>
                              {reportData.request_info.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Col>
                    <Col xs={12} md={4}>
                      <div className="total-metrics-card">
                        <div className="metric-item">
                          <span>Uploaded At <i className="bi bi-clock"></i></span>
                          <div className="metric-value">
                            {formatDate(reportData.request_info.created_at)}
                          </div>
                        </div>
                      </div>
                    </Col>
                  </div>
                ) : (
                  <div className="alert alert-info">No request information available.</div>
                )}

                {validationData.length > 0 ? (
                  <div className="reports-stats-section">
                    <h6 className="reports-stats-section-title">
                      <i className="fas fa-chart-bar me-2"></i>
                      Email Validation Status Distribution
                    </h6>
                    <Row>
                      <Col md={12}>
                        <ResponsiveContainer height={400}>
                          <BarChart
                            data={validationData}
                            margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                            barCategoryGap={15}
                            maxBarSize={60}
                          >
                            <CartesianGrid strokeDasharray="3 3" vertical={false} />
                            <XAxis
                              dataKey="status"
                              tick={{ fontSize: 11 }}
                              angle={validationData.length > 3 ? -45 : 0}
                              textAnchor={validationData.length > 3 ? "end" : "middle"}
                              height={validationData.length > 3 ? 60 : 30}
                              tickLine={false}
                            />
                            <YAxis
                              tick={{ fontSize: 11 }}
                              axisLine={false}
                              tickLine={false}
                              tickFormatter={(value) => value === 0 ? '0' : value >= 1000 ? `${Math.floor(value / 1000)}k` : value}
                            />
                            <Tooltip
                              content={<CustomTooltip />}
                              cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                            />
                            <Bar
                              dataKey="count"
                              name="Count"
                              radius={[4, 4, 0, 0]}
                              label={{
                                position: 'top',
                                formatter: (value: number) => value > 999 ? `${(value / 1000).toFixed(1)}k` : value,
                                fontSize: 11,
                                fill: 'var(--color-text)',
                                offset: 5
                              }}
                            >
                              {validationData.map((entry, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={COLORS[entry.status as keyof typeof COLORS] || 'rgba(108, 117, 125, 0.8)'}
                                />
                              ))}
                            </Bar>
                          </BarChart>
                        </ResponsiveContainer>
                      </Col>
                    </Row>

                    <div className="reports-stats-legend mt-3 d-flex flex-wrap justify-content-center">
                      {validationData.map((entry, index) => (
                        <div key={`legend-${index}`} className="reports-stats-legend-item me-3 mb-2">
                          <span
                            className="reports-stats-legend-color"
                            style={{ backgroundColor: COLORS[entry.status as keyof typeof COLORS] || 'rgba(108, 117, 125, 0.8)' }}
                          ></span>
                          <span className="reports-stats-legend-label">
                            {entry.status}: {entry.count.toLocaleString()} ({entry.percentage}%)
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="alert alert-info">No validation status data available for this request.</div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ReportsStats;