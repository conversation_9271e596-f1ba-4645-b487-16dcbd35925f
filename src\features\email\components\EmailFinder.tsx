import { saveAs } from 'file-saver';
import React, { useEffect, useState, useRef } from 'react';
import { useSelector } from "react-redux";
import { RootState } from '../../../app/store';
import Pagination from '../../../components/Pagination';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import Header from '../../../layouts/Header/components/Header';
import EmailFinderModal from './EmailFinderModal';
import DropdownMenuPortal from './DropdownMenuPortal';

interface FinderResult {
  history: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    status: string;
    request_type: string;
    created_at: string;
    updated_at: string;
  };
  details: {
    request_body: Array<{
      first_name?: string;
      last_name?: string;
      domain: string;
    }>;
    results: Array<{
      email: string | null;
      status: string;
      message: string;
      smtp_response_code?: string;
    }>;
    progress: number;
    created_at: string;
    updated_at: string;
  };
}

const EmailFinder: React.FC = () => {
  const roleId = useSelector((state: RootState) => state.auth.roleId);
  const [results, setResults] = useState<FinderResult[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [downloadingFiles, setDownloadingFiles] = useState<{ [key: number]: boolean }>({});
  const [totalCount, setTotalCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number, left: number }>({ top: 0, left: 0 });
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  const fetchSingleFinderCount = async () => {
    try {
      const response = await apiClient.get("/verifier/count/single-finder");
      setTotalCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch single finder count:", error);
    }
  };

  const fetchFinderResults = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: currentPage,
        page_size: dataPerPage,
        request_type: 'finder'
      };

      if (searchTerm) {
        params.search_contains = searchTerm;
      }

      const response = await apiClient.get('/finder-requests', { params });
      setResults(response.data.data);
      setTotalPages(response.data.total_pages);
    } catch (error) {
      console.error('Failed to fetch finder results:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      fetchFinderResults();
      fetchSingleFinderCount();
    }, 500);

    setSearchTimeout(timeout);

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTerm, currentPage, dataPerPage]);

  useEffect(() => {
    fetchFinderResults();
    fetchSingleFinderCount();
  }, [currentPage, dataPerPage]);

  const downloadResults = async (result: FinderResult) => {
    console.log('Starting download for ID:', result.history.id);
    setDownloadingFiles(prev => {
      const newState = { ...prev, [result.history.id]: true };
      console.log('New downloading state:', newState);
      return newState;
    });

    try {
      const headers = ['First Name', 'Last Name', 'Domain', 'Email', 'Result', 'Message'];

      const data = result.details.results.map(item => ({
        first_name: result.details.request_body[0]?.first_name || '',
        last_name: result.details.request_body[0]?.last_name || '',
        domain: result.details.request_body[0]?.domain || '',
        email: item.email || '',
        status: item.status || '',
        message: item.message || ''
      }));

      if (data.length === 0 && result.details.request_body.length > 0) {
        data.push({
          first_name: result.details.request_body[0]?.first_name || '',
          last_name: result.details.request_body[0]?.last_name || '',
          domain: result.details.request_body[0]?.domain || '',
          email: '',
          status: result.history.status || '',
          message: ''
        });
      }

      let csvContent = headers.join(',') + '\n';
      data.forEach(row => {
        csvContent += [
          `"${row.first_name}"`,
          `"${row.last_name}"`,
          `"${row.domain}"`,
          `"${row.email}"`,
          `"${row.status}"`,
          `"${row.message}"`
        ].join(',') + '\n';
      });

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const filename = `finder_results_${result.history.id}.csv`;

      saveAs(blob, filename);
    } catch (error) {
      console.error('Failed to download results:', error);
    } finally {
      setTimeout(() => {
        console.log('Finished download for ID:', result.history.id);
        setDownloadingFiles(prev => {
          const newState = { ...prev, [result.history.id]: false };
          console.log('New downloading state after completion:', newState);
          return newState;
        });
      }, 500);
    }
  };

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <h5 className="mb-0 text-white">
              <i className="fas fa-search me-2"></i>Email Finder{totalCount > 0 && <span>({totalCount})</span>}
            </h5>
          </div>
          <div className="card-body p-3">
            <div className="filters-container">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search by name, domain"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchTerm('')}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>
              <div className="filter-group right-filters">
                <button onClick={() => setIsModalOpen(true)} className="find-button">
                  <i className="fas fa-search me-2"></i>Find Email
                </button>
              </div>
            </div>

            <div className="table-responsive">
              <table className="results-table">
                <thead>
                  <tr>
                    <th className="name-col">First Name</th>
                    <th className="name-col">Last Name</th>
                    <th className="domain-col">Domain</th>
                    {roleId === 1 ? (
                      <th className="uploaded-col">Uploaded By</th>
                    ) : (
                      <th className="email-col">Email</th>
                    )}
                    <th className="result-col">Result</th>
                    <th className="progress-col">Progress</th>
                    <th className="date-col">Created At</th>
                    <th className="action-col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="text-center">
                        <div className="spinner-border text-primary" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : results.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center">
                        {searchTerm ?
                          'No results found for your search' :
                          'No finder results found'}
                      </td>
                    </tr>
                  ) : (
                    results.map((result) => {
                      const request = result.details.request_body[0];
                      const resultData = result.details.results[0] || {};
                      return (
                        <tr key={result.history.id}>
                          <td>{request?.first_name || '-'}</td>
                          <td>{request?.last_name || '-'}</td>
                          <td>{request?.domain || '-'}</td>
                          <td>
                            {roleId === 1 ? (
                              `${result.history.first_name || ''} ${result.history.last_name || ''}`.trim() || 'N/A'
                            ) : (
                              resultData.email || '-'
                            )}
                          </td>
                          <td>
                            {resultData.status || result.history.status}
                          </td>
                          <td>
                            <div className="progress">
                              <div
                                className={`progress-bar ${getProgressBarColor(result.details?.progress || 0)}`}
                                style={{ width: `${result.details?.progress || 0}%` }}
                              >
                                {result.details?.progress || 0}%
                              </div>
                            </div>
                          </td>
                          <td>{new Date(result.history.created_at).toLocaleString()}</td>
                          <td className="action-col">
                            <div
                              onClick={() => {
                                if (result.history.status === 'completed' && !downloadingFiles[result.history.id]) {
                                  // Set downloading directly
                                  const resultId = result.history.id;
                                  setDownloadingFiles(prev => ({...prev, [resultId]: true}));
                                  
                                  // Generate and download the CSV
                                  try {
                                    const headers = ['First Name', 'Last Name', 'Domain', 'Email', 'Result', 'Message'];
                                    const data = result.details.results.map(item => ({
                                      first_name: result.details.request_body[0]?.first_name || '',
                                      last_name: result.details.request_body[0]?.last_name || '',
                                      domain: result.details.request_body[0]?.domain || '',
                                      email: item.email || '',
                                      status: item.status || '',
                                      message: item.message || ''
                                    }));
                                    
                                    if (data.length === 0 && result.details.request_body.length > 0) {
                                      data.push({
                                        first_name: result.details.request_body[0]?.first_name || '',
                                        last_name: result.details.request_body[0]?.last_name || '',
                                        domain: result.details.request_body[0]?.domain || '',
                                        email: '',
                                        status: result.history.status || '',
                                        message: ''
                                      });
                                    }
                                    
                                    let csvContent = headers.join(',') + '\n';
                                    data.forEach(row => {
                                      csvContent += [
                                        `"${row.first_name}"`,
                                        `"${row.last_name}"`,
                                        `"${row.domain}"`,
                                        `"${row.email}"`,
                                        `"${row.status}"`,
                                        `"${row.message}"`
                                      ].join(',') + '\n';
                                    });
                                    
                                    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                                    const filename = `finder_results_${resultId}.csv`;
                                    
                                    saveAs(blob, filename);
                                  } catch (error) {
                                    console.error('Failed to download results:', error);
                                  } finally {
                                    // Reset downloading state
                                    setTimeout(() => {
                                      setDownloadingFiles(prev => ({...prev, [resultId]: false}));
                                    }, 1000); // Add a slight delay to make the spinner visible
                                  }
                                }
                              }}
                              className={`download-button ${result.history.status !== 'completed' || downloadingFiles[result.history.id] ? 'disabled' : ''}`}
                              title={
                                downloadingFiles[result.history.id]
                                ? 'Downloading...' 
                                : result.history.status !== 'completed' 
                                  ? 'Download not available (Finder not completed)' 
                                  : 'Download Results'
                              }
                            >
                              {downloadingFiles[result.history.id] ? (
                                <div className="spinner-border spinner-border-sm" role="status" style={{
                                  width: '20px',
                                  height: '20px',
                                  borderWidth: '2px',
                                  color: '#164966'
                                }}>
                                  <span className="visually-hidden">Downloading...</span>
                                </div>
                              ) : (
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                              </svg>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>

            {results.length > 0 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                onPerPageChange={setDataPerPage}
                perPage={dataPerPage}
              />
            )}

            <EmailFinderModal
              isOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              onFindSuccess={fetchFinderResults}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

function getProgressBarColor(progress: number): string {
  if (progress === 100) return 'bg-success';
  if (progress > 70) return 'bg-info';
  if (progress > 30) return '';
  return 'bg-warning';
}

export default EmailFinder;