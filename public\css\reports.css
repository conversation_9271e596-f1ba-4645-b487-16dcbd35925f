/* Reports Dashboard Styles */

/* KPICard Styles */
.kpi-card {
    padding: 0 5px;
    height: 100%;
}

.kpi-card-inner {
    background-color: var(--color-background);
    border-radius: 8px;
    padding: 15px;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 2px solid transparent;
}

.kpi-card-inner:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.kpi-card-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    gap: 0.5rem;
}

.kpi-card-title {
    font-size: 0.75rem;
    color: var(--color-text-light);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.kpi-card-value {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--color-primary-light);
}

/* Color variations */
.kpi-card.success .kpi-card-inner {
    border-color: var(--color-success);
}

.kpi-card.success .kpi-card-value {
    color: var(--color-success);
}

.kpi-card.danger .kpi-card-inner {
    border-color: var(--color-danger);
}

.kpi-card.danger .kpi-card-value {
    color: var(--color-danger);
}

.kpi-card.warning .kpi-card-inner {
    border-color: var(--color-warning);
}

.kpi-card.warning .kpi-card-value {
    color: var(--color-warning);
}

.kpi-card.primary .kpi-card-inner {
    border-color: var(--color-primary-light);
}

.kpi-card.primary .kpi-card-value {
    color: var(--color-primary-light);
}

.kpi-card.info .kpi-card-inner {
    border-color: var(--color-info);
}

.kpi-card.info .kpi-card-value {
    color: var(--color-info);
}

/* Total Metrics Cards */
.total-metrics-card {
    background: var(--color-background);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 5px 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 120px;
}

.total-metrics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.12);
}

.metric-item {
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.metric-item span {
    font-size: 0.75rem;
    color: var(--color-text-light);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.metric-value {
    font-size: 1.5rem;
    color: var(--color-primary-light);
    font-weight: bold;
    margin-top: 0.5rem;
}

/* Chart Cards */
.chart-card {
    background: var(--color-white);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Accordion Styles */
.reports-accordion .card {
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 1px solid var(--color-border);
}

.reports-accordion .card-header {
    padding: 0 !important;
    border-bottom: none !important;
    background-color: var(--color-background) !important;
}

.reports-accordion .accordion-button {
    background-color: transparent !important;
    padding: 0 !important;
}

.reports-accordion .accordion-button:not(.collapsed) {
    background-color: transparent !important;
    box-shadow: none !important;
}

.reports-accordion .accordion-button:focus {
    box-shadow: none;
}

.reports-accordion .accordion-arrow {
    transition: transform 0.3s ease;
    font-size: 1.1rem;
    color: var(--color-primary-light);
}

.reports-accordion .accordion-button:not(.collapsed) .accordion-arrow {
    transform: rotate(180deg);
}

.reports-accordion .accordion-body {
    padding: 0;
}

.bg-background {
    background-color: var(--color-background) !important;
}

/* Icons */
.bi {
    font-size: 1.1rem;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kpi-card {
        padding: 0;
        margin-bottom: 10px;
    }

    .kpi-card-title {
        font-size: 0.7rem;
    }

    .kpi-card-value {
        font-size: 1.1rem;
    }

    .total-metrics-card {
        height: auto;
        padding: 1rem;
    }

    .metric-value {
        font-size: 1.25rem;
    }
}

/* Add to your CSS file */
.reports-table {
    --bs-table-bg: var(--color-background);
    --bs-table-striped-bg: rgba(0, 130, 163, 0.05);
    --bs-table-hover-bg: rgba(0, 130, 163, 0.1);
    border-color: var(--color-border);
}

.reports-table th {
    background-color: var(--color-sidebar-bg) !important;
    color: white;
    font-weight: 500;
}

.reports-table td {
    color: var(--color-text);
}

.table-header {
    position: sticky;
    top: 0;
}

/* Daily Metrics Chart Styles */
.metrics-container {
    font-family: var(--font-family);
    background-color: transparent;
}

.metrics-controls {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.view-toggle {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.date-range-controls {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
}

.date-range-select {
    width: 140px;
    background-color: var(--color-background);
    border-color: var(--color-border);
    color: var(--color-text);
}

.export-button {
    border-color: var(--color-border) !important;
    color: var(--color-text) !important;
}

/* .metrics-display {
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--color-border);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
} */

.metrics-range-indicator {
    margin-top: var(--spacing-sm);
    text-align: right;
    font-size: 0.8rem;
    color: var(--color-text-light);
}

.metric-loading-skeleton {
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Chart Styles */
.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
}

.chart-error {
    margin: var(--spacing-md) 0;
}

.chart-grid {
    stroke: var(--color-border);
    opacity: 0.3;
}

.chart-axis {
    font-size: 0.8rem;
    font-family: var(--font-family);
}

.chart-axis text {
    fill: var(--color-text);
}

.chart-tooltip {
    background-color: var(--color-background) !important;
    border-color: var(--color-border) !important;
    border-radius: var(--border-radius-sm) !important;
    font-size: 0.8rem !important;
}

.chart-tooltip .label {
    color: var(--color-text) !important;
}

.chart-legend {
    font-size: 0.8rem;
    color: var(--color-text);
    padding-top: 10px;
}

.chart-bar-processed {
    fill: var(--color-primary-light);
}

.chart-bar-valid {
    fill: var(--color-success);
}

.chart-bar-invalid {
    fill: var(--color-danger);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .metrics-controls {
        flex-direction: column;
        align-items: flex-start;
    }

    .date-range-controls {
        width: 100%;
    }

    .date-range-select {
        width: 100%;
    }

    .export-button {
        width: 100%;
    }

    .reports-table th,
    .reports-table td {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
}