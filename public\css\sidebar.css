/* Sidebar.css */
.sidebar {
    width: 280px;
    height: 100vh;
    background-color: var(--color-sidebar-bg);
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .sidebar-header {
    padding: var(--spacing-sidebar);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-white);
  }
  
  .sidebar-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: var(--color-primary-dark);
  }
  
  .close-button {
    background: none;
    border: none;
    cursor: pointer;
    display: none; 
  }
  
  .close-button img {
    width: 24px;
    height: 24px;
  }
  
  .sidebar-menu {
    padding: var(--spacing-sm);
  }
  
  .sidebar-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--color-white);
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--color-white);
    border-radius: var(--border-radius-sm); 
  }
  
  .sidebar-item.selected {
    background-color: var(--color-sidebar-selected);
    border-radius: var(--border-radius-sm); 
  }
  
  .sidebar-item:hover {
    background-color: var(--color-primary-dark);
    color: var(--color-white);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transform: translateX(3px); 
    cursor: pointer;
  }
  
  .sidebar-item img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1); 
  }
  
  .sidebar-item span {
    font-size: 1rem;
    font-weight: 500;
  }
  
  
  .mobile-toggle {
    display: none; 
    position: fixed;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    background-color: var(--color-primary-dark);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs); 
    cursor: pointer;
    z-index: 1000;
    width: 40px; 
    height: 40px; 
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .mobile-toggle img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1); 
  }
  
  /* Show toggle button on mobile */
  @media (max-width: 768px) {
    .mobile-toggle {
      display: flex; 
    }
  
    .sidebar {
      transform: translateX(-100%);
    }
  
    .sidebar.open {
      transform: translateX(0);
    }
  
    .close-button {
      display: block;
    }
  
    .dashboard-content {
      margin-left: 0;
    }
  
    .dashboard-footer {
      left: 0;
      z-index: 500;
    }
  }