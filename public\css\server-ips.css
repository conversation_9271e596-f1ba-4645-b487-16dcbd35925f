/* Server IPs Container */
.server-ips-container {
    padding: var(--spacing-md);
}

/* Servers Table */
.servers-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    margin-bottom: var(--spacing-md);
}

.servers-table th{
    padding: var(--spacing-xs);
    text-align: center !important;
    border-bottom: 1px solid var(--color-border);
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.servers-table td {
    padding: var(--spacing-sm);
    text-align: center;
    border-bottom: 1px solid var(--color-border);
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.servers-table th {
    background-color: var(--color-table-th);
    color: var(--color-white);
}

.servers-table tr:hover {
    background-color: rgba(0, 130, 163, 0.1);
}

/* Actions Column */
.servers-table td:last-child {
    white-space: nowrap;
}

.id-col {
    width: 5%;
    text-align: center;
}

.server-col {
    width: 18%;
    text-align: center;
}

.ip-col {
    width: 20%;
    text-align: center;
}

.domain-col {
    width: 20%;
    text-align: center;
}

.status-col {
    width: 12%;
    text-align: center;
}

.date-col {
    width: 15%;
    text-align: center;
}

.action-col {
    width: 9%;
    text-align: center;
}

/* Add styles for custom dropdown container in action column */
.custom-dropdown-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.custom-dropdown-toggle {
  margin: 0 auto;
  width: 80px;
  text-align: center;
}

/* Bootstrap Dropdown Styles */
.action-col .dropdown {
  display: flex;
  justify-content: center;
  width: 100%;
}

.action-col .dropdown .btn {
  margin: 0 auto;
  width: 80px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-col .dropdown .dropdown-menu {
  transform: translate(-50%, 0);
  left: 50%;
  right: auto;
  text-align: left;
  min-width: 100px;
  max-width: 150px;
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.15rem 0.4rem; /* Smaller padding */
  font-size: 0.75rem;     /* Smaller font size */
  min-width: auto;        /* Override any default min-width from btn or btn-sm if necessary */
  border-radius: 0 !important; /* Ensure straight lines */
}

/* Custom hover effect for the dropdown items */
.dropdown-item:hover,
.dropdown-item:focus {
  background-color: rgba(22, 73, 102, 0.1); /* Light version of your theme color #164966 */
  color: #164966; /* Optional: darken text on hover for better contrast if needed */
}


/* .dropdown-item > svg,
.dropdown-item > i {
  display: none !important;
} */

.dropdown-item::before {
  display: none !important;
  content: "" !important;
}

/* Straight borders for dropdown menu items */
.dropdown-item {
  border-radius: 0 !important;
}

/* Ensure no border radius on any dropdowns throughout the app */
.dropdown-menu, 
.dropdown-toggle,
.btn-sm,
.btn-outline-secondary,
.dropdown button {
  border-radius: 0 !important;
}

/* Restore spinner border-radius to make spinners circular again */
.spinner-border,
.spinner-border-sm,
.spinner,
[class*="spinner-"] {
  border-radius: 50% !important; /* Override to keep spinners circular */
}

/* Make sure dropdown menus in portals have straight edges */
body > div[style*="position: absolute"] {
  border-radius: 0 !important;
}