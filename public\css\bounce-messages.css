/* Bulk Email Finder Container */
.bulk-email-finder-container {
  padding: var(--spacing-md);
}

/* Form Styling */
.bulk-upload-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* Uploads Table */
.uploads-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}

.uploads-table th,
.uploads-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.uploads-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
}

.uploads-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

.bulk-upload-form {
  padding: var(--spacing-sm);
}

.id-col {
  width: 8%;
  white-space: nowrap;
}

.message-col {
  width: 46%;
  white-space: nowrap;
}

.category-col {
  width: 10%;
  white-space: nowrap;
}

.status-col {
  width: 8%;
  white-space: nowrap;
}

.date-col {
  width: 13%;
  white-space: nowrap;
}

.action-col {
  width: 9%;
  white-space: nowrap;
}

/* Bootstrap Dropdown Styles */
.action-col .dropdown {
  display: flex;
  justify-content: center;
  width: 100%;
}

.action-col .dropdown .btn {
  margin: 0 auto;
  width: 80px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-col .dropdown .dropdown-menu {
  transform: translate(-50%, 0);
  left: 50%;
  right: auto;
  text-align: left;
  min-width: 100px;
  max-width: 150px;
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.15rem 0.4rem; /* Smaller padding */
  font-size: 0.75rem;     /* Smaller font size */
  min-width: auto;        /* Override any default min-width from btn or btn-sm if necessary */
  border-radius: 0 !important; /* Ensure straight lines */
}

/* Custom hover effect for the dropdown items */
.dropdown-item:hover,
.dropdown-item:focus {
  background-color: rgba(22, 73, 102, 0.1); /* Light version of your theme color #164966 */
  color: #164966; /* Optional: darken text on hover for better contrast if needed */
}



.dropdown-item::before {
  display: none !important;
  content: "" !important;
}

/* Straight borders for dropdown menu items */
.dropdown-item {
  border-radius: 0 !important;
}

/* Ensure no border radius on any dropdowns throughout the app */
.dropdown-menu, 
.dropdown-toggle,
.btn-sm,
.btn-outline-secondary,
.dropdown button {
  border-radius: 0 !important;
}

/* Restore spinner border-radius to make spinners circular again */
.spinner-border,
.spinner-border-sm,
.spinner,
[class*="spinner-"] {
  border-radius: 50% !important; /* Override to keep spinners circular */
}

/* Make sure dropdown menus in portals have straight edges */
body > div[style*="position: absolute"] {
  border-radius: 0 !important;
}

/* Custom dropdown container */
.custom-dropdown-container {
  display: flex;
  justify-content: center;
  width: 100%;
}
