/* Verify Email Container */
.verify-email-container {
  padding: var(--spacing-md);
}

/* Verify Section */
.verify-section {
  margin-bottom: var(--spacing-md);
}

.verify-table {
  display: table;
  width: 100%;
}

.verify-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.verify-cell {
  display: flex;
  align-items: center;
}

.verify-cell label {
  font-weight: 600;
}

.verify-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-table-th);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  white-space: nowrap;
  font-size: var(--spacing-custom-xs);
}

.verify-button:hover {
  background: var(--color-primary-light);
}

/* Results Table */
.results-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}

.results-table th,
.results-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Specific override for action column to allow dropdowns to be visible */
.results-table td.action-col {
  overflow: visible; /* Allow the dropdown to overflow the cell without being clipped */
}

.results-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
}

.results-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

/* Column Widths */
.email-col {
  width: 30%;
  word-break: break-all;
  white-space: normal;
}

.uploaded-col{
  width: 15%;
  white-space: nowrap;
}

.status-col {
  width: 12%;
  white-space: nowrap;
}

.result-col {
  width: 10%;
  white-space: nowrap;
}

.progress-col {
  width: 10%;
  white-space: nowrap;
}

.date-col {
  width: 14%;
  white-space: nowrap;
}

.action-col {
  width: 8%;
  white-space: nowrap;
}

.search-group {
  flex: 1;
  min-width: 200px;
}

/* Custom styles for the Actions dropdown specific to VerifyEmail component */
/* ======================================================================== */

/* Custom styles for the Actions button */
.verify-actions-btn {
  padding: 0.15rem 0.4rem; /* Smaller padding */
  font-size: 0.75rem;     /* Smaller font size */
  min-width: auto;        /* Override any default min-width */
}

/* Custom hover effect for dropdown items */
.verify-dropdown-item:hover,
.verify-dropdown-item:focus {
  background-color: rgba(22, 73, 102, 0.1); /* Light version of theme color #164966 */
  color: #164966; /* Darken text on hover for better contrast */
}

/* Hide unexpected icon next to the Download button */
.verify-dropdown-item > svg,
.verify-dropdown-item > i {
  display: none !important;
}

.verify-dropdown-item::before {
  display: none !important; /* Hide if it's a ::before pseudo-element */
  content: "" !important;    /* Also clear content for pseudo-element */
}