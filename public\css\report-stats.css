.reports-stats-container {
  width: 100%;
  padding: 20px;
  background-color: var(--color-background);
}

.filters-container {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 20px !important;
  flex-wrap: wrap !important;
  gap: 15px !important;
  width: 100% !important;
}

.filter-group {
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

.search-group {
  position: relative !important;
  flex-grow: 1 !important;
  min-width: 200px !important;
}

.right-filters {
  display: flex !important;
  align-items: center !important;
  gap: 15px !important;
  flex-wrap: wrap !important;
}

/* Search input - CURRENTLY NOT USED */
.search-input {
  width: 100% !important;
  height: 38px !important;
  padding: 8px 30px 8px 15px !important; /* Add extra padding on the right for the clear button */
  font-size: 14px !important;
  border: 1px solid #0082a3 !important;
  border-radius: 8px !important;
  box-shadow: inset 0 1px 2px rgba(0,0,0,.075) !important;
}

.search-input:focus {
  outline: none !important;
  border-color: #0082a3 !important;
  box-shadow: 0 0 0 2px rgba(0, 130, 163, 0.25) !important;
}

.clear-search {
  position: absolute !important;
  right: 10px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  font-size: 20px !important;
  line-height: 1 !important;
  color: #999 !important;
  display: block !important;
  z-index: 100 !important;
  padding: 0 !important;
  margin: 0 !important;
  height: auto !important;
  width: auto !important;
}

/* Date picker container - CURRENTLY NOT USED */
.date-picker-container {
  position: relative !important;
  width: 150px !important;
  min-width: 150px !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Override react-datepicker styles for our clear button - CURRENTLY NOT USED */
.react-datepicker__close-icon {
  display: none !important;
}

.date-picker {
  width: 100% !important;
  height: 38px !important;
  padding: 8px 30px 8px 12px !important; /* Add right padding for the clear button */
  font-size: 14px !important;
  border: 1px solid #0082a3 !important;
  border-radius: 8px !important;
  box-shadow: inset 0 1px 2px rgba(0,0,0,.075) !important;
  z-index: 1 !important;
  margin: 0 !important;
}

/* Our custom date clear button wrapper - CURRENTLY NOT USED */
.date-clear-button-wrapper {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  height: 100% !important;
  width: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

/* Button to clear date - CURRENTLY NOT USED */
.clear-date {
  display: block !important;
  background: none !important;
  border: none !important;
  font-size: 22px !important;
  line-height: 1 !important;
  color: #999 !important;
  cursor: pointer !important;
  padding: 0 !important;
  margin: 0 !important;
  height: auto !important;
  width: auto !important;
}

/* Actively used styles below this point */
.reports-stats-header {
  background-color: var(--color-primary-dark);
  color: white;
  border-radius: 5px 5px 0 0;
  padding: 15px 20px;
  margin-bottom: 0;
}

.reports-stats-body {
  background-color: var(--color-white);
  border-radius: 0 0 5px 5px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.reports-stats-section {
  margin-bottom: 30px;
}

.reports-stats-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--color-primary-dark);
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 8px;
}

.reports-stats-chart {
  background-color: var(--color-background);
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  height: 380px;
  margin-bottom: 20px;
}

/* Specific adjustments for pie and bar charts */
.pie-chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 100%;
  width: 100% !important;
}

.bar-chart-container {
  padding-left: 10px;
  width: 100% !important;
}

.reports-stats-info-card {
  background-color: var(--color-background);
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid var(--color-primary-dark);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.reports-stats-info-title {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--color-text-light);
  margin-bottom: 5px;
}

.reports-stats-info-value {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--color-text);
  word-break: break-word;
  overflow: hidden;
}

.reports-stats-status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.reports-stats-status-completed {
  background-color: rgba(40, 167, 69, 0.15);
  color: var(--color-success);
}

.reports-stats-status-pending {
  background-color: rgba(255, 193, 7, 0.15);
  color: var(--color-warning);
}

.reports-stats-status-failed {
  background-color: rgba(220, 53, 69, 0.15);
  color: var(--color-danger);
}

.reports-stats-status-processing {
  background-color: rgba(13, 110, 253, 0.15);
  color: var(--color-info);
}

.reports-stats-back-button {
  margin-bottom: 20px;
}

/* For the pie charts */
.reports-stats-pie-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.reports-stats-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 15px;
  padding: 10px;
  background-color: var(--color-background);
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.reports-stats-legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px 5px;
  padding: 3px 8px;
  border-radius: 4px;
  transition: background-color 0.15s ease;
}

.reports-stats-legend-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.reports-stats-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
  display: inline-block;
}

.reports-stats-legend-label {
  font-size: 0.85rem;
  color: var(--color-text);
  white-space: nowrap;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .reports-stats-chart {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .reports-stats-chart {
    height: 250px;
  }
  
  .reports-stats-section {
    margin-bottom: 20px;
  }
}

/* Progress Table Styles - CURRENTLY NOT USED (Will be used in future) */
.table-responsive .reports-stats-progress-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0;
  background-color: var(--color-background) !important;
}

.table-responsive .reports-stats-progress-table th {
  background-color: var(--color-primary-dark) !important;
  color: white !important;
  font-weight: 500 !important;
  padding: 12px 15px !important;
  text-align: left !important;
  font-size: 0.9rem !important;
}

.table-responsive .reports-stats-progress-table td {
  padding: 12px 15px !important;
  border-bottom: 1px solid #dee2e6 !important;
  font-size: 0.9rem !important;
  background-color: var(--color-background) !important;
}

.table-responsive .reports-stats-progress-table tr {
  background-color: var(--color-background) !important;
}

.table-responsive .reports-stats-progress-table tr:hover > td {
  background-color: rgba(0, 130, 163, 0.1) !important;
}

/* Progress Bar in Table - CURRENTLY NOT USED (Will be used in future) */
.progress-bar-container {
  position: relative;
  width: 100%;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: #28a745;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-bar-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Progress Status Badges - CURRENTLY NOT USED (Will be used in future) */
.progress-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.progress-status.completed {
  background-color: rgba(40, 167, 69, 0.15);
  color: #28a745;
}

.progress-status.in-progress {
  background-color: rgba(13, 110, 253, 0.15);
  color: #0d6efd;
}

.progress-status.pending {
  background-color: rgba(255, 193, 7, 0.15);
  color: #ffc107;
}

/* Responsive Table */
@media (max-width: 768px) {
  .reports-stats-progress-table {
    font-size: 0.8rem;
  }
  
  .reports-stats-progress-table th,
  .reports-stats-progress-table td {
    padding: 8px 10px;
  }
  
  .progress-bar-container {
    height: 18px;
  }
  
  .progress-bar-text {
    font-size: 0.7rem;
  }
}

/* Override Bootstrap table-striped alternating rows */
.table-striped.reports-stats-progress-table > tbody > tr:nth-of-type(odd) > * {
  background-color: var(--color-background) !important;
}

.table-striped.reports-stats-progress-table > tbody > tr:nth-of-type(even) > * {
  background-color: var(--color-background) !important;
  opacity: 0.9;
}

/* Override Bootstrap column widths specifically for charts */
.reports-stats-section .row .col-lg-3 {
  width: 25% !important;
  flex: 0 0 25% !important;
  max-width: 25% !important;
}

.reports-stats-section .row .col-lg-9 {
  width: 75% !important;
  flex: 0 0 75% !important;
  max-width: 75% !important;
}

/* Force the responsive container to respect parent dimensions */
.reports-stats-chart .recharts-responsive-container {
  width: 100% !important;
  height: 100% !important;
}

/* Custom chart layout for 75/100 ratio */
.pie-bar-chart-row {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.pie-chart-wrapper {
  width: 45% !important; 
  padding-right: 20px;
}

.bar-chart-wrapper {
  width: 55% !important;
}

/* Ensure charts fill their containers */
.pie-chart-container, 
.bar-chart-container {
  width: 100% !important;
  height: 380px !important;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .pie-chart-wrapper,
  .bar-chart-wrapper {
    width: 100% !important;
    padding-right: 0;
  }
  
  .pie-chart-wrapper {
    margin-bottom: 20px;
  }
}

/* Additional overrides with max specificity */
body .reports-stats-section .pie-bar-chart-row {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  width: 100% !important;
}

body .reports-stats-section .pie-bar-chart-row .pie-chart-wrapper {
  width: 35% !important;
  flex: 0 0 35% !important;
  max-width: 35% !important;
  box-sizing: border-box !important;
}

body .reports-stats-section .pie-bar-chart-row .bar-chart-wrapper {
  width: 65% !important;
  flex: 0 0 65% !important;
  max-width: 65% !important;
  box-sizing: border-box !important;
}

/* Custom DatePicker component styling */
.custom-date-picker-wrapper {
  position: relative !important;
  width: 100% !important;
  display: block !important;
  margin: 0 !important;
}

.custom-date-clear-btn {
  position: absolute !important;
  right: 10px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  background: none !important;
  border: none !important;
  font-size: 22px !important;
  line-height: 1 !important;
  color: #999 !important;
  cursor: pointer !important;
  z-index: 200 !important;
  padding: 0 !important;
  display: block !important;
  width: 24px !important;
  height: 24px !important;
  text-align: center !important;
}

/* Make sure all datepicker inputs have the same height */
.date-picker {
  width: 100% !important;
  height: 38px !important;
  padding: 8px 30px 8px 12px !important; /* Add right padding for the clear button */
  font-size: 14px !important;
  border: 1px solid #0082a3 !important;
  border-radius: 8px !important;
  box-shadow: inset 0 1px 2px rgba(0,0,0,.075) !important;
  z-index: 1 !important;
  margin: 0 !important;
}

/* Hide DatePicker's built-in clear button */
.react-datepicker__close-icon {
  display: none !important;
}

/* Ensure date picker container has correct size */
.date-picker-container {
  position: relative !important;
  width: 150px !important;
  min-width: 150px !important;
  margin: 0 !important;
  padding: 0 !important;
}

.filters-container {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 20px !important;
  flex-wrap: wrap !important;
  gap: 15px !important;
  width: 100% !important;
} 

/* Total Metrics Cards */
.total-metrics-card {
    background: var(--color-background);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 5px 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 120px;
}

.total-metrics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.12);
}

.metric-item {
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.metric-item span {
    font-size: 0.75rem;
    color: var(--color-text-light);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.metric-value {
    font-size: 1rem;
    color: var(--color-primary-light);
    font-weight: bold;
    margin-top: 0.5rem;
}
