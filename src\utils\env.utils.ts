import { environment } from '../config/environment';

export const isDevelopment = (): boolean => {
  return environment.REACT_APP_ENV === 'development';
};

export const isProduction = (): boolean => {
  return environment.REACT_APP_ENV === 'production';
};

export const isTest = (): boolean => {
  return environment.REACT_APP_ENV === 'staging';
};

export const getApiUrl = (): string => {
  return environment.REACT_APP_API_URL;
};

export const isDebugEnabled = (): boolean => {
  return environment.REACT_APP_DEBUG;
};