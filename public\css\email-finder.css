/* Email Finder Container */
.email-finder-container {
    padding: var(--spacing-md);
  }
  
  /* Card Header */
  .card-header {
    background: var(--color-primary-dark);
    color: var(--color-white);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  }
  
  .card-header h5 {
    font-size: 1.25rem;
    font-weight: 600;
  }
  
  /* Form Styling */
  .email-finder-form {
    padding: var(--spacing-sm);
  }
  
  .form-group {
    margin-bottom: var(--spacing-sm);
  }
  
  .form-label {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
  }
  
  .form-control {
    width: 100%;
    padding: var(--spacing-xs);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-sm);
  }
  
  .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .btn-primary {
    background: var(--color-primary-dark);
    color: var(--color-white);
    border: none;
  }
  
  .btn-primary:hover {
    background: var(--color-primary-light);
  }
  
  /* Results Table */
  .results-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    margin-bottom: var(--spacing-md);
  }
  
  .results-table th,
  .results-table td {
    padding: var(--spacing-xs);
    text-align: left;
    border-bottom: 1px solid var(--color-border);
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  /* Specific override for action column to allow dropdowns to be visible */
  .results-table td.action-col {
    overflow: visible; /* Allow the dropdown to overflow the cell without being clipped */
  }
  
  .results-table th {
    background-color: var(--color-table-th);
    color: var(--color-white);
  }
  
  .results-table tr:hover {
    background-color: rgba(0, 130, 163, 0.1);
  }
  
  .btn-sm {
    padding: var(--spacing-xs) var(--spacing-xs);
    font-size: 0.75rem;
  }
  
  .name-col {
    width: 10%;
    white-space: nowrap;
  }
  
  .domain-col{
    width: 14%;
    white-space: nowrap;
  }
  
  .email-col{
    width: 30%;
    white-space: nowrap;
  }

  .uploaded-col{
    width: 12%;
    white-space: nowrap;
  }
  
  .result-col {
    width: 8%;
    white-space: nowrap;
  }
  
  .staus-col {
    width: 10%;
    white-space: nowrap;
  }

  .progress-col {
    width: 8%;
    white-space: nowrap;
  }
  
  .date-col {
    width: 12%;
    white-space: nowrap;
  }
  
  .action-col {
    width: 9%;
    white-space: nowrap;
  }
  
  .find-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--color-table-th);
    color: var(--color-white);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    white-space: nowrap; 
    font-size: var(--spacing-custom-xs);
  }
  
  .find-button:hover {
    background: var(--color-primary-light);
  }

  /* Custom styles for the Actions dropdown specific to EmailFinder component */
  /* ======================================================================== */

  /* Custom styles for the Actions button */
  .finder-actions-btn {
    padding: 0.15rem 0.4rem; /* Smaller padding */
    font-size: 0.75rem;     /* Smaller font size */
    min-width: auto;        /* Override any default min-width */
  }

  /* Custom hover effect for dropdown items */
  .finder-dropdown-item:hover,
  .finder-dropdown-item:focus {
    background-color: rgba(22, 73, 102, 0.1); /* Light version of theme color #164966 */
    color: #164966; /* Darken text on hover for better contrast */
  }

  /* Hide unexpected icon next to the Download button */
  .finder-dropdown-item > svg,
  .finder-dropdown-item > i {
    display: none !important;
  }

  .finder-dropdown-item::before {
    display: none !important; /* Hide if it's a ::before pseudo-element */
    content: "" !important;    /* Also clear content for pseudo-element */
  }