import React from "react";
import { Link } from "react-router-dom";
import "./NotAuthorized.css"; // We'll create this CSS file

const NotAuthorized: React.FC = () => {
  return (
    <div className="not-authorized-container">
      <div className="not-authorized-card">
        <div className="not-authorized-header">
          <h2>Access Denied</h2>
        </div>
        <div className="not-authorized-body">
          <div className="error-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#dc3545">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
            </svg>
          </div>
          <h3>403 - Forbidden</h3>
          <p className="error-message">
            You don't have permission to access this resource.
          </p>
          <p className="error-description">
            Please contact your administrator if you believe this is an error.
          </p>
          <Link to="/" className="home-link">
            <i className="fas fa-arrow-left me-2"></i>
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotAuthorized;