import React, { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { RootState } from "../../../app/store";
import { logout } from "../../../features/auth/store/authSlice";
import LogoutConfirmationModal from "../../../components/LogoutConfirmationModal";
import "./Header.css";

const Header: React.FC = () => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.auth.user);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      dispatch(logout());
      localStorage.removeItem("accessToken");
      localStorage.removeItem("refreshToken");
      localStorage.removeItem("roleId");
      navigate("/login", { replace: true, state: { authRedirect: false, fromLogout: true } });
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  // If user is not logged in, don't render the header
  if (!user) {
    return null;
  }

  return (
    <header className="dashboard-header">
      <div className="header-left">
        <span className="logo-text">RightEmails</span>
      </div>

      <div className="header-right" ref={dropdownRef}>
        <div
          className="user-menu"
          onClick={() => setShowDropdown(!showDropdown)}
          data-testid="user-menu"
        >
          <span className="username">
            Welcome, {user.first_name} {user.last_name}
          </span>
          <img
            src="https://img.icons8.com/ios-glyphs/30/0082A3/user-menu-male.png"
            alt="User menu"
            className="user-icon"
          />
        </div>
        {showDropdown && (
          <div className="dropdown-menu" data-testid="dropdown-menu">
            <button className="dropdown-item" onClick={() => {
              setShowDropdown(false);
              setIsLogoutModalOpen(true);
            }} data-testid="logout-button">
              Logout
            </button>
          </div>
        )}
      </div>

      {/* Logout Confirmation Modal */}
      <LogoutConfirmationModal
        isOpen={isLogoutModalOpen}
        onClose={() => setIsLogoutModalOpen(false)}
        onConfirm={handleLogout}
      />
    </header>
  );
};

export default Header;