import React, { useState, useEffect } from "react";
import apiClient from "../../../core/config/api";
import { ServerIPType, ServerDropdownOption } from "../../../types/models/ServerIPsType";

interface EditServerIPModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEditSuccess: (updatedServerIP: ServerIPType) => void;
  serverIP: ServerIPType | null;
  servers: ServerDropdownOption[];
}

const EditServerIPModal: React.FC<EditServerIPModalProps> = ({
  isOpen,
  onClose,
  onEditSuccess,
  serverIP,
  servers,
}) => {
  const [formData, setFormData] = useState({
    server_id: 0,
    ip_address: "",
    sub_domain: "",
    is_active: true,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    if (serverIP) {
      setFormData({
        server_id: serverIP.server_id,
        ip_address: serverIP.ip_address,
        sub_domain: serverIP.sub_domain,
        is_active: serverIP.is_active,
      });
    }
  }, [serverIP]);

  useEffect(() => {
    if (!isOpen) {
      setFormData({
        server_id: 0,
        ip_address: "",
        sub_domain: "",
        is_active: true,
      });
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
    }
  }, [isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === "is_active" ? value === "true" : 
              name === "server_id" ? Number(value) : 
              value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!serverIP) return;
    
    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.put(`/server-ips/${serverIP.id}`, formData);
      setSuccessMessage("Server IP updated successfully!");
      onEditSuccess(response.data);

      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      if (err.response?.data?.detail) {
        const apiErrors = err.response.data.detail;

        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              newErrors[field] = error.msg;
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to update server IP");
        }
      } else {
        setGeneralError(err.message || "Failed to update server IP. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !serverIP) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Edit Server IP</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Server</label>
                <select
                  name="server_id"
                  value={formData.server_id}
                  onChange={handleInputChange}
                  className={`form-select ${errors.server_id ? "is-invalid" : ""}`}
                  required
                >
                  <option value="0">Select a Server</option>
                  {servers.map((server) => (
                    <option key={server.id} value={server.id}>
                      {server.server_name}
                    </option>
                  ))}
                </select>
                {errors.server_id && (
                  <div className="invalid-feedback">{errors.server_id}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">IP Address</label>
                <input
                  type="text"
                  name="ip_address"
                  value={formData.ip_address}
                  onChange={handleInputChange}
                  className={`form-control ${errors.ip_address ? "is-invalid" : ""}`}
                  required
                />
                {errors.ip_address && (
                  <div className="invalid-feedback">{errors.ip_address}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Sub Domain</label>
                <input
                  type="text"
                  name="sub_domain"
                  value={formData.sub_domain}
                  onChange={handleInputChange}
                  className={`form-control ${errors.sub_domain ? "is-invalid" : ""}`}
                  maxLength={255}
                />
                {errors.sub_domain && (
                  <div className="invalid-feedback">{errors.sub_domain}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Status</label>
                <select
                  name="is_active"
                  value={String(formData.is_active)}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading || !formData.ip_address.trim() || formData.server_id === 0}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Updating...
                </>
              ) : "Update Server IP"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditServerIPModal;