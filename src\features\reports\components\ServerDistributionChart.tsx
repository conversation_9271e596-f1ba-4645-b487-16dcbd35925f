import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, ButtonGroup, Spinner, Table } from 'react-bootstrap';
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Cell, Legend, ResponsiveContainer, <PERSON>lt<PERSON>, <PERSON>Axis, YAxis } from 'recharts';
import apiClient from '../../../core/config/api';

interface ServerDistributionItem {
  server_id: number;
  server_name: string;
  total_requests: number;
  unique_client_ips: number;
  first_request_time: string;
  last_request_time: string;
}

interface ServerDistributionChartProps {
  isActive: boolean;
}

const COLORS = [
  '#0082A3', '#3CACAE', '#58C9C7', '#89E9E0', '#C9F9EF',
  '#005F75', '#007A8C', '#4EDADC', '#B5F5F6', '#D6FAFA'
];

const ServerDistributionChart: React.FC<ServerDistributionChartProps> = ({ isActive }) => {
  const [serverData, setServerData] = useState<ServerDistributionItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
  const hasLoadedRef = useRef(false);

  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();

    const fetchData = async () => {
      try {
        if (!isActive) {
          return;
        }
        setLoading(true);
        setError(null);

        const response = await apiClient.get('/server-distribution', {
          signal: controller.signal
        });

        if (!isMounted) return;

        let serverItems = [];

        if (Array.isArray(response.data)) {
          serverItems = response.data;
        } else if (response.data?.items) {
          serverItems = response.data.items;
        } else if (response.data?.servers) {
          serverItems = response.data.servers;
        } else {
          throw new Error('Unexpected data format from server');
        }

        setServerData(serverItems);
        setLoading(false);
        hasLoadedRef.current = true;
      } catch (err: any) {
        if (isMounted && err.name !== 'AbortError') {
          console.error('Error fetching server distribution data:', err);
          setError('Failed to load server distribution data');
          setLoading(false);
        }
      }
    };

    if (!hasLoadedRef.current) {
      fetchData();
    }

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [isActive]);

  const totalRequests = serverData.reduce((sum, item) => sum + item.total_requests, 0);
  const totalUniqueIPs = serverData.reduce((sum, item) => sum + item.unique_client_ips, 0);

  const handleDownload = () => {
    const headers = ['Server Name', 'Total Requests', 'Unique Client IPs', 'First Request', 'Last Request', 'Request %'];
    const csvRows = [
      headers.join(','),
      ...serverData.map(item => {
        const percentage = totalRequests > 0
          ? ((item.total_requests / totalRequests) * 100).toFixed(2)
          : '0';
        return [
          `"${item.server_name}"`,
          item.total_requests,
          item.unique_client_ips,
          new Date(item.first_request_time).toLocaleString(),
          new Date(item.last_request_time).toLocaleString(),
          `${percentage}%`
        ].join(',');
      })
    ];

    csvRows.push(`Total,${totalRequests},${totalUniqueIPs},,,100%`);

    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'server_distribution.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderChartView = () => {
    if (serverData.length === 0) {
      return (
        <div className="text-center p-5">
          <p>No server distribution data available.</p>
        </div>
      );
    }

    const chartData = serverData.map((item, index) => ({
      name: item.server_name,
      requests: item.total_requests,
      uniqueIps: item.unique_client_ips,
      percentage: Math.round((item.total_requests / totalRequests) * 100),
      color: COLORS[index % COLORS.length]
    }));

    // Calculate dynamic height based on number of items
    const chartHeight = Math.max(100, chartData.length * 30);
    const containerHeight = 600; // Fixed container height for scroll

    return (
      <div className="p-1">
        <div
          // style={{
          //   height: containerHeight,
          //   overflowY: 'auto',
          //   border: '1px solid #eee',
          //   borderRadius: '4px'
          // }}
        >
          <div style={{ height: chartHeight, minHeight: containerHeight }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{
                  top: 20,
                  right: 30,
                  bottom: 60
                }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" opacity={0.3} />
                <XAxis
                  type="number"
                  tick={{ fontSize: '0.75rem' }}
                />
                <YAxis
                  dataKey="name"
                  type="category"
                  width={180}
                  tick={{ fontSize: '0.75rem' }}
                  tickMargin={10}
                  interval={0}
                  tickFormatter={(value) => value.length > 25 ? `${value.slice(0, 25)}...` : value}
                />
                <Tooltip
                  formatter={(value: number, name: string) => {
                    if (name === 'requests') return [value.toLocaleString(), 'Total Requests'];
                    if (name === 'uniqueIps') return [value.toLocaleString(), 'Unique IPs'];
                    return [value, name];
                  }}
                  contentStyle={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)',
                    borderRadius: '4px',
                    fontSize: '0.8rem'
                  }}
                />
                <Legend
                  wrapperStyle={{ paddingTop: '5px' }}
                />
                <Bar
                  dataKey="requests"
                  name="Total Requests"
                  fill="#0082A3"
                  label={{
                    position: 'right',
                    formatter: (value: number) => value.toLocaleString(),
                    fontSize: '0.75rem'
                  }}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
        {renderSummary()}
      </div>
    );
  };

  const renderTableView = () => (
    <div className="table-responsive mt-3">
      <Table striped bordered hover className="reports-table">
        <thead className="table-header">
          <tr>
            <th>Server Name</th>
            <th>Total Requests</th>
            <th>Unique IPs</th>
            <th>First Request</th>
            <th>Last Request</th>
            <th>Percentage</th>
          </tr>
        </thead>
        <tbody>
          {serverData.map((item, index) => {
            const percentage = totalRequests > 0
              ? Math.round((item.total_requests / totalRequests) * 100)
              : 0;
            return (
              <tr key={index}>
                <td>
                  <span
                    className="color-indicator"
                    style={{
                      display: 'inline-block',
                      width: '12px',
                      height: '12px',
                      backgroundColor: COLORS[index % COLORS.length],
                      marginRight: '8px',
                      borderRadius: '2px'
                    }}
                  ></span>
                  {item.server_name}
                </td>
                <td>{item.total_requests.toLocaleString()}</td>
                <td>{item.unique_client_ips.toLocaleString()}</td>
                <td>{new Date(item.first_request_time).toLocaleString()}</td>
                <td>{new Date(item.last_request_time).toLocaleString()}</td>
                <td className="fw-bold">{percentage}%</td>
              </tr>
            );
          })}
          <tr className="table-active fw-bold">
            <td>Total</td>
            <td>{totalRequests.toLocaleString()}</td>
            <td>{totalUniqueIPs.toLocaleString()}</td>
            <td colSpan={2}></td>
            <td>100%</td>
          </tr>
        </tbody>
      </Table>
      {renderSummary()}
    </div>
  );

  const renderSummary = () => (
    <div className="p-3 bg-light rounded">
      <div className="d-flex align-items-center mb-2 fw-bold">
        <span>
         Total Requests: {totalRequests.toLocaleString()} | Unique IPs: {totalUniqueIPs.toLocaleString()}
        </span>
      </div>
      {/* <div className="d-flex flex-wrap gap-3">
        {serverData.map((item, index) => {
          const percentage = totalRequests > 0
            ? Math.round((item.total_requests / totalRequests) * 100)
            : 0;
          return (
            <div key={index} className="d-flex align-items-center">
              <span
                className="color-indicator"
                style={{
                  display: 'inline-block',
                  width: '12px',
                  height: '12px',
                  backgroundColor: COLORS[index % COLORS.length],
                  marginRight: '8px',
                  borderRadius: '2px'
                }}
              ></span>
              <span className="small">
                {item.server_name}: {item.total_requests.toLocaleString()} ({percentage}%)
              </span>
            </div>
          );
        })}
      </div> */}
    </div>
  );

  if (loading) return (
    <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
      <Spinner animation="border" variant="primary" />
    </div>
  );

  if (error) return (
    <div className="alert alert-danger my-3">{error}</div>
  );

  return (
    <div className="bg-transparent">
      <div className="d-flex flex-wrap justify-content-between align-items-center gap-3 mb-3">
        <ButtonGroup>
          <Button
            variant={viewMode === 'chart' ? 'primary' : 'outline-primary'}
            onClick={() => setViewMode('chart')}
            size="sm"
          >
            <i className="bi bi-bar-chart-fill me-1"></i> Chart
          </Button>
          <Button
            variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
            onClick={() => setViewMode('table')}
            size="sm"
          >
            <i className="bi bi-table me-1"></i> Table
          </Button>
        </ButtonGroup>

        <Button
          variant="outline-primary"
          onClick={handleDownload}
          size="sm"
          disabled={serverData.length === 0}
        >
          <i className="bi bi-download me-1"></i> Export
        </Button>
      </div>

      <div className="bg-white shadow-sm rounded">
        {viewMode === 'chart' ? renderChartView() : renderTableView()}
      </div>

      {serverData.length > 0 && (
        <div className="mt-3 text-end text-muted small">
          Showing {serverData.length} servers
        </div>
      )}
    </div>
  );
};

export default ServerDistributionChart;