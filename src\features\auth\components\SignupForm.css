.signup-container {
  display: flex;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background);
}

.signup-card {
  background: var(--color-white);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
}

.signup-header {
  text-align: center;
  margin-bottom: 1rem;
}

.signup-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-primary-dark);
  margin-bottom: 0.25rem;
}

.signup-subtitle {
  color: var(--color-text-light);
  font-size: 0.875rem;
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.password-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.phone-number-group {
  display: none; /* Hide phone number field */
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-primary-dark);
}

.password-toggle-group {
  text-align: center;
  margin: 0.25rem 0;
}

.password-toggle {
  background: none;
  border: none;
  color: var(--color-primary-light);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0;
}

.password-toggle:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

.signup-button {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
}

.signup-button:hover {
  background: linear-gradient(135deg, #4fa8d8 0%, #1f6091 100%);
}

.signup-link {
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-text-light);
  margin-top: 0.75rem;
}

.login-text {
  color: var(--color-primary-dark);
  font-weight: 500;
  text-decoration: none;
}

.login-text:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}

@media (max-width: 768px) {
  .signup-card {
    padding: 1.5rem;
    margin: 0 1rem;
  }

  .form-row,
  .password-row {
    grid-template-columns: 1fr;
  }
}