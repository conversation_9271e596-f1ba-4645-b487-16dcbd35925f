import { saveAs } from 'file-saver';
import React, { useEffect, useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Pagination from '../../../components/Pagination';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import Header from '../../../layouts/Header/components/Header';
import PartialDownloadConfirmationModal from './PartialDownloadConfirmationModal';

interface ApiRequestHistory {
  history: {
    id: number;
    request_origin: string;
    request_type: string;
    file_name: string;
    status: string;
    created_at: string;
    updated_at: string;
  };
  details: {
    data_count: number;
    progress: number;
    request_body: any[];
    results: any[];
  };
}

const ApiRequestHistoryPage: React.FC = () => {
  const [requests, setRequests] = useState<ApiRequestHistory[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [downloadingFiles, setDownloadingFiles] = useState<{ [key: string]: boolean }>({});
  const [downloadedFiles, setDownloadedFiles] = useState<{ [key: string]: boolean }>({});
  const [partialDownloadModal, setPartialDownloadModal] = useState({
    isOpen: false,
    id: 0,
    fileName: '',
    progress: 0
  });
  const [totalCount, setTotalCount] = useState<number>(0);

  const formatDateToISOString = (date: Date | undefined, isEndOfDay: boolean = false) => {
    if (!date) return undefined;

    // Create a new date object to avoid modifying the original
    const d = new Date(date);

    if (isEndOfDay) {
      d.setHours(23, 59, 59, 999);
    } else {
      d.setHours(0, 0, 0, 0);
    }

    return d.toISOString().slice(0, 19).replace('T', ' ');
  };

  const fetchTotalCount = async () => {
    try {
      const response = await apiClient.get("/verifier/count/with-origin");
      setTotalCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch API request history count:", error);
    }
  };

  const fetchApiRequests = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: currentPage,
        page_size: itemsPerPage,
      };

      if (searchTerm) params.request_origin = searchTerm.trim();

      if (startDate) {
        params.created_after = formatDateToISOString(startDate);

        // If no end date is set, use the end of the start date
        if (!endDate) {
          params.created_before = formatDateToISOString(startDate, true);
        }
      }

      if (endDate) {
        params.created_before = formatDateToISOString(endDate, true);

        // If no start date is set, use the start of the end date
        if (!startDate) {
          params.created_after = formatDateToISOString(endDate);
        }
      }

      if (statusFilter !== 'all') params.status = statusFilter;
      if (typeFilter !== 'all') params.request_type = typeFilter;

      const response = await apiClient.get('/api-history', { params });
      setRequests(response.data.data);
      setTotalPages(response.data.total_pages);
    } catch (error) {
      console.error('Failed to fetch API requests:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      fetchApiRequests();
      fetchTotalCount();
    }, 500);

    setSearchTimeout(timeout);

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTerm, currentPage, itemsPerPage, startDate, endDate, statusFilter, typeFilter]);

  const getRequestTypeLabel = (type: string) => {
    switch (type.toLowerCase()) {
      case 'verifier': return 'Email Verifier';
      case 'finder': return 'Email Finder';
      default: return type;
    }
  };

  const downloadPartialResults = async (id: number): Promise<void> => {
    const stateKey = `partial-${id}`;
    if (downloadingFiles[stateKey]) {
      return;
    }

    setDownloadingFiles(prev => ({ ...prev, [stateKey]: true }));

    try {
      const copyResponse = await apiClient.post(`/verifier-requests/${id}/copy-results`);

      if (copyResponse.data.message === "Verification results copied successfully") {
        const downloadResponse = await apiClient.get(`/verifier-requests/${id}/download-results`, {
          responseType: 'blob'
        });

        const filename = downloadResponse.headers['content-disposition']
          ?.split('filename=')[1]
          ?.replace(/"/g, '') || `partial_verification_api_results_${id}.csv`;

        saveAs(downloadResponse.data, filename);

        // Reset the downloading state after download completes
        setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));

        await fetchApiRequests();
      }
    } catch (error) {
      console.error('Failed to download partial results:', error);
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
      throw error;
    }
  };

  const downloadRequestData = async (id: number) => {
    const stateKey = `data-${id}`;
    if (downloadingFiles[stateKey]) {
      return;
    }

    setDownloadingFiles(prev => ({ ...prev, [stateKey]: true }));

    try {
      const response = await apiClient.get(`/finder-requests/${id}/download-request-data`, {
        responseType: 'blob'
      });
      const filename = response.headers['content-disposition']
        ?.split('filename=')[1]
        ?.replace(/"/g, '') || `request_${id}.csv`;

      saveAs(response.data, filename);

      // Reset the downloading state after download completes
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
    } catch (error) {
      console.error('Failed to download request data:', error);
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
    }
  };

  const downloadResults = async (id: number) => {
    const stateKey = `results-${id}`;
    if (downloadingFiles[stateKey]) {
      return;
    }

    setDownloadingFiles(prev => ({ ...prev, [stateKey]: true }));

    try {
      const response = await apiClient.get(`/verifier-requests/${id}/download-results`, {
        responseType: 'blob'
      });
      const filename = response.headers['content-disposition']
        ?.split('filename=')[1]
        ?.replace(/"/g, '') || `api_results_${id}.csv`;

      saveAs(response.data, filename);

      // Reset the downloading state after download completes
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
    } catch (error) {
      console.error('Failed to download results:', error);
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
    }
  };

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <h5 className="mb-0 text-white">
              <i className="fas fa-history me-2"></i>API Request History{totalCount > 0 && <span>({totalCount})</span>}
            </h5>
          </div>
          <div className="card-body p-3">
            <div className="filters-container">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search by request origin"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchTerm('')}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>
              <div className="filter-group right-filters">
                <div className="date-picker-container">
                  <DatePicker
                    selected={startDate || undefined}
                    onChange={(date: Date | null) => {
                      setStartDate(date || undefined);
                      // If end date is before start date, update end date
                      if (date && endDate && date > endDate) {
                        setEndDate(date);
                      }
                    }}
                    placeholderText="Start Date"
                    className="date-picker"
                    dateFormat="MM-dd-yyyy"
                    isClearable={false}
                    maxDate={new Date()}
                  />
                  {startDate && (
                    <button
                      className="clear-date"
                      onClick={() => setStartDate(undefined)}
                      aria-label="Clear start date"
                    >
                      &times;
                    </button>
                  )}
                </div>
                <div className="date-picker-container">
                  <DatePicker
                    selected={endDate || undefined}
                    onChange={(date: Date | null) => {
                      setEndDate(date || undefined);
                      // If start date is after end date, update start date
                      if (date && startDate && date < startDate) {
                        setStartDate(date);
                      }
                    }}
                    placeholderText="End Date"
                    className="date-picker"
                    dateFormat="MM-dd-yyyy"
                    minDate={startDate}
                    maxDate={new Date()}
                    isClearable={false}
                  />
                  {endDate && (
                    <button
                      className="clear-date"
                      onClick={() => setEndDate(undefined)}
                      aria-label="Clear end date"
                    >
                      &times;
                    </button>
                  )}
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="processing">Processing</option>
                  <option value="failed">Failed</option>
                  <option value="retrying">Retrying</option>
                  <option value="paused">Paused</option>
                  <option value="restarted">Restarted</option>
                </select>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Types</option>
                  <option value="verifier">Email Verifier</option>
                  <option value="finder">Email Finder</option>
                </select>
              </div>
            </div>

            <div className="table-responsive">
              <table className="uploads-table">
                <thead>
                  <tr>
                    <th className="id-col">Request ID</th>
                    <th className="origin-col">Request Origin</th>
                    <th className="type-col">Request Type</th>
                    <th className="data-col">Data</th>
                    <th className="status-col">Status</th>
                    <th className="count-col">Count</th>
                    <th className="progress-col">Progress</th>
                    <th className="date-col">Created At</th>
                    <th className="action-col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={9} className="text-center">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : requests.length === 0 ? (
                    <tr>
                      <td colSpan={9} className="text-center">
                        {searchTerm ?
                          'No results found for your search' :
                          'No API requests found'}
                      </td>
                    </tr>
                  ) : (
                    requests.map((request) => (
                      <tr key={request.history.id}>
                        <td className="id-col">#{request.history.id}</td>
                        <td className="origin-col">{request.history.request_origin}</td>
                        <td className="type-col">{getRequestTypeLabel(request.history.request_type)}</td>
                        <td className="action-col">
                          <div
                            onClick={() => {
                              if (!downloadedFiles[`data-${request.history.id}`] && !downloadingFiles[`data-${request.history.id}`]) {
                                downloadRequestData(request.history.id);
                              }
                            }}
                            className={`download-button ${downloadedFiles[`data-${request.history.id}`] ? 'disabled' : ''}`}
                            title={downloadedFiles[`data-${request.history.id}`] ? 'Already downloaded' : 'Download request data'}
                            style={{ cursor: 'pointer' }}
                          >
                            {downloadingFiles[`data-${request.history.id}`] ? (
                              <div className="spinner-border spinner-border-sm" role="status" style={{
                                width: '20px',
                                height: '20px',
                                borderWidth: '2px',
                                color: '#164966'
                              }}>
                                <span className="visually-hidden">Downloading...</span>
                              </div>
                            ) : (
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke={downloadedFiles[`data-${request.history.id}`] ? '#999' : 'currentColor'}
                              >
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                              </svg>
                            )}
                          </div>
                        </td>
                        <td>
                          {request.history.status}
                        </td>
                        <td>{request.details.data_count}</td>
                        <td>
                          <div className="progress">
                            <div
                              className={`progress-bar ${getProgressBarColor(request.details.progress)}`}
                              style={{ width: `${request.details.progress}%` }}
                            >
                              {request.details.progress}%
                            </div>
                          </div>
                        </td>
                        <td>{new Date(request.history.created_at).toLocaleString()}</td>
                        <td className="action-col">
                          <div
                            onClick={() => {
                              if (!downloadedFiles[`results-${request.history.id}`] && !downloadingFiles[`results-${request.history.id}`]) {
                                downloadResults(request.history.id);
                              }
                            }}
                            className={`download-button ${downloadedFiles[`results-${request.history.id}`] ? 'disabled' : ''}`}
                            title={downloadedFiles[`results-${request.history.id}`] ? 'Already downloaded' : 'Download results'}
                            style={{ cursor: 'pointer' }}
                          >
                            {downloadingFiles[`results-${request.history.id}`] ? (
                              <div className="spinner-border spinner-border-sm" role="status" style={{
                                width: '20px',
                                height: '20px',
                                borderWidth: '2px',
                                color: '#164966'
                              }}>
                                <span className="visually-hidden">Downloading...</span>
                              </div>
                            ) : (
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke={downloadedFiles[`results-${request.history.id}`] ? '#999' : 'currentColor'}
                              >
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                              </svg>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {requests.length > 0 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                onPerPageChange={setItemsPerPage}
                perPage={itemsPerPage}
              />
            )}

            <PartialDownloadConfirmationModal
              isOpen={partialDownloadModal.isOpen}
              onClose={() => setPartialDownloadModal(prev => ({ ...prev, isOpen: false }))}
              onConfirm={async () => {
                await downloadPartialResults(partialDownloadModal.id);
              }}
              fileName={partialDownloadModal.fileName}
              progress={partialDownloadModal.progress}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Reuse these helper functions from previous components
function getProgressBarColor(progress: number): string {
  if (progress === 100) return 'bg-success';
  if (progress > 70) return 'bg-info';
  if (progress > 30) return '';
  return 'bg-warning';
}

export default ApiRequestHistoryPage;