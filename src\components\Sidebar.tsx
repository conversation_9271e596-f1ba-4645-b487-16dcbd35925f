import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { RootState } from "../app/store";
import { logout } from "../features/auth/store/authSlice";
import LogoutConfirmationModal from "./LogoutConfirmationModal";
import "./Sidebar.css";

const Sidebar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const roleId = useSelector((state: RootState) => state.auth.roleId);

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const handleLogout = async () => {
    try {
      dispatch(logout());
      localStorage.removeItem("accessToken");
      localStorage.removeItem("refreshToken");
      navigate("/login", { replace: true, state: { authRedirect: false, fromLogout: true } });
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  // Define links for role_id 1 (Admin)
  const adminLinks = [
    { to: "/users-list", icon: "https://img.icons8.com/ios-filled/50/ffffff/conference.png", label: "Users List" },
    { to: "/users-roles", icon: "https://img.icons8.com/ios-filled/50/ffffff/user-group-man-man.png", label: "User Roles" },
    { to: "/email-patterns",icon: "https://img.icons8.com/ios-filled/50/ffffff/email-sign.png", label: "Email Patterns" },
    { to: "/servers", icon: "https://img.icons8.com/ios-filled/50/ffffff/domain.png", label: "Server Lists" },
    { to: "/server-ips", icon: "https://img.icons8.com/ios-filled/50/ffffff/ip-address.png", label: "Server IPs" },
    { to: "/server-capacity", icon: "https://img.icons8.com/ios-filled/50/ffffff/server.png", label: "Server Capacity" },
    { to: "/blocked-ips", icon: "https://img.icons8.com/ios-filled/50/ffffff/block.png", label: "Blocked IPs" },
    { to: "/bounce-messages", icon: "https://img.icons8.com/ios-filled/50/ffffff/returned-mail.png", label: "Bounce Messages" },
    { to: "/api-history", icon: "https://img.icons8.com/ios-filled/50/000000/api-settings.png", label: "API History" },
    { to: "/reports", icon: "https://img.icons8.com/ios-filled/50/ffffff/combo-chart.png", label: "Reports" },
  ];

  // Define links for role_id 2 (User)
  const userLinks = [
    { to: "/bulk-email-verify", icon: "https://img.icons8.com/ios-filled/50/ffffff/check-file.png", label: "Bulk Email Verify" },
    { to: "/bulk-email-finder", icon: "https://img.icons8.com/ios-filled/50/ffffff/email.png", label: "Bulk Email Finder" },
    { to: "/verify-email", icon: "https://img.icons8.com/ios-filled/50/ffffff/verified-account.png", label: "Verify Email" },
    { to: "/email-finder", icon: "https://img.icons8.com/ios-filled/50/ffffff/search.png", label: "Email Finder" },
    { to: "/logs-history", icon: "https://img.icons8.com/ios-filled/50/000000/activity-history.png", label: "Logs History" }
  ];

  // Combine links for admin (role_id: 1)
  const links = roleId === 1 ? [...adminLinks, ...userLinks] : userLinks;

  return (
    <>
      {/* Mobile Toggle Button */}
      <button className="mobile-toggle" onClick={toggleSidebar}>
        <img
          src="https://img.icons8.com/ios-filled/50/0082A3/menu--v1.png"
          alt="Toggle Menu"
        />
      </button>

      {/* Sidebar */}
      <div className={`sidebar ${isOpen ? "open" : ""}`}>
        <div className="sidebar-header text-center">
          <h2>RightEmails</h2>
          <button className="close-button" onClick={toggleSidebar}>
            <img
              src="https://img.icons8.com/ios-filled/50/164966/delete-sign.png"
              alt="Close Menu"
            />
          </button>
        </div>
        <div className="sidebar-menu">
          {/* Render filtered links */}
          {links.map((link) => (
            <Link
              key={link.to}
              to={link.to}
              className={`sidebar-item ${location.pathname === link.to ? "selected" : ""}`}
              onClick={toggleSidebar}
            >
              <img src={link.icon} alt={link.label} />
              <span>{link.label}</span>
            </Link>
          ))}

          {/* Logout Link */}
          <div
            className={`sidebar-item ${location.pathname === "/logout" ? "selected" : ""}`}
            onClick={() => setIsLogoutModalOpen(true)}
          >
            <img
              src="https://img.icons8.com/ios-filled/50/ffffff/logout-rounded.png"
              alt="Logout"
            />
            <span>Logout</span>
          </div>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      <LogoutConfirmationModal
        isOpen={isLogoutModalOpen}
        onClose={() => setIsLogoutModalOpen(false)}
        onConfirm={handleLogout}
      />
    </>
  );
};

export default Sidebar;