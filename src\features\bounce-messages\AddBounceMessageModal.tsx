import axios from "axios";
import React, { useEffect, useState } from "react";
import apiClient from "../../core/config/api";
import { BounceMessage } from "../../types/models/BounceMessage";

interface AddBounceMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSuccess: (newMessage: BounceMessage) => void;
}

const PREDEFINED_CATEGORIES = ["Valid", "Invalid", "Catch-All"];

const AddBounceMessageModal: React.FC<AddBounceMessageModalProps> = ({
  isOpen,
  onClose,
  onAddSuccess,
}) => {
  const [formData, setFormData] = useState({
    message: "",
    category: PREDEFINED_CATEGORIES[0],
    status: true,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    if (!isOpen) {
      setFormData({
        message: "",
        category: PREDEFINED_CATEGORIES[0],
        status: true,
      });
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
    }
  }, [isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: name === "status" ? value === "true" : value.trim(),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.post("/bounce-messages", formData);
      setSuccessMessage("Bounce message added successfully!");
      onAddSuccess(response.data);

      // Reset form after successful submission to allow adding another message
      setFormData({
        message: "",
        category: PREDEFINED_CATEGORIES[0],
        status: true,
      });
    } catch (err: any) {
      if (axios.isAxiosError(err) && err.response) {
        if (err.response.data?.detail) {
          const apiErrors = err.response.data.detail;

          if (Array.isArray(apiErrors)) {
            const newErrors: Record<string, string> = {};
            apiErrors.forEach((error: any) => {
              if (error.loc && error.loc.length > 1) {
                const field = error.loc[error.loc.length - 1];
                newErrors[field] = error.msg;
              } else {
                setGeneralError(error.msg || "An error occurred");
              }
            });
            setErrors(newErrors);
          } else {
            setGeneralError(apiErrors.message || "Failed to add bounce message");
          }
        } else {
          setGeneralError(
            err.response.data?.message ||
            `Error: ${err.response.status} ${err.response.statusText}`
          );
        }
      } else {
        setGeneralError(err.message || "Failed to add bounce message. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Bounce Message</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Category</label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className={`form-select ${errors.category ? "is-invalid" : ""}`}
                  required
                >
                  {PREDEFINED_CATEGORIES.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <div className="invalid-feedback">{errors.category}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Status</label>
                <select
                  name="status"
                  value={String(formData.status)}
                  onChange={handleInputChange}
                  className={`form-select ${errors.status ? "is-invalid" : ""}`}
                >
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
                {errors.status && (
                  <div className="invalid-feedback">{errors.status}</div>
                )}
              </div>
            </div>
          </div>

          <div className="form-group mb-3">
            <label className="form-label">Message</label>
            <textarea
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              className={`form-control ${errors.message ? "is-invalid" : ""}`}
              rows={5}
              required
              placeholder="Enter bounce message text here..."
            />
            {errors.message && (
              <div className="invalid-feedback">{errors.message}</div>
            )}
            <small className="form-text text-muted">
              Enter the exact bounce message pattern you want to match.
            </small>
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Close
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading || !formData.message.trim()}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Adding...
                </>
              ) : "Add Bounce Message"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddBounceMessageModal; 