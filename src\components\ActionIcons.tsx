import React, { SVGProps } from 'react';

interface ActionIconProps extends SVGProps<SVGSVGElement> {
  action: string;
  iconStyle?: React.CSSProperties;
}

export const ActionIcon = ({ action, iconStyle = {}, ...props }: ActionIconProps) => {
  const defaultStyle = { marginRight: '8px', ...iconStyle };
  
  switch (action) {
    case "View Reports":
      return (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="#0082A3" style={defaultStyle} {...props}>
          <path d="M3 3v18h18V3H3zm16 16H5V5h14v14zM9 7h2v10H9zm4 0h2v7h-2zm-8 3h2v4H5z" />
        </svg>
      );
    case "View Credits":
      return (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="#0082A3" style={defaultStyle} {...props}>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z" />
        </svg>
      );
    case "Edit":
      return (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="#0082A3" style={defaultStyle} {...props}>
          <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z" />
        </svg>
      );
    case "Delete":
      return (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="#D9363E" style={defaultStyle} {...props}>
          <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" />
        </svg>
      );
    case "Download":
      return (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="#0082A3" style={defaultStyle} {...props}>
          <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
        </svg>
      );
    case "Activate":
      return (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="#28a745" style={defaultStyle} {...props}>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      );
    case "Inactivate":
      return (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="#dc3545" style={defaultStyle} {...props}>
          <path d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/>
        </svg>
      );
    default:
      return null;
  }
};

// Pre-defined components for convenience
export const ViewReportsIcon = (props: SVGProps<SVGSVGElement>) => (
  <ActionIcon action="View Reports" {...props} />
);

export const ViewCreditsIcon = (props: SVGProps<SVGSVGElement>) => (
  <ActionIcon action="View Credits" {...props} />
);

export const EditIcon = (props: SVGProps<SVGSVGElement>) => (
  <ActionIcon action="Edit" {...props} />
);

export const DeleteIcon = (props: SVGProps<SVGSVGElement>) => (
  <ActionIcon action="Delete" {...props} />
);

export const DownloadIcon = (props: SVGProps<SVGSVGElement>) => (
  <ActionIcon action="Download" {...props} />
);

export const ActivateIcon = (props: SVGProps<SVGSVGElement>) => (
  <ActionIcon action="Activate" {...props} />
);

export const InactivateIcon = (props: SVGProps<SVGSVGElement>) => (
  <ActionIcon action="Inactivate" {...props} />
);