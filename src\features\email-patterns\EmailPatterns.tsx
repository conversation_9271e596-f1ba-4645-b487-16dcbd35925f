import React, { useEffect, useState, useRef } from 'react';
import { EmailPattern } from '../../types/models/EmailPattern';
import AddEmailPatternModal from './AddEmailPatternModal';
import EditEmailPatternModal from './EditEmailPatternModal';
import apiClient from '../../core/config/api';
import Header from '../../layouts/Header/components/Header';
import Sidebar from '../../components/Sidebar';
import Pagination from '../../components/Pagination';
import DeleteConfirmationModal from '../user/components/DeleteConfirmationModal';
import DropdownMenuPortal from '../email/components/DropdownMenuPortal';
import { DeleteIcon, EditIcon } from "../../components/ActionIcons";

const EmailPatterns: React.FC = () => {
  const [patterns, setPatterns] = useState<EmailPattern[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedPattern, setSelectedPattern] = useState<EmailPattern | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [patternToDelete, setPatternToDelete] = useState<EmailPattern | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [activeCount, setActiveCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});
  // Calculate the starting number for the current page
  const startingNumber = (currentPage - 1) * dataPerPage;

  useEffect(() => {
    const patternsCssLink = document.createElement('link');
    patternsCssLink.href = '/css/email-patterns.css';
    patternsCssLink.rel = 'stylesheet';
    patternsCssLink.id = 'email-patterns-css';
    document.head.appendChild(patternsCssLink);

    // Cleanup on unmount
    return () => {
      const cssLink = document.getElementById('email-patterns-css');
      if (cssLink) {
        cssLink.remove();
      }
    };
  }, []);

  const fetchEmailPatterns = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get("/patterns", {
        params: {
          page: currentPage,
          page_size: dataPerPage,
          pattern_contains: searchTerm,
        },
      });

      setPatterns(response.data.data || []);
      setTotalPages(response.data.total_pages || Math.ceil(response.data.total / dataPerPage));
    } catch (error) {
      console.error("Failed to fetch email patterns:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchActiveCount = async () => {
    try {
      const response = await apiClient.get("/patterns/count/active");
      setActiveCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch active patterns count:", error);
    }
  };

  useEffect(() => {
    fetchEmailPatterns();
    fetchActiveCount();
  }, [currentPage, dataPerPage, searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setDataPerPage(newPerPage);
    setCurrentPage(1);
  };

  const handleDeletePattern = async () => {
    if (!patternToDelete) return;

    setDeleteLoading(true);
    setDeleteError(null);

    try {
      await apiClient.delete(`/patterns/${patternToDelete.id}`);
      fetchEmailPatterns();
      fetchActiveCount();
      setIsDeleteModalOpen(false);
    } catch (error: any) {
      console.error("Failed to delete email pattern:", error);
      setDeleteError(
        error.response?.data?.message ||
        error.message ||
        "Failed to delete email pattern. Please try again."
      );
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleUpdateEmailPattern = (updatedPattern: EmailPattern) => {
    setPatterns(prevPatterns =>
      prevPatterns.map(pattern =>
        pattern.id === updatedPattern.id ? updatedPattern : pattern
      )
    );
    fetchActiveCount();
  };

  const handleAddEmailPattern = (newPattern: EmailPattern) => {
    setPatterns(prev => [...prev, newPattern]);
    fetchActiveCount();
  };

  const getStatusColor = (status: boolean): string => {
    return status ? 'badge bg-success' : 'badge bg-danger';
  };

  // Click outside handler to close dropdown menu
  const handleClickOutside = (event: MouseEvent) => {
    if (openActionMenuId !== null) {
      let clickedInsideDropdown = false;

      // Check if click was inside the current open dropdown button
      const openButtonRef = buttonRefs.current[openActionMenuId];
      if (openButtonRef && openButtonRef.contains(event.target as Node)) {
        clickedInsideDropdown = true;
      }

      // Check if click was inside dropdown menu
      const dropdownElements = document.querySelectorAll('.dropdown-portal');
      dropdownElements.forEach(element => {
        if (element.contains(event.target as Node)) {
          clickedInsideDropdown = true;
        }
      });

      // Also check for custom dropdown items
      const dropdownItemElements = document.querySelectorAll('.custom-dropdown-item');
      dropdownItemElements.forEach(element => {
        if (element.contains(event.target as Node)) {
          clickedInsideDropdown = true;
        }
      });

      if (!clickedInsideDropdown) {
        setOpenActionMenuId(null);
      }
    }
  };

  // Add click outside listener
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openActionMenuId]);

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <h5 className="mb-0 text-white">
              <i className="fas fa-envelope-open-text me-2"></i>Email Patterns{activeCount > 0 && <span>({activeCount})</span>}
            </h5>
          </div>
          <div className="card-body p-3">
            <div className="filters-container">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search by pattern..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                  style={{
                    paddingRight: '30px',
                    borderRadius: '8px'
                  }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchTerm('')}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>
              <div className="filter-group right-filters">
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="submit-button"
                >
                  <i className="fas fa-plus me-2"></i>Add Email Pattern
                </button>
              </div>
            </div>

            <div className="table-responsive">
              <table className="uploads-table">
                <thead>
                  <tr>
                    <th className="id-col">ID</th>
                    <th className="pattern-col">Pattern</th>
                    <th className="comments-col">Comments</th>
                    <th className="status-col">Status</th>
                    <th className="date-col">Updated At</th>
                    <th className="action-col text-center">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="text-center py-4">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : patterns.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="text-center py-4">
                        {searchTerm
                          ? `No email patterns found matching "${searchTerm}"`
                          : 'No email patterns found'}
                      </td>
                    </tr>
                  ) : (
                    patterns.map((pattern, i) => (
                      <tr key={pattern.id}>
                        <td className="id-col">#{startingNumber + i + 1}</td>
                        <td className="pattern-col">
                          <div className="pattern-content text-truncate">
                            {pattern.pattern}
                          </div>
                        </td>
                        <td className="comments-col">{pattern.comments || 'N/A'}</td>
                        <td className="status-col">
                          <span className={getStatusColor(pattern.is_active)}>
                            {pattern.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="date-col">{new Date(pattern.updated_at).toLocaleString()}</td>
                        <td className="action-col text-center">
                          <div className="custom-dropdown-container">
                            <button
                              ref={(el) => { buttonRefs.current[pattern.id] = el }}
                              type="button"
                              className="btn btn-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                setOpenActionMenuId(openActionMenuId === pattern.id ? null : pattern.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                                padding: '0.2rem 0.5rem',
                                fontSize: '0.75rem',
                                height: '24px',
                                width: '80px',
                                cursor: 'pointer',
                                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === pattern.id}
                              buttonElement={buttonRefs.current[pattern.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {/* Edit Option */}
                              <button
                                type="button"
                                className="dropdown-item custom-dropdown-item"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedPattern(pattern);
                                  setIsEditModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px'
                                }}
                              >
                                <EditIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">Edit</span>
                              </button>

                              {/* Delete Option */}
                              <button
                                type="button"
                                className="dropdown-item custom-dropdown-item"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setPatternToDelete(pattern);
                                  setIsDeleteModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px'
                                }}
                              >
                                <DeleteIcon width="16" height="16" fill="#D9363E" />
                                <span className="ms-2">Delete</span>
                              </button>
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={handlePerPageChange}
              perPage={dataPerPage}
            />
          </div>
        </div>
      </div>

      {/* Add Modal */}
      <AddEmailPatternModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAddSuccess={handleAddEmailPattern}
        existingPatterns={patterns}
      />

      {/* Edit Modal */}
      {selectedPattern && (
        <EditEmailPatternModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          pattern={selectedPattern}
          onUpdateSuccess={handleUpdateEmailPattern}
          existingPatterns={patterns}
        />
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && patternToDelete && (
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setDeleteError(null);
          }}
          onConfirm={handleDeletePattern}
          userName={patternToDelete.pattern}
          entityType="Email Pattern"
          title="Delete Email Pattern"
          message={deleteError || `Are you sure you want to delete the email pattern "${patternToDelete.pattern}"? This action cannot be undone.`}
          loading={deleteLoading}
        />
      )}
    </div>
  );
};

export default EmailPatterns;