import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const PageLayout = ({ children }: { children: React.ReactNode }) => {
  const location = useLocation();

  useEffect(() => {
    // Get the current route name
    const routeName = location.pathname.split('/')[1] || 'home';
    const pageCssPath = `/css/${routeName}.css`;
    const modalCssPath = '/css/modal.css'; // Always load modal.css

    // Create link elements
    const pageLink = document.createElement('link');
    pageLink.href = pageCssPath;
    pageLink.rel = 'stylesheet';
    pageLink.className = 'page-css';

    const modalLink = document.createElement('link');
    modalLink.href = modalCssPath;
    modalLink.rel = 'stylesheet';
    modalLink.className = 'global-modal-css';

    // Add to head
    document.head.appendChild(pageLink);
    document.head.appendChild(modalLink);

    // Cleanup function - remove only page-specific CSS
    return () => {
      const pageLinks = document.querySelectorAll('link.page-css');
      pageLinks.forEach(el => el.remove());
    };
  }, [location.pathname]);

  return <>{children}</>;
};

export default PageLayout;