.message-col {
    max-width: 300px;
}

.truncate-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.clickable {
    cursor: pointer;
    color: #0082A3;
}

.clickable:hover {
    text-decoration: underline;
}

.filter-inputs {
    display: flex;
    gap: 10px;
}

.date-input {
    width: 120px;
    padding: 6px 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
}

.level-select {
    padding: 6px 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
}

.right-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.date-picker-container {
    position: relative;
    display: inline-block;
}

.date-picker {
    padding: 10px 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 16px;
    width: 200px;
    height: 42px;
}

.filter-select {
    padding: 10px 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 16px;
    background-color: white;
    min-width: 220px;
    height: 42px;
}

.clear-date {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #999;
    font-size: 18px;
    line-height: 1;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    z-index: 1;
}

.clear-date:hover {
    background-color: #ff6b6b;
}

.clear-search {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    font-size: 18px;
    line-height: 1;
    cursor: pointer;
    padding: 0;
}

/* Table styling */
.error-logs-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    table-layout: fixed;
}

.error-logs-table th {
    background-color: #396A7D;
    color: white;
    font-weight: 500;
    text-align: left;
    padding: 10px 8px;
    font-size: 14px;
}

.error-logs-table th:last-child {
    border-right: none;
}

.error-logs-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #0082A3;
    color: #333;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.error-logs-table tr:hover td {
    background-color: rgba(0, 130, 163, 0.05);
}

.request-col {
    width: 8%;
    word-break: break-all;
    white-space: normal;
}

.origin-col {
    width: 20%;
    white-space: normal;
}

.source-col {
    width: 12%;
    white-space: normal;
}

.level-col {
    width: 8%;
    white-space: normal;
}

.message-col {
    width: 25%;
    white-space: nowrap;
}

.date-col {
    width: 15%;
    white-space: normal;
}

.action-col {
    width: 12%;
    word-break: break-all;
    white-space: normal;
}


/* Level badges */
.error-badge {
    display: inline-block;
    background-color: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.warning-badge {
    display: inline-block;
    background-color: #ffc107;
    color: #212529;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.info-badge {
    display: inline-block;
    background-color: #17a2b8;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.debug-badge {
    display: inline-block;
    background-color: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Action buttons */
.actions-cell {
    padding: 12px 8px;
    text-align: center;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.edit-icon,
.delete-icon,
.download-icon {
    cursor: pointer;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.edit-icon:hover {
    background-color: rgba(0, 130, 163, 0.1);
}

.delete-icon:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

.download-icon:hover {
    background-color: rgba(0, 130, 163, 0.1);
}

.edit-icon svg {
    stroke: #0082A3;
}

.delete-icon svg {
    stroke: #dc3545;
}

.download-icon svg {
    stroke: #0082A3;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.1rem 0.25rem; /* Even smaller padding */
  font-size: 0.7rem;     /* Smaller font size */
  min-width: 60px;       /* Fixed smaller width */
  max-width: 70px;       /* Max width */
  border-radius: 0 !important; /* Ensure straight lines */
  margin: 0 auto; /* Center the button */
  height: 22px; /* Fixed height */
  line-height: 1; /* Ensure text is centered vertically */
}

/* Center the dropdown within the action column */
.action-col .dropdown {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Dropdown items styling */
.dropdown-item {
  border-radius: 0 !important;
  padding: 0.25rem 0.75rem !important; /* Smaller padding */
  font-size: 0.8rem !important;      /* Smaller font */
  height: 24px !important;           /* Fixed height */
  line-height: 1 !important;         /* Better vertical centering */
  display: flex !important;
  align-items: center !important;
}