import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Table, Button, ButtonGroup, Spinner } from 'react-bootstrap';
import apiClient from '../../../core/config/api';
import { format } from 'date-fns';

interface DailyMetricData {
  date: string;
  processed: number;
  valid: number;
  invalid: number;
}

interface DailyMetricsChartProps {
  isActive: boolean;
}

const COLORS = {
  Processed: '#0082A3',
  Valid: '#28a745',
  Invalid: '#dc3545'
};

const DailyMetricsChart: React.FC<DailyMetricsChartProps> = ({ isActive }) => {
  const [metrics, setMetrics] = useState<DailyMetricData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
  const hasLoadedRef = useRef(false);

  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();

    const fetchData = async () => {
      try {
        if (!isActive) return;
        setLoading(true);
        setError(null);

        const response = await apiClient.get('/daily-metrics/last-7-days', {
          signal: controller.signal
        });

        if (!isMounted) return;

        const metricsData = response.data?.daily_data?.map((item: any) => ({
          date: item.date,
          processed: item.processed,
          valid: item.valid,
          invalid: item.invalid
        })) || [];

        const sortedMetrics = metricsData.sort((a: DailyMetricData, b: DailyMetricData) =>
          new Date(a.date).getTime() - new Date(b.date).getTime()
        );

        setMetrics(sortedMetrics);
        setLoading(false);
        hasLoadedRef.current = true;
      } catch (err: any) {
        if (isMounted && err.name !== 'AbortError') {
          setError('Failed to load daily metrics data');
          setLoading(false);
        }
      }
    };

    if (!hasLoadedRef.current) fetchData();

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [isActive]);

  const handleDownloadData = () => {
    if (metrics.length === 0) return;

    const csvContent = [
      ['Date', 'Emails Processed', 'Valid Emails', 'Invalid Emails'].join(','),
      ...metrics.map(item => [
        format(new Date(item.date), 'yyyy-MM-dd'),
        item.processed,
        item.valid,
        item.invalid
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `daily_metrics_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderTableView = () => (
    <div className="table-responsive mt-3">
      <Table striped bordered hover className="reports-table">
        <thead>
          <tr>
            <th>Date</th>
            <th>Processed</th>
            <th>Valid</th>
            <th>Invalid</th>
            <th>Success Rate</th>
          </tr>
        </thead>
        <tbody>
          {metrics.map((day, index) => {
            const successRate = day.processed > 0 ?
              Math.round((day.valid / day.processed) * 100) : 0;
            return (
              <tr key={index}>
                <td>{format(new Date(day.date), 'MMM dd')}</td>
                <td>{day.processed.toLocaleString()}</td>
                <td className="text-success">{day.valid.toLocaleString()}</td>
                <td className="text-danger">{day.invalid.toLocaleString()}</td>
                <td className="fw-bold">{successRate}%</td>
              </tr>
            );
          })}
        </tbody>
      </Table>
    </div>
  );

  const renderBarChart = () => (
    <div className="chart-container bg-white shadow-sm rounded p-3">
      <div style={{ height: '500px', minHeight: '400px' }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={metrics}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tickFormatter={(date) => format(new Date(date), 'MM/dd')}
              angle={-45}
              textAnchor="end"
              interval={0}
              height={80}
              tick={{ fontSize: '0.75rem' }}
            />
            <YAxis
              tick={{ fontSize: '0.75rem' }}
            />
            <Tooltip
              labelFormatter={(date) => format(new Date(date), 'MMM dd, yyyy')}
              contentStyle={{
                backgroundColor: 'var(--color-background)',
                borderColor: 'var(--color-border)',
                borderRadius: '4px',
                fontSize: '0.8rem'
              }}
            />
            <Legend
              wrapperStyle={{ paddingTop: '20px' }}
              style={{ fontSize: '0.8rem' }}
            />
            <Bar
              dataKey="processed"
              name="Processed"
              fill={COLORS.Processed}
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="valid"
              name="Valid"
              fill={COLORS.Valid}
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="invalid"
              name="Invalid"
              fill={COLORS.Invalid}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const renderSummary = () => {
    const totalProcessed = metrics.reduce((sum, day) => sum + day.processed, 0);
    const totalValid = metrics.reduce((sum, day) => sum + day.valid, 0);
    const totalInvalid = metrics.reduce((sum, day) => sum + day.invalid, 0);

    return (
      <div className="mt-3 p-3 bg-light border-top">
        <div className="d-flex flex-wrap gap-4">
          <div className="d-flex align-items-center">
            <span
              className="color-indicator"
              style={{
                display: 'inline-block',
                width: '12px',
                height: '12px',
                backgroundColor: COLORS.Processed,
                marginRight: '8px',
                borderRadius: '2px'
              }}
            />
            <span className="small">Processed: {totalProcessed.toLocaleString()}</span>
          </div>
          <div className="d-flex align-items-center">
            <span
              className="color-indicator"
              style={{
                display: 'inline-block',
                width: '12px',
                height: '12px',
                backgroundColor: COLORS.Valid,
                marginRight: '8px',
                borderRadius: '2px'
              }}
            />
            <span className="small">Valid: {totalValid.toLocaleString()}</span>
          </div>
          <div className="d-flex align-items-center">
            <span
              className="color-indicator"
              style={{
                display: 'inline-block',
                width: '12px',
                height: '12px',
                backgroundColor: COLORS.Invalid,
                marginRight: '8px',
                borderRadius: '2px'
              }}
            />
            <span className="small">Invalid: {totalInvalid.toLocaleString()}</span>
          </div>
        </div>
      </div>
    );
  };

  if (loading) return (
    <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
      <Spinner animation="border" variant="primary" />
    </div>
  );

  if (error) return (
    <div className="alert alert-danger my-3">
      {error}
      <Button
        variant="link"
        onClick={() => {
          hasLoadedRef.current = false;
          setLoading(true);
          setError(null);
        }}
      >
        Retry
      </Button>
    </div>
  );

  return (
    <div className="bg-transparent">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <ButtonGroup>
          <Button
            variant={viewMode === 'chart' ? 'primary' : 'outline-primary'}
            onClick={() => setViewMode('chart')}
            size="sm"
          >
            <i className="bi bi-bar-chart me-1"></i> Chart
          </Button>
          <Button
            variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
            onClick={() => setViewMode('table')}
            size="sm"
          >
            <i className="bi bi-table me-1"></i> Table
          </Button>
        </ButtonGroup>

        <Button
          variant="outline-primary"
          onClick={handleDownloadData}
          size="sm"
          disabled={metrics.length === 0}
        >
          <i className="bi bi-download me-1"></i> Export
        </Button>
      </div>

      {viewMode === 'chart' ? (
        <>
          {renderBarChart()}
          {renderSummary()}
        </>
      ) : renderTableView()}

      {metrics.length > 0 && (
        <div className="mt-3 text-end small">
          Showing data for last 7 days
        </div>
      )}
    </div>
  );
};

export default DailyMetricsChart;