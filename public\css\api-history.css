/* Bulk Email Verifier Container */
.bulk-email-verifier-container {
    padding: var(--spacing-md);
}

/* Card Header */
.card-header {
    background: var(--color-primary-dark);
    color: var(--color-white);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.card-header h5 {
    font-size: 1.25rem;
    font-weight: 600;
}

/* Form Styling */
.bulk-upload-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-group {
    margin-bottom: var(--spacing-sm);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
}

.form-input {
    width: 100%;
    padding: var(--spacing-xs);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-sm);
}

/* Uploads Table */
.uploads-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    margin-bottom: var(--spacing-md);
}

.uploads-table th,
.uploads-table td {
    padding: var(--spacing-xs);
    text-align: left;
    border-bottom: 1px solid var(--color-border);
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.uploads-table th {
    background-color: var(--color-table-th);
    color: var(--color-white);
}

.uploads-table tr:hover {
    background-color: rgba(0, 130, 163, 0.1);
}

/* Actions Column */
.uploads-table td:last-child {
    white-space: nowrap;
}

.bulk-upload-form {
    padding: var(--spacing-sm);
}

.form-group {
    margin-bottom: var(--spacing-sm);
}

.form-label {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.form-control {
    width: 100%;
    padding: var(--spacing-xs);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-sm);
}

.btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-outline-primary {
    border: 1px solid var(--color-primary-dark);
    color: var(--color-primary-dark);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--color-primary-dark);
    color: var(--color-white);
}

.project-col {
    width: 15%;
    white-space: nowrap;
}

.id-col {
    width: 12%;
    white-space: nowrap;
}

.origin-col {
    width: 25%;
    white-space: nowrap;
}

.type-col {
    width: 10%;
    white-space: nowrap;
}

.data-col {
    width: 8%;
    white-space: nowrap;
}

.status-col {
    width: 8%;
    white-space: nowrap;
}

.count-col {
    width: 8%;
    white-space: nowrap;
}

.progress-col {
    width: 8%;
    white-space: nowrap;
}

.date-col {
    width: 13%;
    white-space: nowrap;
}

.action-col {
    width: 8%;
    white-space: nowrap;
    overflow: visible;
}

/* Hide unexpected icon next to the Download button in the Actions dropdown */
.actions-dropdown-download-item > svg,
.actions-dropdown-download-item > i {
  display: none !important;
}

.actions-dropdown-download-item::before {
  display: none !important; /* Hide if it's a ::before pseudo-element */
  content: "" !important;    /* Also clear content for pseudo-element */
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.15rem 0.4rem; /* Smaller padding */
  font-size: 0.75rem;     /* Smaller font size */
  min-width: auto;        /* Override any default min-width from btn or btn-sm if necessary */
  border-radius: 0 !important; /* Ensure straight lines */
}

/* Custom hover effect for the download item in the actions dropdown */
.actions-dropdown-download-item:hover,
.actions-dropdown-download-item:focus {
  background-color: rgba(22, 73, 102, 0.1); /* Light version of your theme color #164966 */
  color: #164966; /* Optional: darken text on hover for better contrast if needed */
}

/* Straight borders for dropdown menu items */
.dropdown-item {
  border-radius: 0 !important;
}

/* Ensure no border radius on any dropdowns throughout the app */
.dropdown-menu, 
.dropdown-toggle,
.btn-sm,
.btn-outline-secondary,
.dropdown button {
  border-radius: 0 !important;
}

/* Restore spinner border-radius to make spinners circular again */
.spinner-border,
.spinner-border-sm,
.spinner,
[class*="spinner-"] {
  border-radius: 50% !important; /* Override to keep spinners circular */
}