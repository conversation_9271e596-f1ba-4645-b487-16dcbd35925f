import React from 'react';

interface KPICardProps {
  title: React.ReactNode;
  value: string | number;
  color?: string;
  className?: string;
  isLoading?: boolean;
}

const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  className = '',
  isLoading = false
}) => {
  return (
    <div className={`kpi-card ${className}`}>
      <div className="kpi-card-inner">
        <div className="kpi-card-content">
          <div className="kpi-card-title">{title}</div>
          {isLoading ? (
            <div className="metric-loading-skeleton">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <div className="kpi-card-value">{value}</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default KPICard;