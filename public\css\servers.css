/* Server List Container */
.servers-list-container {
  padding: var(--spacing-md);
}

/* Table Controls */
.table-controls {
  margin-bottom: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Servers Table */
.servers-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}
.servers-table th{
  padding: var(--spacing-xs);
  text-align: center !important; /* Center all headers */
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.servers-table td {
  padding: var(--spacing-sm);
  text-align: center; /* Center all cell content */
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.servers-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
}

.servers-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

/* Column Alignments */
.servers-table .id-col {
  width: 5%;
  text-align: center;
}

.servers-table .name-col {
  width: 15%;
  text-align: center; /* Center name column content */
}

.servers-table .url-col {
  width: 30%;
  text-align: center; /* Center URL column content */
}

.servers-table .ip-col {
  width: 20%;
  text-align: center; /* Center IP column content */
}

.servers-table .purpose-col {
  width: 8%;
  text-align: center;
}

.servers-table .status-col {
  width: 8%;
  text-align: center;
}

.servers-table .status-col .badge {
  display: inline-block;
  min-width: 60px;
  text-align: center;
}

.servers-table .date-col {
  width: 15%;
  text-align: center; /* Center date column content */
  padding-right: 0; /* Remove right padding as text is centered */
}

.servers-table .action-col {
  width: 9%;
  text-align: center;
}

/* Action Column Specific Styles */
.servers-table .action-col {
  text-align: center;
  padding: var(--spacing-xs);
}

/* Add styles for custom dropdown container in action column */
.custom-dropdown-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.15rem 0.4rem;
  font-size: 0.75rem;
  min-width: auto;
  border-radius: 0 !important;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Center the dropdown within the action column */
.action-col .dropdown {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Custom hover effect for the dropdown items */
.dropdown-item:hover,
.dropdown-item:focus {
  background-color: rgba(22, 73, 102, 0.1);
  color: #164966;
}

/* Straight borders for dropdown menu items */
.dropdown-item {
  border-radius: 0 !important;
  display: flex;
  align-items: center;
  width: 100%;
  text-align: left;
  background-color: transparent;
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
}


.custom-dropdown-toggle {
  margin: 0 auto;
}

/* Restore spinner border-radius to make spinners circular */
.spinner-border,
.spinner-border-sm,
.spinner,
[class*="spinner-"] {
  border-radius: 50% !important;
}

/* Responsive Design */
@media (max-width: 992px) {
  .servers-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .servers-table th,
  .servers-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .servers-table .action-col {
    width: 120px;
  }
}

@media (max-width: 768px) {
  .card-header h5 {
    font-size: 1.1rem;
  }

  .servers-table {
    font-size: 0.8125rem;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
}