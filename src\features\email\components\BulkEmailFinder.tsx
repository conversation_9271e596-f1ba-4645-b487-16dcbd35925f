import { saveAs } from 'file-saver';
import React, { useEffect, useState, useRef } from 'react';
import { useSelector } from "react-redux";
import { RootState } from '../../../app/store';
import Pagination from '../../../components/Pagination';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import Header from '../../../layouts/Header/components/Header';
import BulkEmailFinderModal from './BulkEmailFinderModal';
import PartialDownloadConfirmationModal from './PartialDownloadConfirmationModal';
import DropdownMenuPortal from './DropdownMenuPortal';

interface BulkFinderResult {
  history: {
    id: number;
    project_name: string;
    first_name: string;
    last_name: string;
    email: string;
    file_name: string;
    status: string;
    created_at: string;
    updated_at: string;
  };
  details: {
    data_count: number;
    progress: number;
  } | null;
}

const BulkEmailFinder: React.FC = () => {
  const roleId = useSelector((state: RootState) => state.auth.roleId);
  const [results, setResults] = useState<BulkFinderResult[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [partialDownloadModal, setPartialDownloadModal] = useState({
    isOpen: false,
    id: 0,
    fileName: '',
    progress: 0
  });
  const [downloadingFiles, setDownloadingFiles] = useState<{ [key: string]: boolean }>({});
  const [downloadedFiles, setDownloadedFiles] = useState<{ [key: string]: boolean }>({});
  const [totalCount, setTotalCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  const fetchBulkFinderCount = async () => {
    try {
      const response = await apiClient.get("/verifier/count/bulk-finder");
      setTotalCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch bulk finder count:", error);
    }
  };

  const fetchBulkFinderResults = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: currentPage,
        page_size: dataPerPage,
        request_mode: 'bulk'
      };

      if (searchTerm) {
        params.project_name_contains = searchTerm;
      }

      const response = await apiClient.get('/finder-requests', { params });
      setResults(response.data.data);
      setTotalPages(response.data.total_pages);
    } catch (error) {
      console.error('Failed to fetch bulk finder results:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      fetchBulkFinderResults();
      fetchBulkFinderCount();
    }, 500);

    setSearchTimeout(timeout);

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTerm, currentPage, dataPerPage]);

  useEffect(() => {
    fetchBulkFinderResults();
    fetchBulkFinderCount();
  }, [currentPage, dataPerPage]);

  const downloadTemplate = () => {
    const csvContent = "first_name,last_name,domain\n";
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, 'email_finder_template.csv');
  };

  const downloadUploadedData = async (id: number) => {
    const stateKey = `data-${id}`;

    if (downloadingFiles[stateKey] || downloadedFiles[stateKey]) {
      return;
    }

    setDownloadingFiles(prev => ({ ...prev, [stateKey]: true }));

    try {
      const response = await apiClient.get(`/finder-requests/${id}/download-request-data`, {
        responseType: 'blob'
      });

      const filename = response.headers['content-disposition']
        ?.split('filename=')[1]
        ?.replace(/"/g, '') || `finder_data_${id}.csv`;
      saveAs(response.data, filename);

      // Reset both states after download completes to enable the button
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
      setDownloadedFiles(prev => ({ ...prev, [stateKey]: false }));
    } catch (error) {
      console.error('Failed to download results:', error);
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
      setDownloadedFiles(prev => ({ ...prev, [stateKey]: false }));
    }
  };

  const downloadPartialResults = async (id: number): Promise<void> => {
    const stateKey = `results-${id}`;
    if (downloadingFiles[stateKey] || downloadedFiles[stateKey]) {
      return;
    }

    setDownloadingFiles(prev => ({ ...prev, [stateKey]: true }));

    try {
      const copyResponse = await apiClient.post(`/verifier-requests/${id}/copy-results`);

      if (copyResponse.data.message === "Verification results copied successfully") {
        const downloadResponse = await apiClient.get(`/finder-requests/${id}/download-results`, {
          responseType: 'blob'
        });

        const filename = downloadResponse.headers['content-disposition']
          ?.split('filename=')[1]
          ?.replace(/"/g, '') || `partial_finder_results_${id}.csv`;
        saveAs(downloadResponse.data, filename);

        // Reset both states after download completes to enable the button
        setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
        setDownloadedFiles(prev => ({ ...prev, [stateKey]: false }));

        await fetchBulkFinderResults();
      }
    } catch (error) {
      console.error('Failed to download partial results:', error);
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
      setDownloadedFiles(prev => ({ ...prev, [stateKey]: false }));
      throw error;
    }
  };

  const downloadResults = async (id: number) => {
    const stateKey = `results-${id}`;
    if (downloadingFiles[stateKey] || downloadedFiles[stateKey]) {
      return;
    }

    setDownloadingFiles(prev => ({ ...prev, [stateKey]: true }));

    try {
      const response = await apiClient.get(`/finder-requests/${id}/download-results`, {
        responseType: 'blob'
      });

      const filename = response.headers['content-disposition']
        ?.split('filename=')[1]
        ?.replace(/"/g, '') || `finder_results_${id}.csv`;
      saveAs(response.data, filename);

      // Reset both states after download completes to enable the button
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
      setDownloadedFiles(prev => ({ ...prev, [stateKey]: false }));
    } catch (error) {
      console.error('Failed to download results:', error);
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
      setDownloadedFiles(prev => ({ ...prev, [stateKey]: false }));
    }
  };

  const handleUploadSuccess = () => {
    fetchBulkFinderResults();
  };

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <h5 className="mb-0 text-white">
              <i className="fas fa-search me-2"></i>Bulk Email Finder{totalCount > 0 && <span>({totalCount})</span>}
            </h5>
          </div>
          <div className="card-body p-3">
            <div className="filters-container">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search by project name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchTerm('')}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>
              <div className="filter-group right-filters">
                <button
                  onClick={downloadTemplate}
                  className="download-template-button"
                >
                  <i className="fas fa-download me-2"></i>Download Template
                </button>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="submit-button"
                >
                  <i className="fas fa-upload me-2"></i>Bulk Upload
                </button>
              </div>
            </div>

            <div className="table-responsive">
              <table className="uploads-table">
                <thead>
                  <tr>
                    <th className="project-col">Project Name</th>
                    {roleId === 1 ? (
                      <th className="uploaded-col">Uploaded By</th>
                    ) : (
                      <th className="file-col">File Name</th>
                    )}
                    <th className="data-col">Data</th>
                    <th className="status-col">Status</th>
                    <th className="count-col">Count</th>
                    <th className="progress-col">Progress</th>
                    <th className="date-col">Uploaded At</th>
                    <th className="action-col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={7} className="text-center">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : results.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="text-center">
                        {searchTerm ?
                          'No results found for your search' :
                          'No bulk finder requests found'}
                      </td>
                    </tr>
                  ) : (
                    results.map((result) => (
                      <tr key={result.history.id}>
                        <td>{result.history.project_name}</td>
                        <td>
                          {roleId === 1 ? (
                            `${result.history.first_name || ''} ${result.history.last_name || ''}`.trim() || 'N/A'
                          ) : (
                            result.history.file_name
                          )}
                        </td>
                        <td className="action-col">
                          <div
                            onClick={() => {
                              const stateKey = `data-${result.history.id}`;
                              if (!downloadingFiles[stateKey] && !downloadedFiles[stateKey]) {
                                downloadUploadedData(result.history.id);
                              }
                            }}
                            className={`download-button ${downloadingFiles[`data-${result.history.id}`] || downloadedFiles[`data-${result.history.id}`] ? 'disabled' : ''}`}
                            title={downloadingFiles[`data-${result.history.id}`] ? 'Downloading...' : downloadedFiles[`data-${result.history.id}`] ? 'Already downloaded' : 'Download data'}
                          >
                            {downloadingFiles[`data-${result.history.id}`] ? (
                              <div className="spinner-border spinner-border-sm" role="status">
                                <span className="visually-hidden">Downloading...</span>
                              </div>
                            ) : (
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke={downloadedFiles[`data-${result.history.id}`] ? '#999' : 'currentColor'}
                              >
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                              </svg>
                            )}
                          </div>
                        </td>
                        <td>
                          {result.history.status}
                        </td>
                        <td>{result.details?.data_count || 0}</td>
                        <td>
                          <div className="progress">
                            <div
                              className={`progress-bar ${getProgressBarColor(result.details?.progress || 0)}`}
                              style={{ width: `${result.details?.progress || 0}%` }}
                            >
                              {result.details?.progress || 0}%
                            </div>
                          </div>
                        </td>
                        <td>{new Date(result.history.created_at).toLocaleString()}</td>
                        <td className="action-col">
                          {/* <div className="dropdown" style={{ position: 'relative' }}>
                            <button
                              ref={(el) => { buttonRefs.current[result.history.id] = el }}
                              type="button"
                              className="btn btn-outline-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenActionMenuId(openActionMenuId === result.history.id ? null : result.history.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                              }}
                            >
                              <span>Actions</span>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === result.history.id}
                              buttonElement={buttonRefs.current[result.history.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                            >
                              <button
                                type="button"
                                className="dropdown-item actions-dropdown-download-item"
                                onClick={() => {
                                  const resultKey = `results-${result.history.id}`;
                                  const partialKey = `partial-${result.history.id}`;

                                  if (!downloadingFiles[resultKey] && !downloadingFiles[partialKey] &&
                                    !downloadedFiles[resultKey] && !downloadedFiles[partialKey]) {
                                    if (result.history.status === 'completed') {
                                      downloadResults(result.history.id);
                                    } else if (result.history.status !== 'failed') {
                                      setPartialDownloadModal({
                                        isOpen: true,
                                        id: result.history.id,
                                        fileName: result.history.file_name,
                                        progress: result.details?.progress || 0
                                      });
                                    }
                                  }
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.375rem 1rem',
                                  fontSize: '0.875rem',
                                  cursor: 'pointer',
                                }}
                                disabled={(downloadingFiles[`results-${result.history.id}`] || downloadingFiles[`partial-${result.history.id}`] ||
                                  downloadedFiles[`results-${result.history.id}`] || downloadedFiles[`partial-${result.history.id}`] ||
                                  result.history.status === 'failed')}
                              >
                                Download
                              </button>
                            </DropdownMenuPortal>
                          </div> */}
                          <div
                            onClick={() => {
                              const resultKey = `results-${result.history.id}`;
                              const partialKey = `partial-${result.history.id}`;

                              if (!downloadingFiles[resultKey] && !downloadingFiles[partialKey] &&
                                !downloadedFiles[resultKey] && !downloadedFiles[partialKey]) {
                                if (result.history.status === 'completed') {
                                  downloadResults(result.history.id);
                                } else if (result.history.status !== 'failed') {
                                  setPartialDownloadModal({
                                    isOpen: true,
                                    id: result.history.id,
                                    fileName: result.history.file_name,
                                    progress: result.details?.progress || 0
                                  });
                                }
                              }
                            }}
                            className={`download-button ${(downloadingFiles[`results-${result.history.id}`] || downloadingFiles[`partial-${result.history.id}`] ||
                              downloadedFiles[`results-${result.history.id}`] || downloadedFiles[`partial-${result.history.id}`] ||
                              result.history.status === 'failed')
                              ? 'disabled' : ''}`}
                            title={
                              downloadingFiles[`results-${result.history.id}`] || downloadingFiles[`partial-${result.history.id}`]
                                ? 'Downloading...'
                                : downloadedFiles[`results-${result.history.id}`] || downloadedFiles[`partial-${result.history.id}`]
                                  ? 'Already downloaded'
                                  : result.history.status === 'completed'
                                    ? 'Download Results'
                                    : result.history.status === 'failed'
                                      ? 'Download not available (Finder failed)'
                                      : 'Download Partial Results'
                            }
                          >
                            {downloadingFiles[`results-${result.history.id}`] || downloadingFiles[`partial-${result.history.id}`] ? (
                              <div className="spinner-border spinner-border-sm" role="status">
                                <span className="visually-hidden">Downloading...</span>
                              </div>
                            ) : (
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke={
                                  (downloadedFiles[`results-${result.history.id}`] || downloadedFiles[`partial-${result.history.id}`] || result.history.status === 'failed')
                                    ? '#999' : 'currentColor'
                                }
                              >
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                              </svg>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {results.length > 0 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                onPerPageChange={setDataPerPage}
                perPage={dataPerPage}
              />
            )}

            <BulkEmailFinderModal
              isOpen={isModalOpen}
              onClose={() => {
                setIsModalOpen(false);
                fetchBulkFinderResults();
              }}
              onUploadSuccess={handleUploadSuccess}
            />

            <PartialDownloadConfirmationModal
              isOpen={partialDownloadModal.isOpen}
              onClose={() => setPartialDownloadModal(prev => ({ ...prev, isOpen: false }))}
              onConfirm={async () => {
                await downloadPartialResults(partialDownloadModal.id);
              }}
              fileName={partialDownloadModal.fileName}
              progress={partialDownloadModal.progress}
            />

          </div>
        </div>
      </div>
    </div>
  );
};

function getProgressBarColor(progress: number): string {
  if (progress === 100) return 'bg-success';
  if (progress > 70) return 'bg-info';
  if (progress > 30) return '';
  return 'bg-warning';
}

export default BulkEmailFinder;