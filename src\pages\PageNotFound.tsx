import React from "react";
import { Link } from "react-router-dom";
import "./PageNotFound.css"; // We'll create this CSS file

const PageNotFound: React.FC = () => {
  return (
    <div className="page-not-found-container">
      <div className="page-not-found-card">
        <div className="page-not-found-header">
          <h2>Page Not Found</h2>
        </div>
        <div className="page-not-found-body">
          <div className="error-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#dc3545">
              <path d="M3 3l18 18M3 21L21 3" strokeWidth="2" />
              <circle cx="12" cy="12" r="10" strokeWidth="1.5" />
            </svg>
          </div>
          <h3>404 - Page Not Found</h3>
          <p className="error-message">
            The page you're looking for doesn't exist or has been moved.
          </p>
          <p className="error-description">
            Please check the URL or navigate back to our homepage.
          </p>
          <div className="action-buttons">
            <Link to="/" className="home-link">
              <i className="fas fa-home me-2"></i>
              Return Home
            </Link>
            <button className="back-button" onClick={() => window.history.back()}>
              <i className="fas fa-arrow-left me-2"></i>
              Go Back
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PageNotFound;