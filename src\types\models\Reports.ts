export interface RequestHistory {
  id: number;
  user_id: number | null;
  project_name: string | null;
  file_name: string | null;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying' | 'paused' | 'restarted';
  request_type: 'verifier' | 'finder';
  request_origin: string | null;
  created_at: string;
  updated_at: string;
}

export interface RequestDetail {
  id: number;
  request_history_id: number;
  data_count: number;
  request_type: string;
  request_body: Array<Record<string, any>>;
  results: Array<Record<string, any>> | null;
  progress: number;
  created_at: string;
  updated_at: string;
}

export interface DailyMetrics {
  date: string;
  emailsProcessed: number;
  successCount: number;
  failureCount: number;
}

export interface UserMetrics {
  userId: string;
  emailsProcessed: number;
  successRate: number;
}

export interface EmailValidationStatus {
  status: 'Valid' | 'Catch-All' | 'Invalid';
  count: number;
}

export interface SourceDistribution {
  source: string;
  count: number;
  validation?: EmailValidationStatus[];
}

export interface RequestTypeBreakdown {
  type: 'Email Finder' | 'Email Verifier';
  count: number;
}

export interface RequestTypeDistribution {
  requestType: string;
  count: number;
}

export interface UserDetailedReport {
  userId: string;
  userName: string;
  totalUploaded: number;
  totalVerified: number;
  valid: number;
  catchAll: number;
  invalid: number;
  validationRate: number;
  lastActivity: string;
}

export interface SourceDistributionData {
  sources: {
    source: string;
    count: number;
  }[];
  validationResults: {
    valid: number;
    invalid: number;
    catchAll: number;
    unknown: number;
  };
}

// API configuration is handled by apiClient

// KPI Card interfaces matching backend schemas
export interface ValidationStatusMetrics {
  valid: number;
  catch_all: number;
  invalid: number;
  total: number;
}

export interface RequestTypeMetrics {
  finder: number;
  verifier: number;
  total: number;
}

export interface TotalCountMetrics {
  other_source_requests: number;
  total_users: number;
}

export interface DashboardMetrics {
  validation_status: ValidationStatusMetrics;
  request_type: RequestTypeMetrics;
  total_counts: TotalCountMetrics;
}

export interface UserDetailedReport {
  userId: string;
  userName: string;
  totalUploaded: number;
  totalVerified: number;
  valid: number;
  catchAll: number;
  invalid: number;
  validationRate: number;
  lastActivity: string;
}
