import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setCredentials } from "../store/authSlice";
import { authService } from "../../../services/authService";
import "./LoginForm.css";

const LoginForm: React.FC = () => {
  const [loginCredentials, setLoginCredentials] = useState({ 
    email: "", 
    password: "" 
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [infoMessage, setInfoMessage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  // Get the redirect location from the state
  const from = location.state?.from?.pathname || "/";

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLoginCredentials((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setInfoMessage(null);
  
    try {
      setIsSubmitting(true);
      const response = await authService.login(loginCredentials);
  
      // Dispatch the login action with the API response
      dispatch(setCredentials(response));
  
      // Redirect based on role_id
      if (response.role_id === 2) {
        navigate("/bulk-email-verify");
      } else if (response.role_id === 1) {
        navigate("/users-list");
      }
    } catch (err: any) {
      let errorMessage = "Login failed. Please try again.";
      
      if (err.response) {
        // Handle API response errors
        if (err.response.data?.detail) {
          errorMessage = err.response.data.detail;
        } else if (err.response.data?.message) {
          errorMessage = err.response.data.message;
        } else if (err.response.status === 401) {
          errorMessage = "Invalid email or password";
        } else if (err.response.status === 403) {
          errorMessage = "Account not verified. Please check your email.";
        }
      } else if (err.message) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    const message = searchParams.get('message');
    const unauthorized = searchParams.get('unauthorized');
    
    if (message) {
      setInfoMessage(message);
    }
    
    if (unauthorized) {
      setError('You need to login to access that page');
    }
  }, [searchParams]);

  // Display a message if the user was redirected to the login page
  useEffect(() => {
    if (from !== "/") {
      setInfoMessage("You need to log in to access this page.");
    }
  }, [from]);

  return (
    <div className="login-container">
      <div className="login-card">
        <header className="login-header">
          <h1 className="login-title">Sign In</h1>
          <p className="login-subtitle">Sign in to your account</p>
        </header>

        <form onSubmit={handleSubmit} className="login-form">
          {infoMessage && (
            <div className="info-message">
              {infoMessage}
            </div>
          )}
          
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <div className="input-group">
            <label htmlFor="email" className="input-label">Email</label>
            <input
              id="email"
              type="email"
              name="email"
              value={loginCredentials.email}
              onChange={handleChange}
              className="form-input"
              placeholder="Enter your email"
              required
            />
          </div>

          <div className="input-group">
            <div className="password-header">
              <div className="col-md-9">
                <label htmlFor="password" className="input-label">Password</label>
              </div>
              <div className="col-md-3">
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? "Hide" : "Show"}
                </button>
              </div>
            </div>
            <input
              id="password"
              type={showPassword ? "text" : "password"}
              name="password"
              value={loginCredentials.password}
              onChange={handleChange}
              className="form-input"
              placeholder="Enter your password"
              required
            />
          </div>

          <button 
            type="submit" 
            className="login-button" 
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="spinner"></span>
                Logging in...
              </>
            ) : "Continue"}
          </button>

          <div className="login-link">
            Don't have an account?{" "}
            <Link to="/signup" className="login-text">
              Create account
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;