import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { EmailPattern } from '../../types/models/EmailPattern';
import apiClient from '../../core/config/api';

interface EditEmailPatternModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateSuccess: (pattern: EmailPattern) => void;
  pattern: EmailPattern | null;
  existingPatterns?: EmailPattern[];
}

const EditEmailPatternModal: React.FC<EditEmailPatternModalProps> = ({
  isOpen,
  onClose,
  onUpdateSuccess,
  pattern,
  existingPatterns = [],
}) => {
  const [formData, setFormData] = useState({
    pattern: '',
    comments: '',
    is_active: true,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  const isDuplicatePattern = (patternText: string): boolean => {
    return existingPatterns.some(
      existingPattern => 
        existingPattern.pattern.toLowerCase() === patternText.toLowerCase() &&
        existingPattern.id !== pattern?.id
    );
  };

  const isValidPatternFormat = (patternText: string): boolean => {
    // Must contain @ symbol
    const hasAtSymbol = patternText.includes('@');

    // Must contain @{domain}
    const hasDomainPlaceholder = patternText.includes('@{domain}');

    // Check for valid structure (no consecutive special characters)
    const hasValidStructure = !/([._-]{2,})/.test(patternText);

    return hasAtSymbol && hasDomainPlaceholder && hasValidStructure;
  };

  useEffect(() => {
    if (pattern) {
      setFormData({
        pattern: pattern.pattern || '',
        comments: pattern.comments || '',
        is_active: pattern.is_active,
      });
    }
  }, [pattern]);

  useEffect(() => {
    if (!isOpen) {
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
    }
  }, [isOpen]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    const patternText = formData.pattern.trim();

    if (!patternText) {
      newErrors.pattern = 'Pattern is required';
    } else if (patternText.length < 2) {
      newErrors.pattern = 'Pattern must be at least 2 characters long';
    } else if (patternText.length > 255) {
      newErrors.pattern = 'Pattern must be less than 255 characters long';
    } else if (patternText.includes(' ')) {
      newErrors.pattern = 'Pattern should not contain spaces. Use dots, underscores or hyphens instead.';
    } else if (!isValidPatternFormat(patternText)) {
      newErrors.pattern = 'Pattern format is invalid. Must include @{domain}. Avoid consecutive special characters.';
    } else if (isDuplicatePattern(patternText)) {
      newErrors.pattern = 'This pattern already exists. Please use a different pattern.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const target = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: target.checked,
      });
    } else if (name === "is_active") {
      setFormData({
        ...formData,
        [name]: value === "true",
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !pattern) return;

    setIsSubmitting(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.put(`/patterns/${pattern.id}`, formData);
      setSuccessMessage('Email pattern updated successfully!');
      onUpdateSuccess(response.data);
    } catch (error: any) {
      console.error('Error updating email pattern:', error);
      if (axios.isAxiosError(error) && error.response) {
        if (error.response.status === 409) {
          setErrors({
            pattern: 'This pattern already exists. Please use a different pattern.'
          });
        } else if (error.response.data?.detail) {
          const apiErrors = error.response.data.detail;
          if (Array.isArray(apiErrors)) {
            const newErrors: Record<string, string> = {};
            apiErrors.forEach((error: any) => {
              if (error.loc && error.loc.length > 1) {
                const field = error.loc[error.loc.length - 1];
                newErrors[field] = error.msg;
              } else {
                setGeneralError(error.msg || "An error occurred");
              }
            });
            setErrors(newErrors);
          } else if (typeof apiErrors === 'string') {
            setGeneralError(apiErrors);
          } else if (apiErrors.message) {
            setGeneralError(apiErrors.message);
          } else {
            setGeneralError(JSON.stringify(apiErrors));
          }
        } else if (error.response.data?.message) {
          setGeneralError(error.response.data.message);
        } else if (error.response.status === 409) {
          setGeneralError('Pattern already exists. Please use a different pattern.');
        } else {
          setGeneralError(`Error: ${error.response.status} ${error.response.statusText}`);
        }
      } else {
        setGeneralError(error.message || 'Failed to update email pattern. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay email-pattern-modal">
      <div className="modal-content" style={{ padding: "1.75rem" }}>
        <div className="modal-header" style={{ background: "#164966", color: "white", margin: "-1.75rem -1.75rem 1.5rem" }}>
          <h3>Edit Email Pattern</h3>
          <button
            type="button"
            className="close-button"
            onClick={handleClose}
            disabled={isSubmitting}
            style={{ color: "white", fontSize: "1.5rem", background: "none", border: "none" }}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message d-flex align-items-center justify-content-center">
              <i className="fas fa-check-circle me-2"></i>
              <strong>{successMessage}</strong>
            </div>
          )}

          <div className="row mb-3">
            <div className="col-md-8">
              <div className="form-group">
                <label className="form-label mb-2">Pattern <span className="text-danger">*</span></label>
                <input
                  type="text"
                  name="pattern"
                  value={formData.pattern}
                  onChange={handleChange}
                  className={`form-control ${errors.pattern ? "is-invalid" : ""}`}
                  required
                  placeholder="Enter pattern e.g. {first}.{last}@{domain}"
                />
                {errors.pattern && (
                  <div className="invalid-feedback">{errors.pattern}</div>
                )}
              </div>
            </div>
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label mb-2">Status</label>
                <select
                  name="is_active"
                  value={String(formData.is_active)}
                  onChange={handleChange}
                  className="form-select"
                >
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>
          </div>

          <div className="form-group mb-4">
            <label className="form-label mb-2">Comments</label>
            <textarea
              name="comments"
              value={formData.comments}
              onChange={handleChange}
              className="form-control"
              rows={5}
              placeholder="Enter comments (optional)"
            />
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Close
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isSubmitting || !formData.pattern.trim()}
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Updating...
                </>
              ) : 'Update Email Pattern'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditEmailPatternModal;