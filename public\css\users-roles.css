.add-role-form {
  background: var(--color-white);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--spacing-md);
}

.add-role-form h6 {
  color: var(--color-primary-dark);
  font-size: 1.1rem;
  margin-bottom: var(--spacing-sm);
}

.add-role-form .row {
  align-items: center;
}

.add-role-form .form-input {
  width: 100%;
  padding: var(--spacing-xs);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
}

.add-role-form .add-button {
  width: 100%;
  padding: var(--spacing-xs);
  background: var(--color-primary-dark);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
}

.add-role-form .add-button:hover {
  background: var(--color-primary-light);
}

.roles-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}

.roles-table th,
.roles-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.roles-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
}

.roles-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

.roles-table td:last-child {
  white-space: nowrap;
}

.id-col {
  width: 10%;
  white-space: nowrap;
}

.name-col {
  width: 30%;
  white-space: nowrap;
}

.status-col {
  width: 15%;
  white-space: nowrap;
}

.date-col {
  width: 30%;
  white-space: nowrap;
}

.action-col {
  width: 15%;
  white-space: nowrap;
  overflow: visible;
}

/* Action Column Specific Styles */
.users-table .action-col {
  text-align: center;
  padding: var(--spacing-xs);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xs);
}

.action-icon {
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-icon svg {
  width: 16px;
  height: 16px;
}

.edit-icon {
  color: var(--color-primary-dark);
}

.edit-icon:hover {
  color: var(--color-primary-light);
  background-color: rgba(0, 123, 255, 0.1);
}

.delete-icon {
  color: var(--color-error);
}

.delete-icon:hover {
  color: var(--color-error-dark);
  background-color: rgba(220, 53, 69, 0.1);
}

/* Dropdown styling */
.action-col .dropdown {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.1rem 0.25rem;
  font-size: 0.7rem;
  min-width: 60px;
  max-width: 70px;
  border-radius: 0 !important;
  margin: 0 auto;
  height: 22px;
  line-height: 1;
}

/* Custom hover effect for the dropdown items */
.dropdown-item:hover,
.dropdown-item:focus {
  background-color: rgba(22, 73, 102, 0.1);
  color: #164966;
}

/* Straight borders for dropdown menu items */
.dropdown-item {
  border-radius: 0 !important;
  padding: 0.25rem 0.75rem !important;
  font-size: 0.8rem !important;
  height: 24px !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
}