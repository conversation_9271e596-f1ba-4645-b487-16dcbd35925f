import "bootstrap-icons/font/bootstrap-icons.css";
import React, { useEffect, useState } from 'react';
import { Accordion, Card, useAccordionButton } from 'react-bootstrap';
import Sidebar from '../../components/Sidebar';
import apiClient from "../../core/config/api";
import Header from '../../layouts/Header/components/Header';
import { DashboardMetrics } from '../../types/models/Reports';
import DailyMetricsChart from './components/DailyMetricsChart';
import KPICard from './components/KPICard';
import RequestTypeBreakdownChart from './components/RequestTypeBreakdownChart';
import ServerDistributionChart from './components/ServerDistributionChart';
import ServerIPDistributionChart from './components/ServerIPDistributionChart';
import SourceDistributionChart from './components/SourceDistributionChart';

const CustomToggle = React.forwardRef<HTMLDivElement, { children: React.ReactNode, eventKey: string }>(
  ({ children, eventKey }, ref) => {
    const decoratedOnClick = useAccordionButton(eventKey);

    return (
      <div
        ref={ref}
        className="d-flex justify-content-between align-items-center w-100 px-3 py-2"
        onClick={decoratedOnClick}
        style={{ cursor: 'pointer' }}
      >
        {children}
      </div>
    );
  }
);

const ReportsDashboard: React.FC = () => {
  const [dateRange, setDateRange] = useState<string>('last7days');
  const [loadingStates, setLoadingStates] = useState({
    requestType: true,
    totalCounts: true,
    validCount: true,
    invalidCount: true,
    catchAllCount: true,
    totalEmails: true,
    dailyMetrics: true,
    requestBreakdown: true,
    sourceDistribution: true,
    serverDistribution: true,
    serverIPDistribution: true
  });
  const [dashboardMetrics, setDashboardMetrics] = useState<DashboardMetrics>({
    validation_status: { valid: 0, catch_all: 0, invalid: 0, total: 0 },
    request_type: { finder: 0, verifier: 0, total: 0 },
    total_counts: { other_source_requests: 0, total_users: 0 }
  });
  const [activeAccordionKey, setActiveAccordionKey] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      // Reset all loading states to true initially
      setLoadingStates({
        requestType: true,
        totalCounts: true,
        validCount: true,
        invalidCount: true,
        catchAllCount: true,
        totalEmails: true,
        dailyMetrics: true,
        requestBreakdown: true,
        sourceDistribution: true,
        serverDistribution: true,
        serverIPDistribution: true
      });

      try {
        // 1. Request type metrics
        const requestTypeResponse = await apiClient.get('/kpi-card/metrics/request-type');
        setDashboardMetrics(prev => ({
          ...prev,
          request_type: requestTypeResponse.data
        }));
        setLoadingStates(prev => ({ ...prev, requestType: false }));

        // 2. Total counts metrics
        const totalCountsResponse = await apiClient.get('/kpi-card/metrics/other-source-status');
        setDashboardMetrics(prev => ({
          ...prev,
          total_counts: totalCountsResponse.data
        }));
        setLoadingStates(prev => ({ ...prev, totalCounts: false }));

        // 3. Total emails
        const totalEmailsResponse = await apiClient.get('/kpi-card/metrics/validation/total-emails');
        setDashboardMetrics(prev => ({
          ...prev,
          validation_status: {
            ...prev.validation_status,
            total: totalEmailsResponse.data.count
          }
        }));
        setLoadingStates(prev => ({ ...prev, totalEmails: false }));

        // 4. Valid counts
        const validCountResponse = await apiClient.get('/kpi-card/metrics/validation/valid-counts');
        setDashboardMetrics(prev => ({
          ...prev,
          validation_status: {
            ...prev.validation_status,
            valid: validCountResponse.data.count
          }
        }));
        setLoadingStates(prev => ({ ...prev, validCount: false }));

        // 5. Invalid counts
        const invalidCountResponse = await apiClient.get('/kpi-card/metrics/validation/invalid-counts');
        setDashboardMetrics(prev => ({
          ...prev,
          validation_status: {
            ...prev.validation_status,
            invalid: invalidCountResponse.data.count
          }
        }));
        setLoadingStates(prev => ({ ...prev, invalidCount: false }));

        // 6. Catch-all counts
        const catchAllCountResponse = await apiClient.get('/kpi-card/metrics/validation/catch-all-counts');
        setDashboardMetrics(prev => ({
          ...prev,
          validation_status: {
            ...prev.validation_status,
            catch_all: catchAllCountResponse.data.count
          }
        }));
        setLoadingStates(prev => ({ ...prev, catchAllCount: false }));

      } catch (err) {
        console.error('Error in dashboard data fetch:', err);
        // Set all loading states to false on error
        setLoadingStates(prev => ({
          ...prev,
          requestType: false,
          totalCounts: false,
          validCount: false,
          invalidCount: false,
          catchAllCount: false,
          totalEmails: false
        }));
      }
    };

    fetchDashboardData();
  }, [dateRange]);

  const MetricLoadingSkeleton = () => (
    <div className="metric-loading-skeleton">
      <div className="spinner-border text-primary" role="status">
        <span className="visually-hidden">Loading...</span>
      </div>
    </div>
  );

  return (
    <div className="reports-section">
      <div className="dashboard-container">
        <Header />
        <Sidebar />
        <div className="dashboard-content">
          <div className="card shadow-sm border-0">
            <div className="card-header py-3" style={{ background: "#164966" }}>
              <h5 className="mb-0 text-white">
                <i className="bi bi-graph-up me-2"></i>Reports
              </h5>
            </div>
            <div className="card-body p-3 reports-charts">
              {/* Top Metrics Row */}
              <div className="row g-2 mb-2">
                <div className="col-12 col-md-4">
                  <div className="total-metrics-card">
                    <div className="metric-item">
                      <span>TOTAL EMAILS <i className="bi bi-envelope-check"></i></span>
                      {loadingStates.totalEmails ? (
                        <MetricLoadingSkeleton />
                      ) : (
                        <div className="metric-value">
                          {dashboardMetrics.validation_status.total.toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="col-12 col-md-4">
                  <div className="total-metrics-card">
                    <div className="metric-item">
                      <span>TOTAL REQUESTS <i className="bi bi-list-check"></i></span>
                      {loadingStates.requestType ? (
                        <MetricLoadingSkeleton />
                      ) : (
                        <div className="metric-value">
                          {dashboardMetrics.request_type.total.toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="col-12 col-md-4">
                  <div className="total-metrics-card">
                    <div className="metric-item">
                      <span>REACHSTREAM USERS <i className="bi bi-people-fill"></i></span>
                      {loadingStates.totalCounts ? (
                        <MetricLoadingSkeleton />
                      ) : (
                        <div className="metric-value">
                          {dashboardMetrics.total_counts.total_users.toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* KPI Cards Row */}
              <div className="row g-2 mb-2">
                <div className="col-6 col-md-4 col-lg-2">
                  <KPICard
                    title={<>Valid <i className="bi bi-check-circle-fill"></i></>}
                    value={dashboardMetrics.validation_status.valid.toLocaleString()}
                    className="success"
                    isLoading={loadingStates.validCount}
                  />
                </div>
                <div className="col-6 col-md-4 col-lg-2">
                  <KPICard
                    title={<>Invalid <i className="bi bi-x-circle-fill"></i></>}
                    value={dashboardMetrics.validation_status.invalid.toLocaleString()}
                    className="danger"
                    isLoading={loadingStates.invalidCount}
                  />
                </div>
                <div className="col-6 col-md-4 col-lg-2">
                  <KPICard
                    title={<>Catch-All <i className="bi bi-exclamation-triangle-fill"></i></>}
                    value={dashboardMetrics.validation_status.catch_all.toLocaleString()}
                    className="warning"
                    isLoading={loadingStates.catchAllCount}
                  />
                </div>
                <div className="col-6 col-md-4 col-lg-2">
                  <KPICard
                    title={<>Verifier <i className="bi bi-shield-check"></i></>}
                    value={dashboardMetrics.request_type.verifier.toLocaleString()}
                    className="primary"
                    isLoading={loadingStates.requestType}
                  />
                </div>
                <div className="col-6 col-md-4 col-lg-2">
                  <KPICard
                    title={<>Finder <i className="bi bi-search"></i></>}
                    value={dashboardMetrics.request_type.finder.toLocaleString()}
                    className="info"
                    isLoading={loadingStates.requestType}
                  />
                </div>
              </div>

              {/* Charts Accordion */}
              <Accordion
                activeKey={activeAccordionKey}
                onSelect={(key) => setActiveAccordionKey(key as string)}
                className="reports-accordion mt-4"
              >
                {/* Daily Metrics */}
                <div className="border-0 shadow-sm mb-2">
                  <div className="p-0 bg-background">
                    <Accordion.Button as={CustomToggle} eventKey="0">
                      <div className="d-flex justify-content-between align-items-center w-100 px-3">
                        <div>
                          <i className="bi bi-calendar-week me-2"></i> Daily Metrics
                        </div>
                        <i className={`bi bi-chevron-${activeAccordionKey === "0" ? "up" : "down"} accordion-arrow`}></i>
                      </div>
                    </Accordion.Button>
                  </div>
                  <Accordion.Collapse eventKey="0">
                    <Card.Body className="p-0">
                      <div className="chart-card">
                        <DailyMetricsChart
                          isActive={activeAccordionKey === "0"}
                        // isLoading={loadingStates.dailyMetrics}
                        />
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </div>

                {/* Request Type Breakdown */}
                <div className="border-0 shadow-sm mb-2">
                  <div className="p-0 bg-background">
                    <Accordion.Button as={CustomToggle} eventKey="2">
                      <div className="d-flex justify-content-between align-items-center w-100 px-3">
                        <div>
                          <i className="bi bi-diagram-3 me-2"></i> Request Type Breakdown
                        </div>
                        <i className={`bi bi-chevron-${activeAccordionKey === "2" ? "up" : "down"} accordion-arrow`}></i>
                      </div>
                    </Accordion.Button>
                  </div>
                  <Accordion.Collapse eventKey="2">
                    <Card.Body className="p-0">
                      <div className="chart-card">
                        <RequestTypeBreakdownChart
                          isActive={activeAccordionKey === "2"}
                        // isLoading={loadingStates.requestBreakdown}
                        />
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </div>

                {/* Source Distribution */}
                <div className="border-0 shadow-sm mb-2">
                  <div className="p-0 bg-background">
                    <Accordion.Button as={CustomToggle} eventKey="3">
                      <div className="d-flex justify-content-between align-items-center w-100 px-3">
                        <div>
                          <i className="bi bi-pie-chart me-2"></i> Source Distribution
                        </div>
                        <i className={`bi bi-chevron-${activeAccordionKey === "3" ? "up" : "down"} accordion-arrow`}></i>
                      </div>
                    </Accordion.Button>
                  </div>
                  <Accordion.Collapse eventKey="3">
                    <Card.Body className="p-0">
                      <div className="chart-card">
                        <SourceDistributionChart
                          isActive={activeAccordionKey === "3"}
                        // isLoading={loadingStates.sourceDistribution}
                        />
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </div>

                {/* Server Distribution */}
                <div className="border-0 shadow-sm mb-2">
                  <div className="p-0 bg-background">
                    <Accordion.Button as={CustomToggle} eventKey="4">
                      <div className="d-flex justify-content-between align-items-center w-100 px-3">
                        <div>
                          <i className="bi bi-server me-2"></i> Server Distribution
                        </div>
                        <i className={`bi bi-chevron-${activeAccordionKey === "4" ? "up" : "down"} accordion-arrow`}></i>
                      </div>
                    </Accordion.Button>
                  </div>
                  <Accordion.Collapse eventKey="4">
                    <Card.Body className="p-0">
                      <div className="chart-card">
                        <ServerDistributionChart
                          isActive={activeAccordionKey === "4"}
                        // isLoading={loadingStates.serverDistribution}
                        />
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </div>

                {/* Server IP Distribution */}
                <div className="border-0 shadow-sm mb-2">
                  <div className="p-0 bg-background">
                    <Accordion.Button as={CustomToggle} eventKey="5">
                      <div className="d-flex justify-content-between align-items-center w-100 px-3">
                        <div>
                          <i className="bi bi-hdd-network me-2"></i> Server IP Distribution
                        </div>
                        <i className={`bi bi-chevron-${activeAccordionKey === "5" ? "up" : "down"} accordion-arrow`}></i>
                      </div>
                    </Accordion.Button>
                  </div>
                  <Accordion.Collapse eventKey="5">
                    <Card.Body className="p-0">
                      <div className="chart-card">
                        <ServerIPDistributionChart
                          isActive={activeAccordionKey === "5"}
                        // isLoading={loadingStates.serverIPDistribution}
                        />
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </div>
              </Accordion>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsDashboard;