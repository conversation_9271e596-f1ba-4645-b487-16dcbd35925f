/* Custom dropdown menu styling */
.custom-dropdown-menu,
.dropdown-portal,
.dropdown-menu {
  padding: 0.25rem 0;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  z-index: 9999 !important;
  overflow: visible !important; /* Prevent dropdown from causing scrollbars */
}

/* Prevent body scrollbars when dropdown is open */
body:has(.dropdown-portal:not(:empty)),
body:has(.custom-dropdown-menu:not(:empty)),
body:has(.dropdown-menu.show) {
  overflow: auto !important;
}

/* Force all dropdown menus to use our styling */
.dropdown-menu,
.dropdown-portal,
.custom-dropdown-menu {
  background-color: white !important;
}

/* Dropdown items styling */
.dropdown-item,
.custom-dropdown-item {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  text-align: left !important;
  background-color: transparent !important;
  border: none !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: background-color 0.15s ease-in-out !important;
  border-radius: 0 !important;
  height: 32px !important;
}

/* Custom hover effect for the dropdown items */
.dropdown-item:hover,
.dropdown-item:focus,
.custom-dropdown-item:hover,
.custom-dropdown-item:focus,
.dropdown-menu .dropdown-item:hover,
.dropdown-menu .dropdown-item:focus,
.dropdown-portal .dropdown-item:hover,
.dropdown-portal .dropdown-item:focus {
  background-color: #e0e0e0 !important; /* Darker grey background for better visibility */
  color: #333333 !important; /* Dark grey text */
  text-decoration: none !important;
}

/* Override Bootstrap's default blue hover */
.dropdown-menu .dropdown-item:active,
.dropdown-portal .dropdown-item:active,
.custom-dropdown-menu .dropdown-item:active,
.dropdown-portal .custom-dropdown-item:active,
.custom-dropdown-menu .custom-dropdown-item:active {
  background-color: #cccccc !important; /* Even darker grey for active state */
  color: #333333 !important;
}

/* Custom styles for the Actions button */
.actions-btn-custom,
.action-col .btn-outline-primary,
.action-col .btn-primary,
.action-col button.dropdown-toggle,
.action-col .dropdown-toggle,
.custom-dropdown-container .btn-outline-primary,
.custom-dropdown-container .btn-primary,
.custom-dropdown-container button.dropdown-toggle,
.custom-dropdown-container .dropdown-toggle {
  padding: 0.2rem 0.5rem !important;
  font-size: 0.75rem !important;
  height: 24px !important;
  min-width: 70px !important;
  max-width: 80px !important;
  border-radius: 4px !important;
  margin: 0 auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;

  /* White background with grey text */
  background-color: white !important;
  border-color: #dee2e6 !important;
  color: #6c757d !important;
}

/* Hover state for buttons */
.actions-btn-custom:hover,
.action-col .btn-outline-primary:hover,
.action-col .btn-primary:hover,
.action-col button.dropdown-toggle:hover,
.action-col .dropdown-toggle:hover,
.custom-dropdown-container .btn-outline-primary:hover,
.custom-dropdown-container .btn-primary:hover,
.custom-dropdown-container button.dropdown-toggle:hover,
.custom-dropdown-container .dropdown-toggle:hover {
  background-color: #e0e0e0 !important; /* Light grey background on hover */
  border-color: #ced4da !important;
  color: #5a6268 !important; /* Darker grey text on hover */
}

/* Add styles for custom dropdown container in action column */
.custom-dropdown-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Action icons styling */
.dropdown-item i,
.custom-dropdown-item i {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

/* Style for dropdown caret */
.dropdown-toggle .fa-caret-down,
.dropdown-toggle i,
.dropdown-toggle svg {
  color: #6c757d !important; /* Grey color for the caret */
  margin-left: 4px;
}

/* Style for dropdown caret on hover */
.dropdown-toggle:hover .fa-caret-down,
.dropdown-toggle:hover i,
.dropdown-toggle:hover svg {
  color: #5a6268 !important; /* Darker grey on hover */
}

/* Override Bootstrap's default blue background for dropdown items */
.dropdown-item,
.dropdown-menu .dropdown-item,
.dropdown-portal .dropdown-item,
.custom-dropdown-menu .dropdown-item,
.custom-dropdown-item {
  background-color: transparent !important;
}

/* Override Bootstrap's default blue background for dropdown items - with higher specificity */
body .dropdown-item:hover,
body .dropdown-menu .dropdown-item:hover,
body .dropdown-portal .dropdown-item:hover,
body .custom-dropdown-menu .dropdown-item:hover,
body .custom-dropdown-item:hover {
  background-color: #e0e0e0 !important;
  color: #333333 !important;
}

/* Target only action dropdown buttons in server components */
.dashboard-container .actions-btn-custom,
.dashboard-content .actions-btn-custom,
.action-col .btn-outline-primary,
.action-col .btn-primary,
.action-col button.dropdown-toggle,
.action-col .dropdown-toggle,
.action-col .btn-secondary.dropdown-toggle,
.action-col .btn-outline-secondary.dropdown-toggle,
.custom-dropdown-container .btn-outline-primary,
.custom-dropdown-container .btn-primary,
.custom-dropdown-container button.dropdown-toggle,
.custom-dropdown-container .dropdown-toggle,
.custom-dropdown-container .btn-secondary.dropdown-toggle,
.custom-dropdown-container .btn-outline-secondary.dropdown-toggle {
  background-color: white !important; /* White background */
  border-color: #dee2e6 !important;
  color: #6c757d !important; /* Grey text */
}

/* Hover state for specific buttons */
.dashboard-container .actions-btn-custom:hover,
.dashboard-content .actions-btn-custom:hover,
.action-col .btn-outline-primary:hover,
.action-col .btn-primary:hover,
.action-col button.dropdown-toggle:hover,
.action-col .dropdown-toggle:hover,
.action-col .btn-secondary.dropdown-toggle:hover,
.action-col .btn-outline-secondary.dropdown-toggle:hover,
.custom-dropdown-container .btn-outline-primary:hover,
.custom-dropdown-container .btn-primary:hover,
.custom-dropdown-container button.dropdown-toggle:hover,
.custom-dropdown-container .dropdown-toggle:hover,
.custom-dropdown-container .btn-secondary.dropdown-toggle:hover,
.custom-dropdown-container .btn-outline-secondary.dropdown-toggle:hover {
  background-color: #e0e0e0 !important; /* Light grey background on hover */
  border-color: #ced4da !important;
  color: #5a6268 !important; /* Darker grey text on hover */
}

/* Common dropdown menu styles for components */
.dropdown-portal-menu {
  position: absolute;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1050;
  padding: 0.25rem 0;
  text-align: left;
  min-width: 120px;
  max-width: 150px;
}
