import React, { useState, useEffect } from "react";
import apiClient from "../../../core/config/api";

interface AddRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSuccess: (newRole: any) => void;
}

const AddRoleModal: React.FC<AddRoleModalProps> = ({
  isOpen,
  onClose,
  onAddSuccess,
}) => {
  const [name, setName] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  // Clear all messages and states when modal closes or opens
  useEffect(() => {
    if (!isOpen) {
      setName("");
      setIsActive(true);
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.post('/user-roles/', {
        name,
        is_active: isActive
      });

      setSuccessMessage("Role added successfully!");
      onAddSuccess(response.data);

      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      if (err.response?.data?.detail) {
        const apiErrors = err.response.data.detail;

        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              newErrors[field] = error.msg;
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to add role");
        }
      } else {
        setGeneralError(err.message || "Failed to add role. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Role</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="form-group mb-3">
            <label className="form-label">Role Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter role name"
              className={`form-control ${errors.name ? "is-invalid" : ""}`}
              required
            />
            {errors.name && (
              <div className="invalid-feedback">{errors.name}</div>
            )}
          </div>

          <div className="form-group mb-3">
            <label className="form-label">Status</label>
            <select
              className="form-select"
              value={isActive ? "active" : "inactive"}
              onChange={(e) => setIsActive(e.target.value === "active")}
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading || !name.trim()}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Adding...
                </>
              ) : "Add Role"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddRoleModal;