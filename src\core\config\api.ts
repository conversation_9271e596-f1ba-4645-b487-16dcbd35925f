import axios from 'axios';
import { logout, setCredentials } from '../../features/auth/store/authSlice';
import { store } from '../../app/store';

export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
  ENDPOINTS: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH_TOKEN: '/auth/token/refresh',
  },
};

// Configure axios defaults
const apiClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Request interceptor for auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    const cleanedToken = token.replace(/^"(.*)"$/, '$1');
    config.headers.Authorization = `Bearer ${cleanedToken}`;
  }
  return config;
});

// Request interceptor for CSRF token
apiClient.interceptors.request.use((config) => {
  const csrfToken = document.cookie
    .split('; ')
    .find((row) => row.startsWith('XSRF-TOKEN='))
    ?.split('=')[1];
  if (csrfToken) {
    config.headers['X-XSRF-TOKEN'] = csrfToken;
  }
  return config;
});

// Response interceptor for token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    // If error is 401 and we haven't already retried
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Attempt to refresh the token using your refresh token endpoint
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) throw new Error('No refresh token available');
        
        const refreshResponse = await axios.post(
          API_CONFIG.ENDPOINTS.REFRESH_TOKEN,
          {}, // Empty body if you're using cookies
          {
            headers: {
              'Refresh-Token': refreshToken,
            },
            withCredentials: true,
          }
        );
        
        if (refreshResponse.status === 200) {
          const { access_token, refresh_token } = refreshResponse.data;
          
          // Update Redux store with new tokens
          store.dispatch(setCredentials({
            access_token,
            refresh_token,
            token_type: 'Bearer',
            expires_in: 3600, 
            email: store.getState().auth.user?.email || '',
            first_name: store.getState().auth.user?.first_name || '',
            last_name: store.getState().auth.user?.last_name || '',
            role_id: store.getState().auth.roleId || 0,
          }));
          
          // Update the original request with the new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        console.error('Refresh token failed:', refreshError);
        // Clear tokens and redirect to login
        store.dispatch(logout());
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    }
    
    // For other 401 errors or if retry already happened
    if (error.response?.status === 401) {
      console.error('Session expired. Please log in again.');
      store.dispatch(logout());
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

// Debug interceptors in development
if (import.meta.env.DEV) {
  apiClient.interceptors.request.use((config) => {
    return config;
  });

  apiClient.interceptors.response.use((response) => {
    return response;
  });
}

export default apiClient;