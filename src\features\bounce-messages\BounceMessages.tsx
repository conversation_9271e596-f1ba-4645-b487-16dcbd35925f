import { saveAs } from 'file-saver';
import React, { useEffect, useState, useRef } from 'react';
import Pagination from '../../components/Pagination';
import Sidebar from '../../components/Sidebar';
import apiClient from '../../core/config/api';
import Header from '../../layouts/Header/components/Header';
import DeleteConfirmationModal from '../user/components/DeleteConfirmationModal';
import AddBounceMessageModal from './AddBounceMessageModal';
import BounceMessagesModal from './BounceMessagesModal';
import EditBounceMessageModal from './EditBounceMessageModal';
import { BounceMessage } from '../../types/models/BounceMessage';
import DropdownMenuPortal from '../email/components/DropdownMenuPortal';
import { DeleteIcon, EditIcon } from "../../components/ActionIcons";

const BounceMessages: React.FC = () => {
  const [messages, setMessages] = useState<BounceMessage[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedBounceMessage, setSelectedBounceMessage] = useState<BounceMessage | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [messageToDelete, setMessageToDelete] = useState<BounceMessage | null>(null);
  const [activeCount, setActiveCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});
  // Calculate the starting number for the current page
  const startingNumber = (currentPage - 1) * dataPerPage;

  const fetchActiveCount = async () => {
    try {
      const response = await apiClient.get("/bounce-messages/count/active");
      setActiveCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch active bounce messages count:", error);
    }
  };

  const fetchBounceMessages = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get("/bounce-messages", {
        params: {
          page: currentPage,
          page_size: dataPerPage,
          message_contains: searchTerm,
        },
      });

      setMessages(response.data.data || []);
      setTotalPages(response.data.total_pages || Math.ceil(response.data.total / dataPerPage));
    } catch (error) {
      console.error("Failed to fetch bounce messages:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBounceMessages();
    fetchActiveCount();
  }, [currentPage, dataPerPage, searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setDataPerPage(newPerPage);
    setCurrentPage(1);
  };

  const downloadTemplate = () => {
    const csvContent = "category,message,status\n";
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, 'bounce_messages_template.csv');
  };

  const handleDeleteMessage = async () => {
    if (!messageToDelete) return;

    try {
      await apiClient.delete(`/bounce-messages/${messageToDelete.id}`);
      fetchBounceMessages();
      fetchActiveCount();
      setIsDeleteModalOpen(false);
      // Add a small delay before clearing to ensure modal transitions properly
      setTimeout(() => setMessageToDelete(null), 300);
    } catch (error) {
      console.error("Failed to delete bounce message:", error);
    }
  };

  const handleUpdateBounceMessage = (updatedMessage: BounceMessage) => {
    setMessages(prevMessages =>
      prevMessages.map(msg =>
        msg.id === updatedMessage.id ? updatedMessage : msg
      )
    );
    fetchActiveCount();
  };

  const handleAddBounceMessage = (newMessage: BounceMessage) => {
    setMessages(prev => [...prev, newMessage]);
    fetchActiveCount();
    setIsAddModalOpen(false);
  };

  const handleUploadSuccess = () => {
    fetchBounceMessages();
    fetchActiveCount();
  };

  const getStatusColor = (status: boolean): string => {
    return status ? 'badge bg-success' : 'badge bg-danger';
  };

  // We don't need the click outside handler anymore since DropdownMenuPortal handles this
  useEffect(() => {
    // No cleanup needed
    return () => {
      // No cleanup needed
    };
  }, [openActionMenuId]);

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <h5 className="mb-0 text-white">
              <i className="fas fa-exclamation-triangle me-2"></i>Bounce Messages{activeCount > 0 && <span>({activeCount})</span>}
            </h5>
          </div>
          <div className="card-body p-3">
            <div className="filters-container">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search by message..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                  style={{
                    paddingRight: '30px',
                    borderRadius: '8px'
                  }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchTerm('')}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>
              <div className="filter-group right-filters">
                <button
                  onClick={downloadTemplate}
                  className="download-template-button me-2"
                >
                  <i className="fas fa-download me-2"></i>Download Template
                </button>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="submit-button me-2"
                >
                  <i className="fas fa-upload me-2"></i>Bulk Upload
                </button>
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="submit-button"
                >
                  <i className="fas fa-plus me-2"></i>Add Bounce Message
                </button>
              </div>
            </div>

            <div className="table-responsive">
              <table className="uploads-table">
                <thead>
                  <tr>
                    <th className="id-col">ID</th>
                    <th className="message-col">Message</th>
                    <th className="category-col">Category</th>
                    <th className="status-col">Status</th>
                    <th className="date-col">Updated At</th>
                    <th className="action-col text-center">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="text-center py-4">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : messages.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="text-center py-4">
                        {searchTerm
                          ? `No bounce messages found matching "${searchTerm}"`
                          : 'No bounce messages found'}
                      </td>
                    </tr>
                  ) : (
                    messages.map((message, i) => (
                      <tr key={message.id}>
                        <td className="id-col">#{startingNumber + i + 1}</td>
                        <td className="message-cell">
                          <div className="message-content text-truncate">
                            {message.message}
                          </div>
                        </td>
                        <td>{message.category}</td>
                        <td>
                          <span className={getStatusColor(message.status)}>
                            {message.status ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td>{new Date(message.updated_at).toLocaleString()}</td>
                        <td className="action-col text-center">
                          <div className="dropdown" style={{ position: 'relative' }}>
                            <button
                              ref={(el) => { buttonRefs.current[message.id] = el }}
                              type="button"
                              className="btn btn-outline-secondary btn-sm dropdown-toggle actions-btn-custom"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenActionMenuId(openActionMenuId === message.id ? null : message.id);
                              }}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                                padding: '0.2rem 0.5rem',
                                fontSize: '0.75rem',
                                height: '24px',
                                width: '80px',
                                cursor: 'pointer',
                                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                              }}
                            >
                              <span>Actions</span>
                              <i className="fas fa-caret-down ms-1"></i>
                            </button>

                            <DropdownMenuPortal
                              isOpen={openActionMenuId === message.id}
                              buttonElement={buttonRefs.current[message.id] || null}
                              onClose={() => setOpenActionMenuId(null)}
                              className="dropdown-portal-menu"
                              menuStyle={{
                                width: '120px',
                                transform: 'none',
                                left: 'auto',
                                marginTop: '2px',
                              }}
                            >
                              {/* Edit Option */}
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  setSelectedBounceMessage(message);
                                  setIsEditModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px'
                                }}
                              >
                                <EditIcon width="16" height="16" fill="#0082A3" />
                                <span className="ms-2">Edit</span>
                              </button>

                              {/* Delete Option */}
                              <button
                                type="button"
                                className="dropdown-item"
                                onClick={() => {
                                  setMessageToDelete(message);
                                  setIsDeleteModalOpen(true);
                                  setOpenActionMenuId(null);
                                }}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  width: '100%',
                                  textAlign: 'left',
                                  backgroundColor: 'transparent',
                                  border: 'none',
                                  padding: '0.25rem 0.75rem',
                                  fontSize: '0.8rem',
                                  cursor: 'pointer',
                                  borderRadius: '0',
                                  height: '24px'
                                }}
                              >
                                <DeleteIcon width="16" height="16" fill="#D9363E" />
                                <span className="ms-2">Delete</span>
                              </button>
                            </DropdownMenuPortal>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={handlePerPageChange}
              perPage={dataPerPage}
            />
          </div>
        </div>

        <BounceMessagesModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onUploadSuccess={handleUploadSuccess}
        />

        <EditBounceMessageModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onEditSuccess={handleUpdateBounceMessage}
          bounceMessage={selectedBounceMessage}
        />

        <AddBounceMessageModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onAddSuccess={handleAddBounceMessage}
        />

        <DeleteConfirmationModal
          key={messageToDelete?.id || 'empty'}
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setMessageToDelete(null);
          }}
          onConfirm={handleDeleteMessage}
          userName={messageToDelete?.message || ''}
          entityType="Bounce Message"
        />
      </div>
    </div>
  );
};

export default BounceMessages;