import React from 'react';
import './LogoutConfirmationModal.css';

interface LogoutConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
}

const LogoutConfirmationModal: React.FC<LogoutConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  const [isLoggingOut, setIsLoggingOut] = React.useState(false);
  const [message, setMessage] = React.useState<{text: string; isError: boolean} | null>(null);

  const handleConfirm = async () => {
    setIsLoggingOut(true);
    setMessage(null);
    try {
      await onConfirm();
      setMessage({
        text: 'Logged out successfully!',
        isError: false
      });
      setTimeout(() => onClose(), 1500);
    } catch (error) {
      setMessage({
        text: error instanceof Error ? error.message : 'Failed to log out',
        isError: true
      });
    } finally {
      setIsLoggingOut(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Confirm Logout</h3>
        </div>
        <div className="modal-body">
          {message ? (
            <div className={message.isError ? "error-message" : "success-message"}>
              {message.text}
            </div>
          ) : (
            <p>Are you sure you want to log out?</p>
          )}
        </div>
        <div className="form-actions">
          {!message && (
            <>
              <button 
                type="button" 
                onClick={handleConfirm}
                disabled={isLoggingOut}
                className="logout-confirm-button"
              >
                {isLoggingOut ? 'Logging out...' : 'Confirm'}
              </button>
              <button 
                type="button" 
                onClick={onClose}
                disabled={isLoggingOut}
              >
                Cancel
              </button>
            </>
          )}
          {message && (
            <button 
              type="button" 
              onClick={onClose}
            >
              Close
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LogoutConfirmationModal;