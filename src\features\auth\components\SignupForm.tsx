import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { authService } from "../../../services/authService";
import { RegisterPayload } from "../../../types/models/auth";
import "./SignupForm.css";

const SignUpForm: React.FC = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    password: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    password: "",
    confirmPassword: "",
  });

  const validateForm = () => {
    let isValid = true;
    const newFieldErrors = {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
    };

    if (formData.firstName.trim().length < 2) {
      newFieldErrors.firstName = "First name must be at least 2 characters";
      isValid = false;
    }

    if (formData.lastName && formData.lastName.trim().length < 1) {
      newFieldErrors.lastName = "Last name must be at least 1 character";
      isValid = false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      newFieldErrors.email = "Please enter a valid email address";
      isValid = false;
    }

    if (formData.password.length < 8) {
      newFieldErrors.password = "Password must be at least 8 characters";
      isValid = false;
    }

    if (formData.password !== formData.confirmPassword) {
      newFieldErrors.confirmPassword = "Passwords don't match";
      isValid = false;
    }

    setFieldErrors(newFieldErrors);
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setFieldErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (!validateForm()) return;

    const payload: RegisterPayload = {
      first_name: formData.firstName.trim(),
      last_name: formData.lastName.trim() || null,
      email: formData.email.trim(),
      phone_number: formData.phoneNumber.trim() || null,
      password: formData.password,
      confirm_password: formData.confirmPassword,
    };

    try {
      setIsSubmitting(true);
      await authService.register(payload);
      setSuccessMessage("Registration successful! Please wait for admin approval.");
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        password: "",
        confirmPassword: "",
      });
    } catch (err: any) {
      if (err.response?.data?.detail) {
        err.response.data.detail.forEach((error: any) => {
          if (error.type === "value_error" && error.msg.includes("Passwords do not match")) {
            setFieldErrors((prev) => ({
              ...prev,
              confirmPassword: "Passwords do not match",
            }));
          } else if (error.type === "duplicate_email") {
            setFieldErrors((prev) => ({
              ...prev,
              email: "Email address is already in use",
            }));
          } else {
            setError(error.msg || "Registration failed");
          }
        });
      } else if (err.response?.data?.error === "bad_request") {
        const details = err.response.data.details?.[0];
        if (details?.type === "duplicate_email") {
          setFieldErrors((prev) => ({
            ...prev,
            email: "Email address is already in use",
          }));
        } else {
          setError(err.response.data.message || "Registration failed");
        }
      } else {
        setError(err.message || "Registration failed");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="signup-container">
      <div className="signup-card">
        <header className="signup-header">
          <h1 className="signup-title">Sign Up</h1>
          <p className="signup-subtitle">Get started with your account</p>
        </header>

        <form onSubmit={handleSubmit} className="signup-form">
          {error && <div className="error-message">{error}</div>}
          {successMessage && <div className="success-message">{successMessage}</div>}

          <div className="form-row">
            <div className="input-group">
              <label htmlFor="firstName" className="input-label">
                First Name*
              </label>
              <input
                id="firstName"
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                className={`form-input ${fieldErrors.firstName ? "input-error" : ""}`}
                placeholder="John"
                required
              />
              {fieldErrors.firstName && (
                <span className="error-text">{fieldErrors.firstName}</span>
              )}
            </div>
            <div className="input-group">
              <label htmlFor="lastName" className="input-label">
                Last Name
              </label>
              <input
                id="lastName"
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                className={`form-input ${fieldErrors.lastName ? "input-error" : ""}`}
                placeholder="Doe"
              />
              {fieldErrors.lastName && (
                <span className="error-text">{fieldErrors.lastName}</span>
              )}
            </div>
          </div>

          <div className="input-group">
            <label htmlFor="email" className="input-label">
              Email*
            </label>
            <input
              id="email"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className={`form-input ${fieldErrors.email ? "input-error" : ""}`}
              placeholder="<EMAIL>"
              required
            />
            {fieldErrors.email && (
              <span className="error-text">{fieldErrors.email}</span>
            )}
          </div>

          <div className="input-group phone-number-group" style={{"display":"none"}}>
            <label htmlFor="phoneNumber" className="input-label">
              Phone Number
            </label>
            <input
              id="phoneNumber"
              type="tel"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleChange}
              className={`form-input ${fieldErrors.phoneNumber ? "input-error" : ""}`}
              placeholder="1234567890"
            />
            {fieldErrors.phoneNumber && (
              <span className="error-text">{fieldErrors.phoneNumber}</span>
            )}
          </div>

          <div className="password-row">
            <div className="input-group">
              <label htmlFor="password" className="input-label">
                Password*
              </label>
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`form-input ${fieldErrors.password ? "input-error" : ""}`}
                placeholder="Create a password (min 8 characters)"
                required
              />
              {fieldErrors.password && (
                <span className="error-text">{fieldErrors.password}</span>
              )}
            </div>
            <div className="input-group">
              <label htmlFor="confirmPassword" className="input-label">
                Confirm Password*
              </label>
              <input
                id="confirmPassword"
                type={showPassword ? "text" : "password"}
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className={`form-input ${fieldErrors.confirmPassword ? "input-error" : ""}`}
                placeholder="Confirm your password"
                required
              />
              {fieldErrors.confirmPassword && (
                <span className="error-text">{fieldErrors.confirmPassword}</span>
              )}
            </div>
          </div>

          <div className="password-toggle-group">
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? "Hide Password" : "Show Password"}
            </button>
          </div>

          <button
            type="submit"
            className="signup-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Creating Account..." : "Create Account"}
          </button>

          <div className="signup-link">
            Already have an account?{" "}
            <Link to="/login" className="login-text">
              Sign in
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SignUpForm;