import React, { useState, useEffect } from "react";
import apiClient from "../../../core/config/api";

interface Role {
    id: number;
    name: string;
}

interface UserFormData {
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string | null;
    role_id: number;
    account_status: string;
    password: string;
    confirm_password: string;
    max_upload_limit: number;
}

interface UserDetails {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    role_id: number;
    account_status: string;
    created_at: string;
    phone_number: string | null;
    max_upload_limit: number;
}

interface EditUserModalProps {
    isOpen: boolean;
    onClose: () => void;
    user: UserDetails | null;
    onUpdate: (updatedUser: UserDetails) => void;
}

const accountStatusOptions = [
    "pending",
    "active",
    "inactive"
];

const EditUserModal: React.FC<EditUserModalProps> = ({
    isOpen,
    onClose,
    user,
    onUpdate,
}) => {
    const [formData, setFormData] = useState<UserFormData>({
        first_name: "",
        last_name: "",
        email: "",
        phone_number: null,
        role_id: 2,
        account_status: "pending",
        password: "",
        confirm_password: "",
        max_upload_limit: 100000,
    });
    const [roles, setRoles] = useState<Role[]>([]);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [generalError, setGeneralError] = useState("");
    const [successMessage, setSuccessMessage] = useState("");

    useEffect(() => {
        if (user) {
            setFormData({
                first_name: user.first_name,
                last_name: user.last_name,
                email: user.email,
                phone_number: user.phone_number,
                role_id: user.role_id,
                account_status: user.account_status,
                password: "",
                confirm_password: "",
                max_upload_limit: user.max_upload_limit
            });
        }
    }, [user]);

    useEffect(() => {
        const fetchRoles = async () => {
            try {
                const response = await apiClient.get("/user-roles");
                setRoles(response.data);
            } catch (error) {
                console.error("Failed to fetch roles:", error);
            }
        };

        if (isOpen) {
            fetchRoles();
        }
    }, [isOpen]);

    const handleInputChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value,
        });
        if (errors[name]) {
            setErrors({
                ...errors,
                [name]: "",
            });
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setGeneralError("");
        setErrors({});
        setSuccessMessage("");

        try {
            const payload: any = {};

            // Only include changed fields
            if (formData.first_name !== user?.first_name) payload.first_name = formData.first_name;
            if (formData.last_name !== user?.last_name) payload.last_name = formData.last_name;
            if (formData.email !== user?.email) payload.email = formData.email;
            if (formData.phone_number !== user?.phone_number) payload.phone_number = formData.phone_number;
            if (formData.role_id !== user?.role_id) payload.role_id = formData.role_id;
            if (formData.account_status !== user?.account_status) payload.account_status = formData.account_status;
            if (formData.max_upload_limit !== user?.max_upload_limit) {
                payload.max_upload_limit = parseInt(formData.max_upload_limit.toString(), 10);
            }

            // Password handling
            if (formData.password) {
                if (formData.password !== formData.confirm_password) {
                    throw { response: { data: { detail: [{ type: "value_error", msg: "Passwords do not match" }] } } };
                }
                payload.password = formData.password;
                payload.confirm_password = formData.confirm_password;
            }

            const response = await apiClient.put(`/user/${user?.id}`, payload);
            onUpdate(response.data);
            setSuccessMessage("User updated successfully!");
            setTimeout(() => {
                onClose();
            }, 1500);
        } catch (error: any) {
            if (error.response?.data?.detail) {
                const apiErrors = error.response.data.detail;
                if (Array.isArray(apiErrors)) {
                    const newErrors: Record<string, string> = {};
                    apiErrors.forEach((err: any) => {
                        if (err.loc && err.loc.length > 1) {
                            const field = err.loc[err.loc.length - 1];
                            newErrors[field] = err.msg;
                        } else if (err.msg === "Passwords do not match") {
                            newErrors.confirm_password = "Passwords do not match";
                        } else {
                            setGeneralError(err.msg || "An error occurred");
                        }
                    });
                    setErrors(newErrors);
                } else {
                    setGeneralError(apiErrors.message || "An error occurred");
                }
            } else {
                setGeneralError("An unexpected error occurred");
            }
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen || !user) return null;

    return (
        <div className="modal-overlay">
            <div className="modal-content">
                <div className="modal-header">
                    <h3>Edit User</h3>
                </div>

                <form onSubmit={handleSubmit}>
                    {generalError && <div className="error-message">{generalError}</div>}
                    {successMessage && <div className="success-message">{successMessage}</div>}

                    <div className="row pt-2">
                        <div className="col-md-6">
                            <div className="form-group">
                                <label>First Name</label>
                                <input
                                    type="text"
                                    name="first_name"
                                    value={formData.first_name}
                                    onChange={handleInputChange}
                                    className={errors.first_name ? "input-error" : ""}
                                    required
                                />
                                {errors.first_name && <span className="error-text">{errors.first_name}</span>}
                            </div>
                        </div>
                        <div className="col-md-6">
                            <div className="form-group">
                                <label>Last Name</label>
                                <input
                                    type="text"
                                    name="last_name"
                                    value={formData.last_name}
                                    onChange={handleInputChange}
                                    className={errors.last_name ? "input-error" : ""}
                                />
                                {errors.last_name && <span className="error-text">{errors.last_name}</span>}
                            </div>
                        </div>
                    </div>

                    <div className="row">
                        <div className="col-md-6">
                            <div className="form-group">
                                <label>Email</label>
                                <input
                                    type="email"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    className={errors.email ? "input-error" : ""}
                                    required
                                />
                                {errors.email && <span className="error-text">{errors.email}</span>}
                            </div>
                        </div>
                        <div className="col-md-6">
                            <div className="form-group">
                                <label>Daily Upload Limit</label>
                                <input
                                    type="number"
                                    name="max_upload_limit"
                                    value={formData.max_upload_limit}
                                    onChange={handleInputChange}
                                    className={errors.max_upload_limit ? "input-error" : ""}
                                    min="0"
                                />
                                {errors.max_upload_limit && <span className="error-text">{errors.max_upload_limit}</span>}
                            </div>
                        </div>
                    </div>

                    <div className="row">
                        <div className="col-md-6">
                            <div className="form-group">
                                <label>Role</label>
                                <div className="select-wrapper">
                                    <select
                                        name="role_id"
                                        value={formData.role_id}
                                        onChange={handleInputChange}
                                        className={errors.role_id ? "input-error" : ""}
                                    >
                                        {roles.map((role) => (
                                            <option key={role.id} value={role.id}>
                                                {role.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                {errors.role_id && <span className="error-text">{errors.role_id}</span>}
                            </div>
                        </div>
                        <div className="col-md-6">
                            <div className="form-group">
                                <label>Account Status</label>
                                <div className="select-wrapper">
                                    <select
                                        name="account_status"
                                        value={formData.account_status}
                                        onChange={handleInputChange}
                                        className={errors.account_status ? "input-error" : ""}
                                    >
                                        {accountStatusOptions.map((status) => (
                                            <option key={status} value={status}>
                                                {status}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                {errors.account_status && <span className="error-text">{errors.account_status}</span>}
                            </div>
                        </div>
                    </div>

                    <div className="row">
                        <div className="col-md-6">
                            <div className="form-group">
                                <label>Password</label>
                                <input
                                    type="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    className={errors.password ? "input-error" : ""}
                                    placeholder="Leave blank to keep current"
                                />
                                {errors.password && <span className="error-text">{errors.password}</span>}
                            </div>
                        </div>
                        <div className="col-md-6">
                            <div className="form-group">
                                <label>Confirm Password</label>
                                <input
                                    type="password"
                                    name="confirm_password"
                                    value={formData.confirm_password}
                                    onChange={handleInputChange}
                                    className={errors.confirm_password ? "input-error" : ""}
                                    placeholder="Leave blank to keep current"
                                />
                                {errors.confirm_password && (
                                    <span className="error-text">{errors.confirm_password}</span>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="form-actions">
                        <button type="submit" disabled={loading}>
                            {loading ? "Updating..." : "Update User"}
                        </button>
                        <button type="button" onClick={onClose} disabled={loading}>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditUserModal;