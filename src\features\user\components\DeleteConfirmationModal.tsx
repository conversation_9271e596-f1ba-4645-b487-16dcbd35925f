import React from 'react';
import './DeleteConfirmationModal.css';

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose?: () => void;
  onCancel?: () => void;
  onConfirm: () => Promise<void>;
  userName?: string;
  entityType?: string;
  title?: string;
  message?: string;
  loading?: boolean;
  actionType?: 'delete' | 'activate' | 'inactivate';
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  onCancel,
  onConfirm,
  userName,
  entityType = 'User',
  title,
  message,
  loading: externalLoading,
  actionType = 'delete',
}) => {
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [statusMessage, setStatusMessage] = React.useState<{ text: string; isError: boolean } | null>(null);

  // Get action-specific text
  const getActionText = () => {
    switch (actionType) {
      case 'activate':
        return { verb: 'activate', past: 'activated', gerund: 'Activating' };
      case 'inactivate':
        return { verb: 'inactivate', past: 'inactivated', gerund: 'Inactivating' };
      default:
        return { verb: 'delete', past: 'deleted', gerund: 'Deleting' };
    }
  };

  const actionText = getActionText();
  const defaultTitle = `Confirm ${actionText.verb.charAt(0).toUpperCase() + actionText.verb.slice(1)}`;

  // Reset state when modal opens or when userName changes
  React.useEffect(() => {
    setStatusMessage(null);
    setIsDeleting(false);
  }, [isOpen, userName]);

  const handleCancel = onCancel || onClose || (() => { });

  const handleConfirm = async () => {
    setIsDeleting(true);
    setStatusMessage(null);
    try {
      await onConfirm();
      setStatusMessage({
        text: userName
          ? `${entityType} "${userName}" ${actionText.past} successfully!`
          : `${actionText.past.charAt(0).toUpperCase() + actionText.past.slice(1)} successfully!`,
        isError: false
      });
    } catch (error) {
      setStatusMessage({
        text: error instanceof Error ? error.message : `Failed to ${actionText.verb}`,
        isError: true
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen) return null;

  const isLoading = externalLoading !== undefined ? externalLoading : isDeleting;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>{title || defaultTitle}</h3>
        </div>
        <div className="modal-body">
          {statusMessage ? (
            <div className={statusMessage.isError ? "error-message" : "success-message"}>
              {statusMessage.text}
            </div>
          ) : (
            <p>{message || (userName ? `Are you sure you want to ${actionText.verb} "${userName}"? This action cannot be undone.` : `Are you sure you want to proceed with ${actionText.verb}? This action cannot be undone.`)}</p>
          )}
        </div>
        <div className="form-actions">
          {!statusMessage && (
            <>
              <button
                type="button"
                onClick={handleConfirm}
                disabled={isLoading}
                className="delete-confirm-button"
              >
                {isLoading ? `${actionText.gerund}...` : 'Confirm'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                disabled={isLoading}
              >
                Cancel
              </button>
            </>
          )}
          {statusMessage && (
            <button
              type="button"
              onClick={handleCancel}
            >
              Close
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;