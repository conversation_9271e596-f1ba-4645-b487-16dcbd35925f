// src/features/server-capacity/components/EditServerCapacityModal.tsx
import React, { useState, useEffect } from 'react';
import apiClient from '../../../core/config/api';
import { ServerCapacityType, ServerDropdownOption } from '../../../types/models/ServerCapacityType';

interface EditServerCapacityModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEditSuccess: (updatedCapacity: ServerCapacityType) => void;
  capacity: ServerCapacityType | null;
  servers: ServerDropdownOption[];
}

const EditServerCapacityModal: React.FC<EditServerCapacityModalProps> = ({
  isOpen,
  onClose,
  onEditSuccess,
  capacity,
  servers,
}) => {
  const [formData, setFormData] = useState({
    server_id: 0,
    total_ips: 0,
    active_ips: 0,
    base_capacity: 0,
    current_capacity: 0,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    if (capacity) {
      setFormData({
        server_id: capacity.server_id,
        total_ips: capacity.total_ips,
        active_ips: capacity.active_ips,
        base_capacity: capacity.base_capacity,
        current_capacity: capacity.current_capacity,
      });
    }
  }, [capacity]);

  useEffect(() => {
    if (!isOpen) {
      setFormData({
        server_id: 0,
        total_ips: 0,
        active_ips: 0,
        base_capacity: 0,
        current_capacity: 0,
      });
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
    }
  }, [isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === "server_id" ? Number(value) : Number(value) || 0,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!capacity) return;

    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");

    try {
      const response = await apiClient.put(`/server-capacities/${capacity.server_id}`, formData);
      setSuccessMessage("Server capacity updated successfully!");

      // Call onEditSuccess with the updated capacity from server response
      onEditSuccess(response.data);

      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err: any) {
      if (err.response?.data?.detail) {
        const apiErrors = err.response.data.detail;

        if (Array.isArray(apiErrors)) {
          const newErrors: Record<string, string> = {};
          apiErrors.forEach((error: any) => {
            if (error.loc && error.loc.length > 1) {
              const field = error.loc[error.loc.length - 1];
              newErrors[field] = error.msg;
            } else {
              setGeneralError(error.msg || "An error occurred");
            }
          });
          setErrors(newErrors);
        } else {
          setGeneralError(apiErrors.message || "Failed to update server capacity");
        }
      } else {
        setGeneralError(err.message || "Failed to update server capacity. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !capacity) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Edit Server Capacity</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="row mb-3">
            <div className="col-md-12">
              <div className="form-group">
                <label className="form-label">Server</label>
                <select
                  name="server_id"
                  value={formData.server_id}
                  onChange={handleInputChange}
                  className={`form-select ${errors.server_id ? "is-invalid" : ""}`}
                  required
                  disabled
                >
                  <option value="0">Select a Server</option>
                  {servers.map((server) => (
                    <option key={server.id} value={server.id}>
                      {server.server_name}
                    </option>
                  ))}
                </select>
                {errors.server_id && (
                  <div className="invalid-feedback">{errors.server_id}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Total IPs</label>
                <input
                  type="number"
                  name="total_ips"
                  value={formData.total_ips}
                  onChange={handleInputChange}
                  className={`form-control ${errors.total_ips ? "is-invalid" : ""}`}
                  required
                  min="0"
                />
                {errors.total_ips && (
                  <div className="invalid-feedback">{errors.total_ips}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Active IPs</label>
                <input
                  type="number"
                  name="active_ips"
                  value={formData.active_ips}
                  onChange={handleInputChange}
                  className={`form-control ${errors.active_ips ? "is-invalid" : ""}`}
                  required
                  min="0"
                />
                {errors.active_ips && (
                  <div className="invalid-feedback">{errors.active_ips}</div>
                )}
              </div>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Base Capacity (requests/min)</label>
                <input
                  type="number"
                  name="base_capacity"
                  value={formData.base_capacity}
                  onChange={handleInputChange}
                  className={`form-control ${errors.base_capacity ? "is-invalid" : ""}`}
                  required
                  min="0"
                />
                {errors.base_capacity && (
                  <div className="invalid-feedback">{errors.base_capacity}</div>
                )}
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Current Capacity</label>
                <input
                  type="number"
                  name="current_capacity"
                  value={formData.current_capacity}
                  onChange={handleInputChange}
                  className={`form-control ${errors.current_capacity ? "is-invalid" : ""}`}
                  required
                  min="0"
                />
                {errors.current_capacity && (
                  <div className="invalid-feedback">{errors.current_capacity}</div>
                )}
              </div>
            </div>
          </div>

          <div className="form-actions d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Updating...
                </>
              ) : "Update Capacity"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditServerCapacityModal;