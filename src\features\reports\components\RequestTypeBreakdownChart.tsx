import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Too<PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Button, ButtonGroup, Table, Spinner } from 'react-bootstrap';
import apiClient from '../../../core/config/api';

interface RequestTypeItem {
  type: string;
  count: number;
}

interface RequestTypeDistribution {
  requestType: string;
  count: number;
  name?: string;
  value?: number;
  percentage?: number;
  fill?: string;
}

interface RequestTypeBreakdownChartProps {
  isActive: boolean;
}

const COLORS = ['#0082A3', '#28a745', '#ffc107', '#dc3545', '#17a2b8'];

const RequestTypeBreakdownChart: React.FC<RequestTypeBreakdownChartProps> = ({ isActive }) => {
  const [breakdown, setBreakdown] = useState<RequestTypeDistribution[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
  const hasLoadedRef = useRef(false);

  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();

    const fetchData = async () => {
      try {
        if (!isActive) {
          return;
        }
        setLoading(true);
        setError(null);

        const response = await apiClient.get('/request-type/distribution', {
          signal: controller.signal
        });

        if (!isMounted) return;

        let breakdownData = [];

        if (response.data?.types) {
          breakdownData = response.data.types;
        } else if (Array.isArray(response.data)) {
          breakdownData = response.data;
        } else if (response.data?.items) {
          breakdownData = response.data.items;
        } else {
          throw new Error('Unexpected data format from server');
        }

        const transformedData: RequestTypeDistribution[] = breakdownData.map((item: RequestTypeItem) => ({
          requestType: item.type,
          count: item.count
        }));

        if (isMounted) {
          setBreakdown(transformedData);
          setLoading(false);
          hasLoadedRef.current = true;
        }
      } catch (err: any) {
        if (isMounted && err.name !== 'AbortError') {
          setError('Failed to load request type breakdown data');
          setLoading(false);
          hasLoadedRef.current = true;
        }
      }
    };

    if (!hasLoadedRef.current) {
      fetchData();
    }

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [isActive]);

  const totalRequests = breakdown.reduce((sum, item) => sum + item.count, 0);

  const enhancedData = breakdown.map((item, index) => ({
    ...item,
    name: item.requestType,
    value: item.count,
    percentage: totalRequests > 0 ? Math.round((item.count / totalRequests) * 100) : 0,
    fill: COLORS[index % COLORS.length]
  }));

  const handleDownload = () => {
    const headers = ['Request Type', 'Count', 'Percentage'];
    const csvRows = [
      headers.join(','),
      ...enhancedData.map(item => `"${item.requestType}",${item.count},${item.percentage}%`)
    ];

    csvRows.push(`Total,${totalRequests},100%`);

    const csvContent = csvRows.join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'request_type_distribution.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderTableView = () => (
    <div className="table-responsive mt-3">
      <Table striped bordered hover className="reports-table">
        <thead className="table-header">
          <tr>
            <th>Request Type</th>
            <th>Count</th>
            <th>Percentage</th>
          </tr>
        </thead>
        <tbody>
          {enhancedData.map((item, index) => (
            <tr key={index}>
              <td>
                <span
                  className="color-indicator"
                  style={{
                    display: 'inline-block',
                    width: '12px',
                    height: '12px',
                    backgroundColor: COLORS[index % COLORS.length],
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}
                ></span>
                {item.requestType}
              </td>
              <td>{item.count.toLocaleString()}</td>
              <td className="fw-bold">{item.percentage}%</td>
            </tr>
          ))}
          <tr className="table-active fw-bold">
            <td>Total</td>
            <td>{totalRequests.toLocaleString()}</td>
            <td>100%</td>
          </tr>
        </tbody>
      </Table>
    </div>
  );

  const renderChartView = () => (
    <div className="chart-container bg-white shadow-sm p-3 rounded">
      <ResponsiveContainer width="100%" height={350}>
        <PieChart margin={{ top: 20, right: 30, left: 20, bottom: 25 }}>
          <Pie
            data={enhancedData}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={110}
            paddingAngle={0}
            dataKey="value"
            nameKey="requestType"
            label={({ value, percentage }) => `${value.toLocaleString()} (${percentage}%)`}
            labelLine={false}
          >
            {enhancedData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [value.toLocaleString(), 'Count']}
            contentStyle={{
              backgroundColor: 'var(--color-background)',
              borderColor: 'var(--color-border)',
              borderRadius: '4px',
              fontSize: '0.8rem'
            }}
            itemStyle={{ color: 'var(--color-text)' }}
          />
          <Legend
            wrapperStyle={{
              fontSize: '0.8rem',
              paddingTop: '10px',
              color: 'var(--color-text)'
            }}
            formatter={(value) => <span style={{ marginRight: '30px' }}>{value}</span>}
            iconSize={10}
          />
        </PieChart>
      </ResponsiveContainer>
      {renderSummaryLabels()}
    </div>
  );

  const renderSummaryLabels = () => (
    <div className="p-3 bg-light rounded">
      {enhancedData.map((item, index) => (
        <div key={index} className="d-flex align-items-center mb-2">
          <span
            className="color-indicator"
            style={{
              display: 'inline-block',
              width: '12px',
              height: '12px',
              backgroundColor: COLORS[index % COLORS.length],
              marginRight: '8px',
              borderRadius: '2px'
            }}
          ></span>
          <span className="small">
            {item.requestType}: {item.count.toLocaleString()} ({item.percentage}%)
          </span>
        </div>
      ))}
      <div className="d-flex align-items-center mb-2 fw-bold">
        <span
          className="color-indicator"
          style={{
            display: 'inline-block',
            width: '12px',
            height: '12px',
            backgroundColor: 'var(--color-text)',
            marginRight: '8px',
            borderRadius: '2px'
          }}
        ></span>
        <span className="small">
          Total: {totalRequests.toLocaleString()} (100%)
        </span>
      </div>
    </div>
  );

  if (loading) return (
    <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
      <Spinner animation="border" variant="primary" />
    </div>
  );

  if (error) return (
    <div className="alert alert-danger my-3">{error}</div>
  );

  return (
    <div className="bg-transparent">
      <div className="d-flex flex-wrap justify-content-between align-items-center gap-3 mb-3">
        <div className="d-flex flex-wrap gap-2">
          <ButtonGroup>
            <Button
              variant={viewMode === 'chart' ? 'primary' : 'outline-primary'}
              onClick={() => setViewMode('chart')}
              size="sm"
            >
              <i className="bi bi-pie-chart me-1"></i> Chart
            </Button>
            <Button
              variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
              onClick={() => setViewMode('table')}
              size="sm"
            >
              <i className="bi bi-table me-1"></i> Table
            </Button>
          </ButtonGroup>
        </div>

        <Button
          variant="outline-primary"
          onClick={handleDownload}
          size="sm"
          disabled={breakdown.length === 0}
          style={{ borderColor: 'var(--color-border)', color: 'var(--color-text)' }}
        >
          <i className="bi bi-download me-1"></i> Export
        </Button>
      </div>

      {viewMode === 'chart' ? renderChartView() : renderTableView()}

      {breakdown.length > 0 && (
        <div className="mt-3 text-end text-muted small">
          Showing {breakdown.length} request types
        </div>
      )}
    </div>
  );
};

export default RequestTypeBreakdownChart;