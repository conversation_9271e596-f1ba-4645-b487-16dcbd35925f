.uploads-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}

.uploads-table th,
.uploads-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.uploads-table th {
  background-color: var(--color-table-th);
  color: var(--color-white);
}

.uploads-table tr:hover {
  background-color: rgba(0, 130, 163, 0.1);
}

.id-col {
  width: 5%;
  white-space: nowrap;
}

.pattern-col {
  width: 15%;
  white-space: nowrap;
}

.comments-col {
  width: 39%;
  white-space: nowrap;
}

.status-col {
  width: 8%;
  white-space: nowrap;
}

.date-col {
  width: 13%;
  white-space: nowrap;
}

.action-col {
  width: 7%;
  white-space: nowrap;
}

/* Custom styles for the Actions button */
.actions-btn-custom {
  padding: 0.1rem 0.25rem; /* Even smaller padding */
  font-size: 0.7rem;     /* Smaller font size */
  min-width: 60px;       /* Fixed smaller width */
  max-width: 70px;       /* Max width */
  border-radius: 0 !important; /* Ensure straight lines */
  margin: 0 auto; /* Center the button */
  height: 22px; /* Fixed height */
  line-height: 1; /* Ensure text is centered vertically */
}

/* Center the dropdown within the action column */
.action-col .dropdown {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Dropdown items styling */
.dropdown-item {
  border-radius: 0 !important;
  padding: 0.25rem 0.75rem !important; /* Smaller padding */
  font-size: 0.8rem !important;      /* Smaller font */
  height: 24px !important;           /* Fixed height */
  line-height: 1 !important;         /* Better vertical centering */
  display: flex !important;
  align-items: center !important;
}