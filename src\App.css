:root {
  --color-primary-light: #0082A3;
  --color-primary-dark: #164966;
  --color-background: #E3F8FF;
  --color-text: #1e293b;
  --color-text-light: #64748b;
  --color-white: #ffffff;
  --color-sidebar-bg: #067494;
  --color-sidebar-selected: #164966;
  --color-border: #0082A3;
  --color-table-th: #396A7D;
  --color-error: #ff4d4f;
  --color-error-light: #ff7875;
  --color-error-dark: #d9363e;
  --color-error-light-dark: #cc2e32;
  --color-button-color: #007292;
  --color-success: #28a745;
  --color-danger: #dc3545;
  --color-warning: #ffc107;
  --color-info: #17a2b8;

  /* Font Family */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Can<PERSON>ell, 'Open Sans', 'Helvetica Neue', sans-serif;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 2.5rem;
  --spacing-xxl: 3.5rem;
  --spacing-sidebar: 1.2rem;
  --spacing-custom-xs: 0.85rem;

  /* Border Radius */
  --border-radius-sm: 0.5rem;
  --border-radius-md: 0.75rem;
  --border-radius-lg: 1rem;
}

/* Global Styles */
body {
  margin: 0;
  font-family: var(--font-family);
  color: var(--color-text);
  background-color: var(--color-background);
  line-height: 1.5;
}

/* Reusable Utility Classes */
.text-center {
  text-align: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Links */
a {
  color: var(--color-primary-dark);
  text-decoration: none;
}

a:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}

/* Buttons */
button {
  font-family: var(--font-family);
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Inputs */
input,
select,
textarea {
  font-family: var(--font-family);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--color-primary-light);
  box-shadow: 0 0 0 3px rgba(0, 130, 163, 0.1);
}

/* Form Inputs with Theme Colors */
.form-input {
  width: 100%;
  padding: var(--spacing-xs);
  border: 1px solid var(--color-border) !important;
  border-radius: var(--border-radius-sm) !important;
  font-size: 0.875rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
  border-color: var(--color-primary-light);
  box-shadow: 0 0 0 3px rgba(0, 130, 163, 0.1);
}

/* Border Primary Utility */
.border-primary {
  border-color: var(--color-primary-light) !important;
}

.border-primary:focus {
  border-color: var(--color-primary-light) !important;
  box-shadow: 0 0 0 3px rgba(0, 130, 163, 0.1);
}

/* Buttons with Theme Colors */
.btn-primary {
  background-color: var(--color-sidebar-bg) !important;
  border-color: var(--color-primary-dark) !important;
  color: var(--color-white) !important;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--color-primary-light) !important;
  border-color: var(--color-primary-light) !important;
}

.btn-outline-primary {
  border-color: var(--color-primary-dark) !important;
  color: var(--color-primary-dark) !important;
  background-color: transparent;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
}

.btn-outline-primary:hover {
  background-color: var(--color-primary-dark) !important;
  color: var(--color-white) !important;
}

/* Form Labels */
.form-label {
  font-size: 0.875rem;
  margin-bottom: var(--spacing-xs);
  display: block;
  color: var(--color-primary-dark) !important;
}

/* Dashboard Layout */
.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--color-background);
}

.dashboard-content {
  flex: 1;
  margin-left: 280px;
  padding: var(--spacing-md);
  overflow-y: auto;
  margin-top: var(--spacing-xxl);
}

/* Card Styling */
.card {
  background: var(--color-white);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  background: var(--color-primary-dark);
  color: var(--color-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.card-header h5 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
}

.pagination button {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  border-radius: var(--border-radius-sm);
  background-color: var(--color-primary-dark);
  color: var(--color-white);
  cursor: pointer;
}

.pagination button.active {
  background-color: var(--color-primary-light);
}

.pagination button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

select {
  appearance: none;
  background-color: var(--color-white);
  border: 1px solid var(--color-border) !important;
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--color-text);
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

select:focus {
  outline: none;
  border-color: var(--color-primary-light);
  box-shadow: 0 0 0 3px rgba(0, 130, 163, 0.1);
}

/* Custom Select Arrow */
.select-wrapper {
  position: relative;
  width: 100%;
}

.select-wrapper::after {
  content: "▼";
  position: absolute;
  top: 50%;
  right: var(--spacing-sm);
  transform: translateY(-50%);
  pointer-events: none;
  color: var(--color-primary-dark);
  font-size: 0.75rem;
}

.search-input {
  width: 100%;
  max-width: auto;
  padding: var(--spacing-xs);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
}

.table-responsive {
  overflow-x: auto;
  width: 100%;
}

.error-message {
  color: #ff4d4f !important;
  margin-bottom: 16px !important;
  text-align: center;
}

.input-error {
  border-color: #ff4d4f !important;
}

.error-text {
  color: #ff4d4f;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
}

.info-message {
  color: #0c5460;
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.success-message {
  color: var(--color-primary-dark);
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  text-align: center;
}

/* Action Buttons */
.edit-button,
.delete-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  cursor: pointer;
}

.edit-button {
  background-color: var(--color-primary-light);
  color: var(--color-white);
  margin-right: var(--spacing-xs);
}

.edit-button:hover {
  background-color: #007292;
}

.delete-button {
  background-color: #ff4444;
  color: var(--color-white);
}

.delete-button:hover {
  background-color: #cc0000;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  width: 250px;
}

.date-picker {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 150px;
}

.filters-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  margin-bottom: 1.5rem;
}

.search-group {
  flex: 1;
}

.search-input {
  width: 100%;
}

.right-filters {
  display: flex;
  gap: 10px;
}

/* Download Button */
.download-button {
  padding: 0.4rem 0.75rem;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 0.8125rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  transition: transform 0.2s ease;
}

.download-button i {
  font-size: 0.75rem;
}

.download-button:hover {
  transform: scale(1.1);
}

.download-button {
  cursor: pointer;
  display: inline-block;
  transition: opacity 0.2s ease;
  color: var(--color-primary);

  &:hover {
    color: var(--color-primary-dark);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    color: var(--color-gray);
    pointer-events: none;

    &:hover {
      color: var(--color-gray);
    }
  }
}

.filter-group.search-group {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  /* Ensure it takes full width */
}

.search-input {
  width: 100%;
  padding-right: 30px;
  /* Make space for the clear button */
  /* Your existing search input styles */
}

.clear-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--color-text-light);
  cursor: pointer;
  font-size: 18px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  padding: 0;
  z-index: 2;
  /* Ensure it appears above the input */
}

.clear-search:hover {
  color: var(--color-error);
  background-color: var(--color-error-light);
}

.text-primary {
  color: var(--color-primary-light) !important;
}

.status-badge {
  &.processing {
    background-color: #ffc107;
    color: #000;
  }

  &.completed {
    background-color: #28a745;
    color: #fff;
  }

  &.failed {
    background-color: #dc3545;
    color: #fff;
  }
}

.progress-bar {
  font-size: 11px;
}

.download-template-button,
.submit-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-table-th);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  white-space: nowrap;
  font-size: var(--spacing-custom-xs);
}

.download-template-button:hover {
  background: var(--color-primary-light);
}

.submit-button:hover {
  background: var(--color-primary-light);
}

@media (max-width: 768px) {

  .edit-button,
  .delete-button {
    padding: var(--spacing-xs);
    font-size: 0.75rem;
  }
}

svg {
  cursor: pointer;
}

/* View Reports icon */
/* .dropdown-item.view-reports::before {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-image: url('https://img.icons8.com/ios-filled/50/0082A3/statistics.png');
  background-size: cover;
  background-repeat: no-repeat;
} */

/* View Credits icon */
/* .dropdown-item.view-credits::before {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-image: url('https://img.icons8.com/ios-filled/50/0082A3/money-bag.png');
  background-size: cover;
  background-repeat: no-repeat;
} */

/* Edit icon */
/* .dropdown-item.edit::before {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-image: url('https://img.icons8.com/ios-filled/50/0082A3/edit.png');
  background-size: cover;
  background-repeat: no-repeat;
} */

/* Delete icon */
/* .dropdown-item.delete::before {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-image: url('https://img.icons8.com/ios-filled/50/0082A3/delete.png');
  background-size: cover;
  background-repeat: no-repeat;
} */