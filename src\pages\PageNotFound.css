/* PageNotFound.css */
.page-not-found-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f8f9fa;
    padding: 2rem;
}

.page-not-found-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
    overflow: hidden;
}

.page-not-found-header {
    background-color: #164966;
    padding: 1.5rem;
    text-align: center;
}

.page-not-found-header h2 {
    margin: 0;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 600;
}

.page-not-found-body {
    padding: 2rem;
    text-align: center;
}

.error-icon {
    margin-bottom: 1.5rem;
}

.error-icon svg {
    stroke-width: 1.5;
}

.page-not-found-body h3 {
    color: #dc3545;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.error-message {
    color: #212529;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.error-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.home-link,
.back-button {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.home-link {
    background-color: #164966;
    color: #fff;
    border: none;
}

.home-link:hover {
    background-color: #1d5d82;
    color: #fff;
}

.back-button {
    background-color: #f8f9fa;
    color: #164966;
    border: 1px solid #164966;
    cursor: pointer;
}

.back-button:hover {
    background-color: #e9ecef;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .page-not-found-container {
        padding: 1rem;
    }

    .page-not-found-body {
        padding: 1.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .home-link,
    .back-button {
        width: 100%;
        justify-content: center;
    }
}