import React, { useState, useEffect } from "react";
import apiClient from "../../../core/config/api";

interface BulkEmailVerifierModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadSuccess: (response: any) => void;
}

interface LimitErrorResponse {
  error: string;
  message: string;
  daily_limit: number;
  remaining: number;
  attempted: number;
}

const BulkEmailVerifierModal: React.FC<BulkEmailVerifierModalProps> = ({
  isOpen,
  onClose,
  onUploadSuccess,
}) => {
  const [projectName, setProjectName] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [limitError, setLimitError] = useState<LimitErrorResponse | null>(null);

  useEffect(() => {
    if (!isOpen) {
      setProjectName("");
      setFile(null);
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
      setLimitError(null);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");
    setLimitError(null);

    try {
      if (!file) {
        throw new Error("Please select a file to upload");
      }

      const formData = new FormData();
      formData.append("project_name", projectName);
      formData.append("file", file);

      const response = await apiClient.post('/verifier-requests/bulk', formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      setSuccessMessage(response.data.message || "Bulk verification processing started");
      onUploadSuccess(response.data);

      setTimeout(() => {
        onClose();
      }, 5000);
    } catch (err: any) {
      console.error("Upload error:", err);
      const errorData = err.response?.data || {};

      // Handle different error structures
      if (errorData.error === "limit_exceeded") {
        // Direct limit error
        setLimitError({
          error: errorData.error,
          message: errorData.message,
          daily_limit: errorData.daily_limit,
          remaining: errorData.remaining,
          attempted: errorData.attempted
        });
      } else if (errorData.error === "http_error" && errorData.message?.includes("limit_exceeded")) {
        // Nested limit error in message string
        try {
          const startIdx = errorData.message.indexOf("{");
          const endIdx = errorData.message.lastIndexOf("}");
          if (startIdx !== -1 && endIdx !== -1) {
            const jsonStr = errorData.message.slice(startIdx, endIdx + 1);
            const validJsonStr = jsonStr.replace(/'/g, '"');
            const limitErrorDetails = JSON.parse(validJsonStr);
            setLimitError({
              error: limitErrorDetails.error,
              message: limitErrorDetails.message,
              daily_limit: limitErrorDetails.daily_limit,
              remaining: limitErrorDetails.remaining,
              attempted: limitErrorDetails.attempted
            });
          } else {
            setGeneralError(errorData.message);
          }
        } catch (parseError) {
          console.error("Failed to parse limit error:", parseError);
          setGeneralError(errorData.message);
        }
      } else if (errorData.details) { // Now accessible without type error
        // Validation errors
        const newErrors: Record<string, string> = {};
        errorData.details.forEach((error: any) => {
          if (error.loc && error.loc.length > 1) {
            const field = error.loc[error.loc.length - 1];
            if (field === "project_name") newErrors.projectName = error.msg;
            if (field === "file") newErrors.file = error.msg;
          } else {
            setGeneralError(error.msg || "An error occurred");
          }
        });
        setErrors(newErrors);
      } else {
        // Generic error
        setGeneralError(errorData.message || err.message || "Failed to upload file. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Bulk Email Verifier</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {generalError && (
            <div className="error-message">
              {generalError}
            </div>
          )}

          {limitError && (
            <div className="alert alert-warning">
              <div className="limit-error-message">
                <p>{limitError.message}</p>
                <ul className="limit-error-details">
                  <li>Daily limit: {limitError.daily_limit?.toLocaleString('en-US') || 'N/A'}</li>
                  <li>Remaining today: {limitError.remaining?.toLocaleString('en-US') || 'N/A'}</li>
                  <li>Attempted: {limitError.attempted?.toLocaleString('en-US') || 'N/A'}</li>
                </ul>
              </div>
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              {successMessage}
            </div>
          )}

          <div className="form-group">
            <label>Project Name</label>
            <input
              type="text"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              placeholder="Enter project name"
              className={errors.projectName ? "input-error" : ""}
              required
            />
            {errors.projectName && (
              <span className="error-text">{errors.projectName}</span>
            )}
          </div>

          <div className="form-group">
            <label>CSV File</label>
            <input
              type="file"
              onChange={(e) => setFile(e.target.files?.[0] || null)}
              accept=".csv"
              className={errors.file ? "input-error" : ""}
              required
            />
            <small className="text-muted">
              CSV file should contain a single column: email
            </small>
            {errors.file && (
              <span className="error-text">{errors.file}</span>
            )}
          </div>

          <div className="form-actions">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Uploading...
                </>
              ) : "Upload & Process"}
            </button>
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BulkEmailVerifierModal;