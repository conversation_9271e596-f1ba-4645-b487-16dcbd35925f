import React from "react";
import { PaginationProps } from "../types/models/auth";

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  onPerPageChange,
  perPage,
  disabled = false,
}) => {
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };
  const handlePerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onPerPageChange(Number(e.target.value));
  };
  const renderPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 3;
    const ellipsis = <span key="ellipsis-start" className="pagination-ellipsis">...</span>;

    // Always show the first page
    pages.push(
      <button
        key={1}
        onClick={() => handlePageChange(1)}
        className={currentPage === 1 ? "active" : ""}
        disabled={disabled}
      >
        1
      </button>
    );

    // Show ellipsis if current page is far from the start
    if (currentPage > maxVisiblePages + 1) {
      pages.push(ellipsis);
    }

    // Show pages around the current page
    for (
      let i = Math.max(2, currentPage - maxVisiblePages);
      i <= Math.min(totalPages - 1, currentPage + maxVisiblePages);
      i++
    ) {
      pages.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={currentPage === i ? "active" : ""}
          disabled={disabled}
        >
          {i}
        </button>
      );
    }

    // Show ellipsis if current page is far from the end
    if (currentPage < totalPages - maxVisiblePages) {
      pages.push(<span key="ellipsis-end" className="pagination-ellipsis">...</span>);
    }

    // Always show the last page
    if (totalPages > 1) {
      pages.push(
        <button
          key={totalPages}
          onClick={() => handlePageChange(totalPages)}
          className={currentPage === totalPages ? "active" : ""}
          disabled={disabled}
        >
          {totalPages}
        </button>
      );
    }

    return pages;
  };

  return (
    <div className="pagination">
      <button
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1 || disabled}
        className="pagination-button"
      >
        Previous
      </button>
      {renderPageNumbers()}
      <button
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages || disabled}
        className="pagination-button"
      >
        Next
      </button>
      <select
        value={perPage}
        onChange={handlePerPageChange}
        className="per-page-select"
        disabled={disabled}
      >
        <option value={10}>10 per page</option>
        <option value={25}>25 per page</option>
        <option value={50}>50 per page</option>
        <option value={100}>100 per page</option>
      </select>
    </div>
  );
};

export default Pagination;