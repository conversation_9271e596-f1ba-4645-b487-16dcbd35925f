.login-container {
  display: flex;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background);
  padding: var(--spacing-sm); /* Reduced padding */
}

.login-card {
  background: var(--color-white);
  padding: var(--spacing-lg); /* Reduced padding */
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-md); /* Reduced margin */
}

.login-title {
  font-size: 1.5rem; /* Reduced font size */
  font-weight: 600;
  color: var(--color-primary-dark);
  margin-bottom: var(--spacing-xs);
}

.login-subtitle {
  color: var(--color-text-light);
  font-size: 0.875rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm); /* Reduced gap */
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-primary-dark);
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.password-toggle {
  background: none;
  border: none;
  color: var(--color-primary-light);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0; /* Removed padding */
}

.password-toggle:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: var(--spacing-sm); 
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 0.875rem; 
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #4fa8d8 0%, #1f6091 100%);
  transform: translateY(-1px);
}

.login-link {
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-text-light);
  margin-top: var(--spacing-xs); /* Reduced margin */
}

.login-text {
  color: var(--color-primary-dark);
  font-weight: 500;
  text-decoration: none;
}

.login-text:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}