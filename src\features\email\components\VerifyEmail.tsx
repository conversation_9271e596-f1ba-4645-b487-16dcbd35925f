import { saveAs } from 'file-saver';
import React, { useEffect, useState, useRef } from 'react';
import { useSelector } from "react-redux";
import { RootState } from '../../../app/store';
import Pagination from '../../../components/Pagination';
import Sidebar from '../../../components/Sidebar';
import apiClient from '../../../core/config/api';
import Header from '../../../layouts/Header/components/Header';
import VerifyEmailModal from './VerifyEmailModal';

interface VerificationResult {
  history: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    status: string;
    request_type: string;
    created_at: string;
    updated_at: string;
  };
  details: {
    request_body: Array<{
      email?: string;
      domain: string;
      first_name?: string;
      last_name?: string;
    }>;
    results: Array<{
      email: string | null;
      status: string;
      message: string;
      smtp_response_code?: string;
    }>;
    progress: number;
    created_at: string;
    updated_at: string;
  };
}

const VerifyEmail: React.FC = () => {
  const roleId = useSelector((state: RootState) => state.auth.roleId);
  const [results, setResults] = useState<VerificationResult[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [dataPerPage, setDataPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [downloadingFiles, setDownloadingFiles] = useState<{ [key: string]: boolean }>({});
  const [totalCount, setTotalCount] = useState<number>(0);
  const [openActionMenuId, setOpenActionMenuId] = useState<number | null>(null);
  const [error, setError] = useState<{
    message: string,
    dailyLimit?: number,
    remaining?: number,
    attempted?: number
  } | null>(null);
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  const fetchSingleVerifierCount = async () => {
    try {
      const response = await apiClient.get("/verifier/count/single-verifier");
      setTotalCount(response.data.count);
    } catch (error) {
      console.error("Failed to fetch single verifier count:", error);
    }
  };

  const fetchVerificationResults = async () => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {
        page: currentPage,
        page_size: dataPerPage,
      };

      if (searchTerm && searchTerm.trim()) {
        params.email_contains = searchTerm.trim();
      }

      const response = await apiClient.get('/verifier-requests', { params });
      setResults(response.data.data);
      setTotalPages(response.data.total_pages);
    } catch (error: any) {
      console.error('Failed to fetch verification results:', error);

      // Handle limit exceeded error (400 status)
      if (error?.response?.status === 400 && error?.response?.data?.error === "limit_exceeded") {
        const limitError = error.response.data;
        setError({
          message: limitError.message,
          dailyLimit: limitError.daily_limit,
          remaining: limitError.remaining,
          attempted: limitError.attempted
        });
        return;
      }

      // Handle 422 validation errors
      if (error?.response?.status === 422) {
        const errorData = error.response.data;
        setError({
          message: errorData.message || "Validation error occurred"
        });
        return;
      }

      // Handle other types of errors
      setError({
        message: error?.response?.data?.message ||
          error?.message ||
          "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      fetchVerificationResults();
      fetchSingleVerifierCount();
    }, 500);

    setSearchTimeout(timeout);

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTerm, currentPage, dataPerPage]);

  useEffect(() => {
    fetchVerificationResults();
    fetchSingleVerifierCount();
  }, [currentPage, dataPerPage]);

  const getDisplayEmail = (result: VerificationResult) => {
    return result.details.request_body[0]?.email ||
      result.details.results[0]?.email ||
      'N/A';
  };

  const downloadEmailVerificationResults = async (id: number) => {
    const stateKey = `results-${id}`;

    if (downloadingFiles[stateKey]) {
      return;
    }

    setDownloadingFiles(prev => ({ ...prev, [stateKey]: true }));
    try {
      const response = await apiClient.get(`/verifier-requests/${id}/download-results`, {
        responseType: 'blob'
      });

      const filename = response.headers['content-disposition']
        ?.split('filename=')[1]
        ?.replace(/"/g, '') || `verification_results_${id}.csv`;

      saveAs(response.data, filename);
    } catch (error: any) {
      console.error('Failed to download results:', error);
      setError({
        message: error?.response?.data?.message ||
          "Failed to download verification results"
      });
    } finally {
      setDownloadingFiles(prev => ({ ...prev, [stateKey]: false }));
    }
  };

  return (
    <div className="dashboard-container">
      <Header />
      <Sidebar />
      <div className="dashboard-content">
        <div className="card shadow-sm border-0">
          <div className="card-header py-3" style={{ background: "#164966" }}>
            <h5 className="mb-0 text-white">
              <i className="fas fa-envelope me-2"></i>Email Verification{totalCount > 0 && <span>({totalCount})</span>}
            </h5>
          </div>
          <div className="card-body p-3">
            {error && (
              <div className="alert alert-danger mb-3">
                <div className="d-flex align-items-center">
                  <i className="fas fa-exclamation-circle me-2"></i>
                  <strong>{error.message}</strong>
                </div>
                {error.dailyLimit !== undefined && (
                  <div className="mt-2">
                    <small>
                      Limit: {error.remaining}/{error.dailyLimit} |
                      Attempted: {error.attempted}
                    </small>
                  </div>
                )}
              </div>
            )}

            <div className="filters-container">
              <div className="filter-group search-group">
                <input
                  type="text"
                  placeholder="Search by email, project name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                  style={{ paddingRight: '30px' }}
                />
                {searchTerm && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchTerm('')}
                    aria-label="Clear search"
                  >
                    &times;
                  </button>
                )}
              </div>

              <div className="filter-group right-filters">
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="verify-button"
                  disabled={error?.message?.includes('daily limit')}
                >
                  Verify Email
                </button>
              </div>
            </div>

            <div className="table-responsive">
              <table className="results-table">
                <thead>
                  <tr>
                    <th className="email-col">Email</th>
                    {roleId === 1 && (
                      <th className="uploaded-col">Uploaded By</th>
                    )}
                    <th className="status-col">Status</th>
                    <th className="result-col">Result</th>
                    <th className="progress-col">Progress</th>
                    <th className="date-col">Created At</th>
                    <th className="action-col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={roleId === 1 ? 7 : 6} className="text-center">
                        <div className="spinner-border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : results.length === 0 ? (
                    <tr>
                      <td colSpan={roleId === 1 ? 7 : 6} className="text-center">
                        {searchTerm ?
                          'No results found for your search' :
                          'No verification results found'}
                      </td>
                    </tr>
                  ) : (
                    results.map((result, index) => (
                      <tr key={index}>
                        <td className="email-col">{getDisplayEmail(result)}</td>
                        {roleId === 1 && (
                          <td className="uploaded-col">
                            {(result.history?.first_name || result.history?.last_name)
                              ? `${result.history.first_name || ''} ${result.history.last_name || ''}`.trim()
                              : 'N/A'}
                          </td>
                        )}
                        <td className="status-col">
                          {result?.history?.status || 'N/A'}
                        </td>
                        <td className="result-col">{result.details.results[0]?.status || 'N/A'}</td>
                        <td className="progress-col">
                          <div className="progress">
                            <div
                              className={`progress-bar ${getProgressBarColor(result.details?.progress || 0)}`}
                              style={{ width: `${result.details?.progress || 0}%` }}
                            >
                              {result.details?.progress || 0}%
                            </div>
                          </div>
                        </td>
                        <td className="date-col">{new Date(result.history.created_at).toLocaleString()}</td>
                        <td className="action-col">
                          <div
                            onClick={() => {
                              const stateKey = `results-${result.history.id}`;
                              // Only start download if result is completed AND not already downloading
                              if (result.history.status === 'completed' && !downloadingFiles[stateKey]) {
                                downloadEmailVerificationResults(result.history.id);
                              }
                            }}
                            className={`download-button ${result.history.status !== 'completed' || downloadingFiles[`results-${result.history.id}`] ? 'disabled' : ''}`}
                            title={
                              downloadingFiles[`results-${result.history.id}`]
                                ? 'Downloading...'
                                : result.history.status === 'completed'
                                  ? 'Download Results'
                                  : `Results not available (Status: ${result.history.status})`
                            }
                          >
                            {downloadingFiles[`results-${result.history.id}`] ? (
                              <div className="spinner-border spinner-border-sm" role="status">
                                <span className="visually-hidden">Downloading...</span>
                              </div>
                            ) : (
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                              >
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                              </svg>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              onPerPageChange={setDataPerPage}
              perPage={dataPerPage}
            />

            <VerifyEmailModal
              isOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              onVerifySuccess={fetchVerificationResults}
              dailyLimitExceeded={error?.message?.includes('daily limit')}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

function getProgressBarColor(progress: number): string {
  if (progress === 100) return 'bg-success';
  if (progress > 70) return 'bg-info';
  if (progress > 30) return '';
  return 'bg-warning';
}

export default VerifyEmail;