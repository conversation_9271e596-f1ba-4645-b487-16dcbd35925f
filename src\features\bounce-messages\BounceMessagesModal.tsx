import axios from "axios";
import React, { useEffect, useRef, useState } from "react";

interface BounceMessagesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadSuccess: (response: any) => void;
}

const BounceMessagesModal: React.FC<BounceMessagesModalProps> = ({
  isOpen,
  onClose,
  onUploadSuccess,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [uploadResult, setUploadResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!isOpen) {
      setFile(null);
      setErrors({});
      setGeneralError("");
      setSuccessMessage("");
      setUploadResult(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  }, [isOpen]);

  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('File read error'));
      reader.readAsText(file);
    });
  };

  const validateCSVFile = async (file: File): Promise<boolean> => {
    if (!file.name.endsWith('.csv')) {
      setErrors(prev => ({ ...prev, file: "Only CSV files are allowed" }));
      return false;
    }

    return new Promise((resolve) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (!content) {
          setErrors(prev => ({ ...prev, file: "Could not read file content" }));
          resolve(false);
          return;
        }

        // Handle BOM character if present at the start of the file
        let cleanContent = content;
        if (content.startsWith('\ufeff')) {
          cleanContent = content.replace('\ufeff', '');
        }

        const lines = cleanContent.split('\n');
        if (lines.length === 0) {
          setErrors(prev => ({ ...prev, file: "CSV file is empty" }));
          resolve(false);
          return;
        }

        // Check header row
        const header = lines[0].trim().toLowerCase();
        const requiredColumns = ['category', 'message'];
        
        // Check if both required columns exist in the header
        const headerColumns = header.split(',').map(col => col.trim().toLowerCase());
        
        const missingColumns = requiredColumns.filter(
          col => !headerColumns.includes(col)
        );
        
        if (missingColumns.length > 0) {
          setErrors(prev => ({ 
            ...prev, 
            file: `CSV file must contain the columns: ${missingColumns.join(', ')}` 
          }));
          resolve(false);
          return;
        }

        // If there's only a header row and no data
        if (lines.length < 2 || (lines.length === 2 && lines[1].trim() === '')) {
          setErrors(prev => ({ ...prev, file: "CSV file contains no data rows" }));
          resolve(false);
          return;
        }

        // Basic validation for empty required fields
        let hasValidationErrors = false;
        const categoryIndex = headerColumns.indexOf('category');
        const messageIndex = headerColumns.indexOf('message');

        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue; // Skip empty lines
          
          const values = line.split(',');
          
          // Handle quoted values (simple implementation)
          const processedValues = values.map(val => val.trim().replace(/^"|"$/g, ''));
          
          if (processedValues[categoryIndex]?.trim() === '') {
            setErrors(prev => ({ 
              ...prev, 
              file: `Row ${i}: Category is required and cannot be empty` 
            }));
            hasValidationErrors = true;
            break;
          }
          
          if (processedValues[messageIndex]?.trim() === '') {
            setErrors(prev => ({ 
              ...prev, 
              file: `Row ${i}: Message is required and cannot be empty` 
            }));
            hasValidationErrors = true;
            break;
          }
        }

        resolve(!hasValidationErrors);
      };

      reader.onerror = () => {
        setErrors(prev => ({ ...prev, file: "Error reading file" }));
        resolve(false);
      };

      reader.readAsText(file);
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});
    setGeneralError("");
    setSuccessMessage("");
    setUploadResult(null);

    try {
      if (!file) {
        setErrors({ file: "Please select a file to upload" });
        throw new Error("Please select a file to upload");
      }

      const isValid = await validateCSVFile(file);
      if (!isValid) {
        throw new Error("CSV validation failed");
      }

      const fileContent = await readFileAsText(file);
      let cleanContent = fileContent;
      
      if (fileContent.startsWith('\ufeff')) {
        cleanContent = fileContent.replace('\ufeff', '');
      }
      
      let fileToUpload = file;
      if (cleanContent !== fileContent) {
        const blob = new Blob([cleanContent], { type: 'text/csv' });
        fileToUpload = new File([blob], file.name, { type: 'text/csv' });
      }

      const formData = new FormData();
      formData.append("file", fileToUpload);
      
      try {
        const baseURL = import.meta.env.VITE_API_URL;
        const token = localStorage.getItem('accessToken')?.replace(/^"(.*)"$/, '$1');
        
        const response = await axios.post(`${baseURL}bounce-messages/bulk`, formData, {
          headers: {
            'Authorization': token ? `Bearer ${token}` : '',
          },
        });
        
        setUploadResult(response.data);
        
        if (response.data.failed > 0 && response.data.errors?.length > 0) {
          setGeneralError(`Upload completed with ${response.data.failed} errors.`);
        } else {
          setSuccessMessage(`Successfully uploaded ${response.data.successful} bounce messages.`);
          onUploadSuccess(response.data);
        }
      } catch (axiosError) {
        throw axiosError;
      }
    } catch (err: any) {
      if (Object.keys(errors).length === 0) {
        if (err.response?.data?.message) {
          setGeneralError(err.response.data.message);
        }
        else if (err.response?.data?.detail) {
          const apiErrors = err.response.data.detail;

          if (Array.isArray(apiErrors)) {
            const newErrors: Record<string, string> = {};
            apiErrors.forEach((error: any) => {
              if (error.loc && error.loc.length > 1) {
                const field = error.loc[error.loc.length - 1];
                if (field === "file") newErrors.file = error.msg;
              } else {
                setGeneralError(error.msg || "An error occurred");
              }
            });
            setErrors(newErrors);
          } else {
            setGeneralError(apiErrors.message || "Failed to upload file");
          }
        }
        else if (err.message !== "CSV validation failed") {
          setGeneralError(err.message || "Failed to upload file. Please try again.");
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const renderUploadResult = () => {
    if (!uploadResult) return null;

    return (
      <div className="upload-result-container mt-3">
        <div className="summary-section mb-3 p-3 bg-light rounded">
          <h5>Upload Summary</h5>
          <div className="d-flex flex-wrap gap-4">
            <div>
              <strong>Total Processed:</strong> {uploadResult.total_processed}
            </div>
            <div className="text-success">
              <strong>Successful:</strong> {uploadResult.successful}
            </div>
            <div className="text-danger">
              <strong>Failed:</strong> {uploadResult.failed}
            </div>
            <div className="text-warning">
              <strong>Skipped:</strong> {uploadResult.skipped}
            </div>
          </div>
        </div>

        {(uploadResult.errors?.length > 0 || uploadResult.skipped_messages?.length > 0) && (
          <div className="scrollable-result-section" style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {uploadResult.errors?.length > 0 && (
              <div className="errors-section mb-4">
                <h5 className="text-danger">Error Details</h5>
                <div className="table-responsive">
                  <table className="table table-sm table-bordered">
                    <thead>
                      <tr>
                        <th>Row</th>
                        <th>Message</th>
                        <th>Error</th>
                      </tr>
                    </thead>
                    <tbody>
                      {uploadResult.errors.map((error: any, index: number) => (
                        <tr key={`error-${index}`}>
                          <td>{error.row}</td>
                          <td className="text-truncate" style={{ maxWidth: '200px' }} title={error.message}>
                            {error.message}
                          </td>
                          <td className="text-truncate" style={{ maxWidth: '300px' }} title={error.error}>
                            {error.error.split('\n')[0]}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {uploadResult.skipped_messages?.length > 0 && (
              <div className="skipped-section">
                <h5 className="text-warning">Skipped Messages</h5>
                <div className="table-responsive">
                  <table className="table table-sm table-bordered">
                    <thead>
                      <tr>
                        <th>Row</th>
                        <th>Message</th>
                        <th>Reason</th>
                      </tr>
                    </thead>
                    <tbody>
                      {uploadResult.skipped_messages.map((skipped: any, index: number) => (
                        <tr key={`skipped-${index}`}>
                          <td>{skipped.row}</td>
                          <td className="text-truncate" style={{ maxWidth: '300px' }} title={skipped.original_message}>
                            {skipped.original_message}
                          </td>
                          <td>{skipped.reason}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content" style={{ maxWidth: '800px', width: '90%' }}>
        <div className="modal-header">
          <h3>Bulk Upload Bounce Messages</h3>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            disabled={loading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {generalError && (
              <div className="error-message">
                {generalError}
              </div>
            )}

            {successMessage && (
              <div className="success-message">
                {successMessage}
              </div>
            )}

            <div className="form-group mb-3">
              <label className="form-label">CSV File</label>
              <input
                type="file"
                onChange={(e) => setFile(e.target.files?.[0] || null)}
                accept=".csv"
                className={`form-control ${errors.file ? "is-invalid" : ""}`}
                required
                ref={fileInputRef}
              />
              <small className="form-text text-muted">
                CSV file must contain columns: category, message (status is optional and defaults to active)
              </small>
              {errors.file && (
                <div className="invalid-feedback">{errors.file}</div>
              )}
            </div>

            {renderUploadResult()}
          </div>

          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Close
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Uploading...
                </>
              ) : "Upload"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BounceMessagesModal;